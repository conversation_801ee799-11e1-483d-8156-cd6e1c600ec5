import os
import shutil
import subprocess
import sys
import tkinter as tk
from datetime import datetime
from tkinter import messagebox, filedialog

import pandas as pd
from PyQt5.QtCore import QTimer, pyqtSignal, Qt
from PyQt5.QtGui import QFont
from PyQt5.QtWidgets import (
    QWidget,
    QVBoxLayout,
    QLineEdit,
    QListWidget,
    QButtonGroup,
    QApplication,
    QScrollArea,
    QPushButton,
    QTextEdit,
    QComboBox,
    QListWidgetItem,
    QRadioButton,
    QHBoxLayout,
    QLabel,
    QInputDialog,
)

from god import get_dict_from_df


def update_df_from_dict(df, file_csv, values, key):
    if key not in df.index:
        df.loc[key] = values
        df.to_csv(
            f"/home/<USER>/Dropbox/hnd/csv_source/{file_csv}",
            index_label=df.index.name,
        )
    else:
        df_update = pd.DataFrame([values], index=[key])  # cập nhật dataframe df2
        df.update(df_update)
        df.to_csv(
            f"/home/<USER>/Dropbox/hnd/csv_source/{file_csv}",
            index_label=df.index.name,
        )


def get_dict_from_df(df, index_val):
    df.fillna("", inplace=True)
    try:
        selected_values = df.loc[df.index == index_val].tail(1)
        return selected_values.to_dict(orient="records")[0]
    except:
        empty_dict = dict.fromkeys(df.columns, "")
        return empty_dict


def one_index(
    df,
    list_include,
    index_val,
    index_col,
    dict_radio,
    dict_combo,
    dict_widget,
    list_upper,
    list_title,
    dict_default,
    multi,
    list_ngay,
    list_phone,
    tieude,
    func,
    x,
    y,
    x1,
    y1,
):
    values_dict = get_dict_from_df(df, index_val)
    list_include = sorted(list_include, key=lambda x: list(df.columns).index(x))

    # NOTE: XEM CLASS CHINH VA press_enter

    class ExcelInputApp(QWidget):
        def __init__(self):
            super().__init__()
            self.setup_init()
            self.inputs = {}
            self.first_input = None  # Biến để lưu trữ trường nhập liệu đầu tiên

            self.add_all_items()
            self.add_submit_button()
            self.add_scroll_area()
            self.final_layout = QVBoxLayout(self)
            self.final_layout.addWidget(self.scroll_area)
            self.set_global_font()
            QTimer.singleShot(500, self.forcus_first_input)

        def add_all_items(self):
            for col in list_include:
                row_layout = self.add_all_label(col)
                if col in dict_radio:
                    self.add_button_group(col, row_layout)
                elif col in dict_widget:
                    self.add_list_widgets(col, row_layout)
                elif col in dict_combo:
                    self.add_combo_item(col, row_layout)
                elif col in multi:
                    self.add_multi_item(col, row_layout)
                else:  # TODO 1. ưu tiên khi col có giá trị từ dict get từ df, 2. nếu ko check xem có giá trị trong default values không
                    self.add_line_edits(col, row_layout)
                self.main_layout.addLayout(row_layout)

        def add_line_edits(self, col, row_layout):
            line_edit = QLineEdit(self)
            line_edit.setStyleSheet("QLineEdit { color: white; }")
            # Thiết lập giá trị mặc định nếu có
            input_widget = self.add_line_edit_item(col, line_edit)
            self.set_first_lineedit_active(col, input_widget)
            row_layout.addWidget(input_widget)

        def add_list_widgets(self, col, row_layout):
            list_widget = QListWidget(self)
            self.set_height_and_font_listwidget(col, list_widget)
            for option in dict_widget[col]:
                self.add_listwidget_item(list_widget, option)
            self.inputs[col] = list_widget
            row_layout.addWidget(list_widget)

        def add_button_group(self, col, row_layout):
            button_group = QButtonGroup(self)
            for index, option in enumerate(dict_radio[col]):
                radio_button = self.add_radio_button(button_group, option, row_layout)
                self.set_first_radio_true(index, radio_button)
            self.inputs[col] = button_group

        def set_global_font(self):
            self.font = QFont("DejaVu", 15)
            QApplication.setFont(self.font)

        def add_scroll_area(self):
            self.scroll_area = QScrollArea(self)
            self.scroll_area.setWidgetResizable(True)
            self.scroll_area.setWidget(self.main_widget)

        def add_submit_button(self):
            self.submit_button = QPushButton("Submit", self)
            self.submit_button.clicked.connect(self.press_enter)
            self.main_layout.addWidget(self.submit_button)

        def set_first_lineedit_active(self, col, input_widget):
            if self.first_input is None:
                self.first_input = input_widget
            self.inputs[col] = input_widget

        def add_line_edit_item(self, col, line_edit):
            if values_dict[col] != "":
                line_edit.setText(values_dict[col])
            elif dict_default.get(col, "") != "":
                # TODO TRICK, nếu có thì trả về value, nếu ko thì trả về giá trị rỗng,hoặc nếu values là rỗng thì cũng cho giá trị check
                line_edit.setText(str(dict_default[col]))
            input_widget = line_edit
            return input_widget

        def add_multi_item(self, col, row_layout):
            text_edit = QTextEdit(self)
            text_edit.setWordWrapMode(True)  # Bật chế độ wrap text
            input_widget = text_edit
            row_layout.addWidget(input_widget)
            self.inputs[col] = text_edit

        def add_combo_item(self, col, row_layout):
            combo_box = QComboBox(self)
            # combo_box.setEditable(True)
            # Lấy danh sách các lựa chọn cho combobox từ list_combo dựa trên cột hiện tại
            options = dict_combo[col]
            # Thêm các lựa chọn vào combobox
            for option in options:
                combo_box.addItem(option)
            row_layout.addWidget(combo_box)
            self.inputs[col] = combo_box

        def add_listwidget_item(self, list_widget, option):
            list_item = QListWidgetItem(option)
            list_widget.addItem(list_item)

        def set_height_and_font_listwidget(self, col, list_widget):
            list_widget.setMinimumHeight(len(dict_widget[col]) * 25)  # Đúng
            font = QFont()
            font.setPointSize(
                15
            )  # Bạn có thể thay đổi số này để tăng/giảm kích thước font
            list_widget.setFont(font)

        def set_first_radio_true(self, index, radio_button):
            if index == 0:  # Kiểm tra nếu là phần tử đầu tiên trong list
                radio_button.setChecked(True)  # Đặt radio button này được chọn mặc định

        def add_radio_button(self, button_group, option, row_layout):
            radio_button = QRadioButton(option)
            button_group.addButton(radio_button)
            row_layout.addWidget(radio_button)
            return radio_button

        def add_all_label(self, col):
            row_layout = QHBoxLayout()
            label = QLabel(col)
            row_layout.addWidget(label)
            return row_layout

        def setup_init(self):
            self.setWindowTitle(tieude)
            self.completed = pyqtSignal()
            # Tạo widget chính và layout chính
            self.main_widget = QWidget()
            self.setWindowFlags(self.windowFlags() | Qt.WindowStaysOnTopHint)
            self.main_layout = QVBoxLayout(self.main_widget)

        def forcus_first_input(self):
            # Đảm bảo trường nhập liệu đầu tiên được focus trước khi thực hiện tự động nhấn phím
            self.first_input.setFocus()

        def keyPressEvent(self, event):
            if event.key() in (Qt.Key_Return, Qt.Key_Enter):
                self.press_enter()

        def press_enter(self):
            data_dict = {}
            self.get_values_from_pyqt(data_dict)
            self.convert_phone_upper_title_ngay(data_dict)
            self.set_index_to_indexval(data_dict)
            self.hide()
            func(data_dict)
            show_message("THÔNG BÁO", "ĐÃ HOÀN THÀNH")
            QApplication.quit()

        def set_index_to_indexval(self, data_dict):
            data_dict[index_col] = index_val

        def get_values_from_pyqt(self, data_dict):
            for col in list_include:
                widget = self.inputs[col]
                if isinstance(widget, QLineEdit):
                    # Đối với QLineEdit, lấy trực tiếp giá trị text
                    data_dict[col] = widget.text()
                elif isinstance(widget, QButtonGroup):
                    # Đối với QButtonGroup (tức là nhóm các QRadioButton), lấy nút được chọn
                    selected_button = widget.checkedButton()  # Lấy nút được chọn
                    if (
                        selected_button is not None
                    ):  # Kiểm tra xem có nút nào được chọn không
                        data_dict[col] = (
                            selected_button.text()
                        )  # Lấy text của nút được chọn
                    else:
                        data_dict[col] = None
                elif isinstance(widget, QTextEdit):
                    # Đối với QTextEdit, lấy trực tiếp nội dung văn bản
                    data_dict[col] = widget.toPlainText()
                elif isinstance(widget, QListWidget):
                    # Đối với QListWidget, lấy tất cả các mục được chọn
                    selected_items = widget.selectedItems()
                    if selected_items:  # Kiểm tra xem có mục nào được chọn không
                        # Lưu trữ tất cả các giá trị được chọn vào data_dict
                        data_dict[col] = selected_items[0].text()
                    else:
                        data_dict[col] = None
                elif isinstance(widget, QComboBox):
                    # Lấy text của lựa chọn hiện tại từ QComboBox
                    selected_text = widget.currentText()
                    # Lưu giá trị vào từ điển, sử dụng tên cột làm khóa
                    data_dict[col] = selected_text if selected_text != "" else None

        def convert_phone_upper_title_ngay(self, data_dict):
            for key, value in data_dict.items():
                if key in list_ngay:
                    data_dict[key] = convert_ngay(value)
                elif key in list_phone:
                    data_dict[key] = phone_format(value)
                elif key in list_upper:
                    data_dict[key] = value.upper()
                elif key in list_title:
                    data_dict[key] = value.title()

    app = QApplication(sys.argv)
    window = ExcelInputApp()
    window.setStyleSheet(
        """
            QLabel {
                color: teal; /* Đặt màu chữ cho QLabel */
                font-weight: bold; /* Tăng độ đậm của chữ */
            }
            QLineEdit {
                border: 0.5px solid green; /* Thêm đường viền với màu sắc và độ dày cụ thể */
                padding: 5px; /* Thêm padding để văn bản không sát viền */
                background-color: #521073; /* Đặt màu nền cho QLineEdit */
            }
            QTextEdit {
                    border: 0.5px solid green; /* Thêm đường viền với màu sắc và độ dày cụ thể */
                    padding: 5px; /* Thêm padding để văn bản không sát viền */
                    background-color: #521073; /* Đặt màu nền cho QLineEdit */
                    }
            QPushButton {
                    background-color: #c90076; /* Đặt màu nền cho QPushButton */
                    padding: 5px; /* Thêm padding để nút không sát viền */
                    border: 0.5px solid green; /* Thêm đường viền với màu sắc và độ dày cụ thể */
                    font-weight: bold; /* Tăng độ đậm của chữ */
                    }
        """
    )
    window.resize(x1, y1)
    window.move(x, y)
    # Có thể điều chỉnh kích thước cửa sổ tại đây
    window.show()
    # window.setWindowFlag(Qt.WindowType.WindowStaysOnTopHint)
    sys.exit(app.exec_())


def get_df_so_gphn():
    list_index = df_last_50.index.to_list()
    for index, item in enumerate(list_index):
        list_index[index] = int(list_index[index].replace("/VP-GPHN", ""))
    max_so_gphn = max(list_index)
    return str(max_so_gphn + 1).zfill(6)


def get_excel_values(df, index_val):
    df.fillna("", inplace=True)
    try:
        selected_values = df.loc[df.index == index_val].tail(1)
        return selected_values.to_dict(orient="records")[0], df
    except:
        empty_dict = dict.fromkeys(df.columns, "")
        return empty_dict, df


def input_dialog(title, question, default_text, x_position, y_position, width, height):
    app = QApplication.instance()
    if app is None:
        app = QApplication([])
    # Tạo một hộp thoại nhập liệu
    input_string = QInputDialog()
    input_string.setWindowTitle(title)
    input_string.setLabelText(question)
    input_string.setTextValue(default_text)

    # Thiết lập thuộc tính "always on top"
    input_string.setWindowFlag(Qt.WindowType.WindowStaysOnTopHint)

    # Đặt kích thước cửa sổ
    input_string.setFixedSize(width, height)

    # Đặt font
    font = QFont()
    font.setPointSize(16)  # Bạn có thể thay đổi giá trị này để thay đổi kích thước font
    input_string.setFont(font)

    # Di chuyển hộp thoại tới vị trí chỉ định
    input_string.move(x_position, y_position)

    # Hiển thị hộp thoại và lấy giá trị trả về
    ok = input_string.exec()

    if ok == QInputDialog.DialogCode.Accepted:
        # Trả về giá trị nhập vào nếu người dùng bấm "OK"
        return input_string.textValue()
    else:
        # Trả về None nếu người dùng bấm "Cancel"
        return None


def show_message(WINDOWS_LABEL, MESSAGE):
    # Tạo một cửa sổ Tkinter đơn giản
    root = tk.Tk()
    root.withdraw()  # Ẩn cửa sổ chính

    # Tạo hộp thoại thông báo
    messagebox_root = tk.Toplevel(root)
    messagebox_root.withdraw()
    messagebox_root.attributes("-topmost", True)
    messagebox.showinfo(WINDOWS_LABEL, MESSAGE, parent=messagebox_root)

    messagebox_root.destroy()  # Đóng cửa sổ messagebox
    root.destroy()


def convert_ngay(ngay):
    try:
        if len(ngay) > 1:
            if "/" not in ngay:
                if str(ngay)[2:4] in ["01", "02", "10", "11", "12"]:
                    a = str(ngay)[2:4]
                else:
                    a = str(ngay)[3:4]
                ngay = str(ngay)[:2] + "/" + a + "/" + str(ngay)[-4:]
        return ngay
    except:
        print(f"có lỗi ở giá trị ngày {ngay}")


def phone_format(n):
    if len(n) > 5:
        n = n.replace(".", "")
        n = f"{n[:3]}.{n[3:6]}.{n[-4:]}"
    return n


def compile_latex(text, ten_file_pdf):
    f = open("mylatex.tex", "w", encoding="utf-8")
    f.write(text)
    f.close()
    try:
        subprocess.run(["pdflatex", "mylatex.tex"])
    except Exception as e:
        print(e)
        pass
    # shutil.copy("mylatex.pdf", rf"C:\Users\<USER>\Desktop\{ten_file_pdf}.pdf")
    # subprocess.Popen(rf"C:\Users\<USER>\Desktop\{ten_file_pdf}.pdf", shell=True)


def get_current_date():
    now = datetime.now()
    ngay = str(now.day).zfill(2)
    thang = now.month
    if thang < 3:
        thang = str(thang).zfill(2)
    nam = now.year
    to_day = f"{ngay}/{thang}/{nam}"
    return ngay, thang, nam, to_day


def is_valid_gphn():
    return len(so_gphn) != 6 or int(so_gphn) > int(default_so_gphn) + 1


def not_vp_in_gphn():
    return "VP" not in so_gphn


if __name__ == "__main__":
    os.chdir("/home/<USER>/Dropbox/hnd/csv_source")
    # os.chdir(r"D:\Compile")
    path_excel = "GIẤY PHÉP HÀNH NGHỀ KBCB.xlsx"
    index_col = "SỐ GPHN"
    df_gphn_id_sogp = pd.read_excel(path_excel, dtype=str, index_col=index_col)
    df_last_50 = df_gphn_id_sogp.tail(50)
    last_ngay = df_gphn_id_sogp["NGÀY CẤP GPHN"].iloc[-1]
    default_so_gphn = get_df_so_gphn()

    so_gphn = input_dialog(
        "NHẬP",
        "NHẬP SỐ GPHN (CHỈ NHẬP SỐ, KHÔNG NHẬP ĐUÔI)",
        default_so_gphn,
        800,
        500,
        400,
        400,
    )
    if is_valid_gphn():
        show_message("LỖI", "NHẬP SAI SỐ GPHN")
        sys.exit()
    if not_vp_in_gphn():
        so_gphn += "/VP-GPHN"
    else:
        show_message("LỖI", "KHÔNG NHẬP ĐUÔI")
        sys.exit()

    list_upper = ["HỌ VÀ TÊN"]
    multiline = ""
    list_ngay = ["NGÀY CẤP GPHN", "NGÀY CẤP", "ngay sinh"]
    dict_combo = {}
    dict_widget = {}
    list_phone = []
    list_title = []
    dict_df = {
        "NƠI CẤP": "Cục Cảnh sát Quản lý hành chính về trật tự xã hội",
        "gioi tinh": "0",
        "QUỐC TỊCH": "Việt Nam",
        "NGÀY CẤP GPHN": last_ngay,
    }
    list_exclude = [
        "ma ho so",
        "NGÀY",
        "thang",
        "nam",
        "ngay han",
        "SỐ GPHN CŨ",
        "pham vi hanh nghe cu",
        "NGÀY GPHN CŨ",
        "loai cap",
    ]
    # listcol = {'loai cap': ['Cấp mới', 'Cấp lại', 'Cấp đổi', 'Cấp bổ sung'],
    #            'CHỨC DANH CHUYÊN MÔN': ['Bác sĩ', 'Điều dưỡng', 'Kỹ thuật viên']}
    dict_radio = {
        "CHỨC DANH CHUYÊN MÔN": [
            "Bác sỹ",
            "Y sỹ",
            "Điều dưỡng",
            "Kỹ thuật y",
            "Hộ sinh",
            "Lương y",
        ]
    }
    list_include = [
        col for col in df_gphn_id_sogp.columns.tolist() if col not in list_exclude
    ]

    def funcs(values):
        values["NGÀY"], values["thang"], values["nam"] = values["NGÀY CẤP GPHN"].split(
            "/"
        )
        values["ngay han"] = (
            f"{values['NGÀY']}/{values['thang']}/{int(values['nam']) + 5}"
        )
        if values["CCCD"] != "":
            loai_giay_to = "Thẻ căn cước công dân"
            so_giay_to = values["CCCD"]
        elif values["SỐ ĐỊNH DANH CÁ NHÂN"] != "":
            loai_giay_to = "Số định danh cá nhân"
            so_giay_to = values["SỐ ĐỊNH DANH CÁ NHÂN"]
        else:
            loai_giay_to = "Hộ chiếu"
            so_giay_to = values["SỐ HỘ CHIẾU"]
        file_path = filedialog.askopenfilename()
        if file_path:
            # shutil.copy(file_path, fr"D:\Compile\anh\{os.path.basename(file_path)}")
            shutil.copy(file_path, f"/home/<USER>/{os.path.basename(file_path)}")
            # values['ma ho so']=file_path.split('--')[1]
            values["ma ho so"] = f"{values['HỌ VÀ TÊN']}-{values['CCCD']}"
        else:
            sys.exit()
        anhcc = os.path.splitext(os.path.basename(file_path))[0]
        root = tk.Tk()
        root.withdraw()

        text = rf"""
        \documentclass{{article}}
        \usepackage[utf8]{{inputenc}}
        \usepackage[T5]{{fontenc}}
        \nonstopmode
        \usepackage{{graphicx}}
        \usepackage[a4paper,hmargin=2cm,top=2cm,bottom=1cm]{{geometry}}
        \usepackage[fontsize=14pt]{{scrextend}}
        \usepackage{{times}}
        \usepackage{{tikz}}
        \usepackage{{setspace}}
        \usepackage{{indentfirst}}
        \usepackage{{eso-pic}}
        \usepackage{{microtype}}
        \usepackage{{ulem}}
        \usepackage{{parskip}}
        \setlength{{\parskip}}{{0pt}}
        \usepackage{{tabularray}}
        \UseTblrLibrary{{varwidth}}
        \usepackage{{ulem}}
        \renewcommand{{\ULdepth}}{{5pt}}
        \renewcommand{{\ULthickness}}{{0.7pt}}
        \setlength{{\parindent}}{{0pt}}
        \usepackage{{transparent}}
        \begin{{document}}

        \pagestyle{{empty}}
        \AddToShipoutPictureBG{{\includegraphics[width=\paperwidth, height=\paperheight]{{anh/KHUNG KCB.png}}}}
        \setstretch{{1}}
        \begin{{minipage}}{{1.05\textwidth}}
        \begin{{tblr}}{{width=1.05\linewidth,
                colspec={{X[0.9,c] X[1.9,c]}},
                colsep=0pt,
                rowsep=0pt,
                row{{2}} = {{font=\bfseries}}}}
        \fontsize{{13pt}}{{0pt}}\selectfont UBND TỈNH VĨNH PHÚC & \fontsize{{13pt}}{{0pt}}\selectfont \textbf{{CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM}}\\
        {{\fontsize{{13pt}}{{0pt}}\selectfont SỞ Y TẾ\\[3pt]
                \rule[0.6\baselineskip]{{.14\linewidth}}{{.7pt}}}} & \fontsize{{14pt}}{{0pt}}\selectfont \uline{{Độc lập - Tự do - Hạnh phúc}}\\[6pt]
        Số:  {{\color{{red}}\textbf{{{so_gphn.replace('/VP-GPHN', '')}}}}}/VP-GPHN &   \\
        \end{{tblr}}
\end{{minipage}}
                
               
        \vspace{{0.05cm}}


        \begin{{center}} \setstretch{{1.4}}
           \bfseries 
           
           {{\color{{red}}\textbf{{ \fontsize{{20pt}}{{0pt}}\selectfont GIẤY PHÉP HÀNH NGHỀ \\   		
                        KHÁM BỆNH, CHỮA BỆNH}}}}
                
                \vspace{{0.33cm}}

                \textbf{{\fontsize{{13pt}}{{0pt}} GIÁM ĐỐC SỞ Y TẾ}}
        \end{{center}}

        \setstretch{{1.18}}

        \vspace{{0.45cm}}


        Căn cứ Luật Khám bệnh, chữa bệnh ngày 09 tháng 01 năm 2023;

        Theo đề nghị của Trưởng phòng Nghiệp vụ dược Sở Y tế,

        \vspace{{0.5cm}}

        \begin{{center}}
                \textbf{{CẤP GIẤY PHÉP HÀNH NGHỀ KHÁM BỆNH, CHỮA BỆNH}}

        \end{{center}}

        \vspace{{0.64cm}}

        \setstretch{{1.2}}

        \noindent
        \begin{{minipage}}{{\textwidth}}
                \begin{{tblr}}{{
                        colspec={{X[h,4cm] X[12.8cm,l,font=\setstretch{{1.5}}]}},
                        measure=vbox,colsep=3pt,
                        width=0.9\linewidth,
                }}
                \includegraphics[width=4cm]{{anh/{anhcc}}} & 
                
        \vspace{{-0.2cm}}
                
                Họ và tên: \textbf{{{values['HỌ VÀ TÊN']}}}\par
                Ngày, tháng, năm sinh: {values['ngay sinh']}\par
                {loai_giay_to}: {so_giay_to}\par
                Ngày cấp: {values['NGÀY CẤP']} \par
                Nơi cấp: {values['NƠI CẤP']}\par
                Quốc tịch: {values['QUỐC TỊCH'].replace('Việt Nam', '')}\par
                Chức danh chuyên môn: {values['CHỨC DANH CHUYÊN MÔN']}\par
                Phạm vi hành nghề: {values['PHẠM VI HÀNH NGHỀ MỚI']}\par


                
                Giấy phép này có thời hạn đến ngày {values['NGÀY']} tháng {values['thang']} năm 2029./.\\
        \end{{tblr}}
\end{{minipage}}

        \noindent
        \hfill
        \begin{{minipage}}[t]{{0.6\textwidth}}\singlespacing
                \begin{{center}} \textit{{Phú Thọ,  ngày {values['NGÀY']} tháng {values['thang']} năm {values['nam']}}}\\ \bfseries
                        KT. GIÁM ĐỐC\\
                        PHÓ GIÁM ĐỐC\\
                        \vspace{{3cm}}
                        Nguyễn Đắc Ca\\
                \end{{center}}
        \end{{minipage}}
        \end{{document}}
        """
        # update_df_from_dict(df_gphn_id_sogp,'GIẤY PHÉP HÀNH NGHỀ KBCB.xlsx', values, so_gphn)

        compile_latex(
            text,
            f"{int(so_gphn.replace('/VP-GPHN', ''))}. {values['CHỨC DANH CHUYÊN MÔN'].upper()} {values['HỌ VÀ TÊN']}",
        )

    one_index(
        df_gphn_id_sogp,
        list_include,
        so_gphn,
        "SỐ GPHN",
        dict_radio,
        dict_combo,
        dict_widget,
        list_upper,
        list_title,
        dict_df,
        multiline,
        list_ngay,
        list_phone,
        "CẤP GPHN",
        funcs,
        600,
        700,
        0,
        100,
    )
