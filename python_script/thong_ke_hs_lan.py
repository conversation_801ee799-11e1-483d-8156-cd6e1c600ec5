import pandas as pd
import os

os.chdir("/home/<USER>/Dropbox/hnd/csv_source")


class CosobanleProcessor:
    def __init__(self):
        self.df = pd.read_csv("co_so_ban_le.csv", dtype=str)
        self.df.fillna("", inplace=True)

    def filter_nguoi_lam_hs(self):
        self.df = self.df[self.df["noi nhan"].str.upper() == "LAN"]
        return self.df

    def filter_da_thanh_toan(self):
        self.df = self.df[self.df["THANH TOÁN"] == ""]
        return self.df

    def select_columns(self):
        colist = ["ten qt-nt", "ten nguoi ptcm", "dia chi co so", "so dt chu hs"]
        self.df = self.df[colist]
        return self.df

    def rename_columns(self):
        self.df.rename(
            columns={
                "ten qt-nt": "<PERSON>ên cơ sở",
                "ten nguoi ptcm": "Người phụ trách",
                "dia chi co so": "Địa chỉ cơ sở",
                "so dt chu hs": "Số điện thoại",
            },
            inplace=True,
        )
        return self.df

    def insert_stt(self):
        self.df.insert(0, "STT", range(1, 1 + len(self.df)))
        return self.df

    def export_excel(self):
        self.df.to_excel("/home/<USER>/Dropbox/co_so_ban_le_lan.xlsx", index=False)


if __name__ == "__main__":
    processor = CosobanleProcessor()
    processor.filter_nguoi_lam_hs()
    processor.filter_da_thanh_toan()
    processor.select_columns()
    processor.rename_columns()
    processor.insert_stt()
    processor.export_excel()
