from csv_load_and_export import CsvLoaderFactory
from god_class import dataframe_to_latex, insert_stt, change_xa
import pandas as pd
import os

os.chdir("/home/<USER>/Dropbox/hnd/csv_source")

df = CsvLoaderFactory.create_basic_loader().load_df("lichthamdinh")
df["ngay td"] = pd.to_datetime(df["ngay td"])
df["ten qt-nt"] = df["ten qt-nt"].str.title().str.replace("Thuốc", "thuốc")
df.sort_values(by="ngay td", inplace=True)
df["ngay td"] = df["ngay td"].dt.strftime("%d/%m/%Y")
df.drop(columns=["stt"], inplace=True)
insert_stt(df)
df["dia chi now"] = df["dia chi co so"].apply(change_xa)
df["dia chi co so"] = df["dia chi now"] + " (tr<PERSON>ớ<PERSON> đây là: " + df["dia chi co so"] + ")"
df["ten nguoi ptcm"] = df["ten nguoi ptcm"].str.title()
list_cols = [
    "stt",
    "ten qt-nt",
    "ten nguoi ptcm",
    "dia chi co so",
    "ngay td",
]
df = df[list_cols]
latex = dataframe_to_latex(df)
with open("/home/<USER>/latex_output.txt", "w", encoding="utf-8") as f:
    f.write(latex)
