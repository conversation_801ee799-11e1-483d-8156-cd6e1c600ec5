import os

import pandas as pd

from god import dataframe_to_latex, compile_latex

os.chdir("/home/<USER>/Dropbox/hnd/csv_source")
df = pd.read_csv(
    "/home/<USER>/Dropbox/hnd/latexall/documents/HND/LIÊN THÔNG CSKD/all.csv",
    dtype=str,
    header=10,
)
df = df[
    [
        "Tên nhà thuốc",
        " loai hinh",
        "Tỉnh/ Thành phố",
        "Quận/ Huyện",
        "Xã/ Phường",
        "Địa chỉ",
        "Tên người đại diện",
        "S<PERSON> điên thoại NĐD",
        "S<PERSON> chứng chỉ hành nghề",
        "Trình độ chuyên môn",
    ]
]
df["Tỉnh/ Thành phố"] = df["Tỉnh/ Thành phố"].str.replace("Tỉnh", "tỉnh")
df["Quận/ Huyện"] = (
    df["Quận/ Huyện"]
    .str.replace("Thị xã Phúc Yên", "thành phố Phúc Yên")
    .str.replace("Thành phố Vĩnh Yên", "thành phố Vĩnh Yên")
)
df["dia chi"] = (
    df["Địa chỉ"]
    + ", "
    + df["Xã/ Phường"]
    + ", "
    + df["Quận/ Huyện"]
    + ", "
    + df["Tỉnh/ Thành phố"]
)
df = df[df[" loai hinh"] == "Doanh nghiệp bán buôn thuốc"]

df.rename(columns={"Tên nhà thuốc": "TÊN CƠ SỞ BÁN BUÔN"}, inplace=True)
df.reset_index(drop=True, inplace=True)
df.index += 1

df["stt"] = df.index
df = df[["stt", "TÊN CƠ SỞ BÁN BUÔN", "dia chi"]]
clip = dataframe_to_latex(df)


def get_current_date():
    from datetime import datetime

    now = datetime.now()
    to_day = now.strftime("%d/%m/%Y")
    return to_day


today = get_current_date()
text = rf"""
\documentclass{{article}}
\usepackage[T5]{{fontenc}}
\usepackage[fontsize=14pt]{{scrextend}}
\usepackage[a4paper,margin=1cm]{{geometry}}
\usepackage{{mathptmx}}
\usepackage{{ulem}}
\renewcommand{{\ULdepth}}{{7pt}}
\renewcommand{{\ULthickness}}{{0.5pt}}
\usepackage{{setspace}}
\usepackage{{parskip}}
\setlength{{\parskip}}{{6pt}}
\usepackage{{tabularray}}
\usepackage{{indentfirst}}
\setlength{{\parindent}}{{1.27cm}}
\usepackage{{fancyhdr}}
\fancyhf{{}}
\fancyhead[C]{{\small\thepage}}
\renewcommand{{\headrulewidth}}{{0pt}}
\pagestyle{{fancy}}
\thispagestyle{{empty}}
\begin{{document}}
\begin{{center}}
    \textbf{{DANH SÁCH CƠ SỞ BÁN BUÔN THUỐC ĐÃ ĐƯỢC SỞ Y TẾ CẤP \\GIẤY CHỨNG NHẬN ĐỦ ĐIỀU KIỆN KINH DOANH DƯỢC}}\par
(Cập nhật đến ngày {today})
\end{{center}}

\DefTblrTemplate{{contfoot-text}}{{default}}{{}}
\DefTblrTemplate{{conthead-text}}{{default}}{{}}
\DefTblrTemplate{{caption}}{{default}}{{}}
\DefTblrTemplate{{conthead}}{{default}}{{}}
\DefTblrTemplate{{capcont}}{{default}}{{}}
\noindent
\begin{{longtblr}}{{width=1\linewidth,hlines,vlines,rowhead=1,
        colspec={{X[0.5,c] X[4,l] X[4,l]}},
        colsep=3pt,
        rowsep=1pt,rows={{font=\small,m,1cm}},
        row{{1}} = {{font=\bfseries,c}}}}
    stt & Tên cơ sở bán buôn thuốc & Địa chỉ\\
  {clip}
\end{{longtblr}}
\end{{document}}
"""
compile_latex(text, "DS CƠ SỞ BÁN BUÔN THUỐC")
