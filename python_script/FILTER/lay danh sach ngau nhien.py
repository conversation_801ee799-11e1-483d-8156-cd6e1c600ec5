import os

os.chdir('/home/<USER>/Dropbox/hnd/csv_source')
import pandas as pd
df=pd.read_csv('du_lieu_gpp_all.csv',dtype=str)
df=df[df['ngay het han gpp'].notnull()]
df['ngay het han gpp']=pd.to_datetime(df['ngay het han gpp'], format='%d/%m/%Y')

df2=df[df['ngay het han gpp']>=pd.to_datetime('01/01/2026',format='%d/%m/%Y')]
df2
df2["HUYỆN"] = (
    df2["dia chi co so"]
    .apply(lambda x: x[::-1])
    .str.split(",")
    .str.get(1)
    .apply(lambda x: x[::-1])
    .str.replace(" huyện ", "")
    .str.replace(" thành phố ", "TP ")
)

print(df2['HUYỆN'].unique())
df2['nam']=df2['ngay het han gpp'].dt.year
print(df2.columns)
def custom_sample(group):
    if group.df_name[0] == 'TP Vĩnh Yên':  # Kiểm tra nếu 'HUYỆN' là 'c'
        return group.sample(n=12, replace=True if len(group) < 12 else False)
    else:
        return group.sample(n=6, replace=True if len(group) < 6 else False)
df2 = df2.groupby(['HUYỆN', 'nam']).apply(custom_sample).reset_index(drop=True)
df2=df2.drop_duplicates()
df2.to_csv('TEST.csv',index=False)
df2.reset_index(inplace=True)
df2.index+=1
df2['stt']=df2.index
df2=df2[['stt','ten qt-nt','ten nguoi ptcm','so cchnd','so dt chu hs','dia chi co so','HUYỆN','ngay het han gpp','nam']]
df2['ngay het han gpp']=df2['ngay het han gpp'].dt.strftime('%d/%m/%Y')
df2.to_csv('DANH SACH KIEM TRA.csv',index=False)