{
 "cells": [
  {
   "cell_type": "code",
   "execution_count": 1,
   "id": "initial_id",
   "metadata": {
    "collapsed": true,
    "ExecuteTime": {
     "end_time": "2023-12-28T09:06:44.021808700Z",
     "start_time": "2023-12-28T09:06:42.459828100Z"
    }
   },
   "source": [
    "import pandas as pd\n",
    "import os\n",
    "from god import dataframe_to_latex\n",
    "os.chdir(r\"D:\\Dropbox\\latexall\\code_manager\\pandas\")\n",
    "df=pd.read_csv(r\"D:\\Dropbox\\latexall\\Documents\\HND\\LIÊN THÔNG CSKD\\all.csv\",dtype=str,header=10)\n",
    "df=df[['Tên nhà thuốc',' loai hinh','Tỉnh/ Thành phố', 'Quận/ Huyện', '<PERSON>ã/ Phường', '<PERSON><PERSON><PERSON> chỉ','<PERSON>ên người đại diện','<PERSON><PERSON> điên thoại NĐD','<PERSON><PERSON> chứng chỉ hành nghề', '<PERSON>r<PERSON><PERSON> độ chuyên môn']]\n",
    "df['Tỉnh/ Thành phố']=df['Tỉnh/ Thành phố'].str.replace('Tỉnh','tỉnh')\n",
    "df['Quận/ Huyện']=df['Quận/ Huyện'].str.replace('Thị xã Phúc Yên','thành phố Phúc Yên').str.replace('Thành phố Vĩnh Yên','thành phố Vĩnh Yên')\n",
    "df['dia chi']=df['Địa chỉ'] + ', ' + df['Xã/ Phường'] + ', ' +df['Quận/ Huyện'] + ', ' +df['Tỉnh/ Thành phố']\n",
    "df = df[df[' loai hinh'] == 'Doanh nghiệp bán buôn thuốc']\n",
    "\n",
    "df.rename(columns={'Tên nhà thuốc':'TÊN CƠ SỞ BÁN BUÔN'},inplace=True)\n",
    "df.reset_index(drop=True,inplace=True)\n",
    "df.index+=1\n"
   ],
   "outputs": []
  },
  {
   "cell_type": "code",
   "execution_count": 2,
   "source": [
    "df['stt']=df.index\n",
    "df=df[['stt','TÊN CƠ SỞ BÁN BUÔN','dia chi']]\n",
    "clip=dataframe_to_latex(df)"
   ],
   "metadata": {
    "collapsed": false,
    "ExecuteTime": {
     "end_time": "2023-12-28T09:06:44.037810500Z",
     "start_time": "2023-12-28T09:06:44.022807300Z"
    }
   },
   "id": "8a13e5a688a00f9b",
   "outputs": []
  },
  {
   "cell_type": "code",
   "execution_count": 3,
   "source": [
    "def get_current_date():\n",
    "    from datetime import datetime\n",
    "    now = datetime.now()\n",
    "    to_day=now.strftime(\"%d/%m/%Y\")\n",
    "    return to_day\n",
    "today = get_current_date()\n",
    "text=fr'''\n",
    "\\documentclass{{article}}\n",
    "\\usepackage[T5]{{fontenc}}\n",
    "\\usepackage[fontsize=14pt]{{scrextend}}\n",
    "\\usepackage[a4paper,margin=1cm]{{geometry}}\n",
    "\\usepackage{{mathptmx}}\n",
    "\\usepackage{{ulem}}\n",
    "\\renewcommand{{\\ULdepth}}{{7pt}}\n",
    "\\renewcommand{{\\ULthickness}}{{0.5pt}}\n",
    "\\usepackage{{setspace}}\n",
    "\\usepackage{{parskip}}\n",
    "\\setlength{{\\parskip}}{{6pt}}\n",
    "\\usepackage{{tabularray}}\n",
    "\\usepackage{{indentfirst}}\n",
    "\\setlength{{\\parindent}}{{1.27cm}}\n",
    "\\usepackage{{fancyhdr}}\n",
    "\\fancyhf{{}}\n",
    "\\fancyhead[C]{{\\small\\thepage}}\n",
    "\\renewcommand{{\\headrulewidth}}{{0pt}}\n",
    "\\pagestyle{{fancy}}\n",
    "\\thispagestyle{{empty}}\n",
    "\\begin{{document}}
}
\n",
    "\\begin{{center}}\n",
    "    \\textbf{{DANH SÁCH CƠ SỞ BÁN BUÔN THUỐC ĐÃ ĐƯỢC SỞ Y TẾ CẤP \\\\GIẤY CHỨNG NHẬN ĐỦ ĐIỀU KIỆN KINH DOANH DƯỢC}}\\par\n",
    "(Cập nhật đến ngày {today})\n",
    "\\end{{center}}\n",
    "\n",
    "\\DefTblrTemplate{{contfoot-text}}{{default}}{{}}\n",
    "\\DefTblrTemplate{{conthead-text}}{{default}}{{}}\n",
    "\\DefTblrTemplate{{caption}}{{default}}{{}}\n",
    "\\DefTblrTemplate{{conthead}}{{default}}{{}}\n",
    "\\DefTblrTemplate{{capcont}}{{default}}{{}}\n",
    "\\noindent\n",
    "\\begin{{longtblr}}{{width=1\\linewidth,hlines,vlines,rowhead=1,\n",
    "        colspec={{X[0.5,c] X[4,l] X[4,l]}},\n",
    "        colsep=3pt,\n",
    "        rowsep=1pt,rows={{font=\\small,m,1cm}},\n",
    "        row{{1}} = {{font=\\bfseries,c}}}}\n",
    "    stt & Tên cơ sở bán buôn thuốc & Địa chỉ\\\\\n",
    "  {clip}\n",
    "\\end{{longtblr}}\n",
    "\\end{{document}}\n",
    "'''"
   ],
   "metadata": {
    "collapsed": false,
    "ExecuteTime": {
     "end_time": "2023-12-28T09:06:44.063565700Z",
     "start_time": "2023-12-28T09:06:44.038816800Z"
    }
   },
   "id": "853c3056feca0c1b",
   "outputs": []
  },
  {
   "cell_type": "code",
   "execution_count": 4,
   "source": [
    "from god import compile_latex\n",
    "compile_latex(text,'DS CƠ SỞ BÁN BUÔN THUỐC')"
   ],
   "metadata": {
    "collapsed": false,
    "ExecuteTime": {
     "end_time": "2023-12-28T09:06:49.043661100Z",
     "start_time": "2023-12-28T09:06:44.057515700Z"
    }
   },
   "id": "214435f7dd0ee3bb",
   "outputs": []
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3",
   "language": "/home/<USER>/python313/bin/python",
   "name": "/home/<USER>/python313/bin/python"
  },
  "language_info": {
   "codemirror_mode": {
    "name": "ipython",
    "version": 2
   },
   "file_extension": ".py",
   "mimetype": "text/x-python",
   "name": "/home/<USER>/python313/bin/python",
   "nbconvert_exporter": "/home/<USER>/python313/bin/python",
   "pygments_lexer": "ipython2",
   "version": "2.7.6"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 5
}
