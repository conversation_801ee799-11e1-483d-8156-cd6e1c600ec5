{"cells": [{"cell_type": "code", "execution_count": 1, "id": "a5a39bb6", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import os\n", "os.chdir('/home/<USER>/Dropbox/hnd/csv_source')\n", "df=pd.read_csv(\"du_lieu_gpp_all.csv\",dtype=str)"]}, {"cell_type": "code", "execution_count": 3, "id": "3b30c08f", "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['so cchnd', 'so dkkd', 'ten qt-nt', 'ten co so dang ky hn',\n", "       'dia chi co so', 'so dt chu hs', 'ten nguoi ptcm', 'trinh do cm',\n", "       'ngay qd', 'so gpp', 'ngay het han gpp', 'cmnd', 'ngay cap cchnd',\n", "       'noi cap cchnd', 'so qd', 'co quan chu quan',\n", "       'dia chi co quan chu quan', 'loai hinh', 'vi tri hanh nghe'],\n", "      dtype='object')"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["df.columns"]}, {"cell_type": "code", "execution_count": 4, "id": "46428cda", "metadata": {}, "outputs": [], "source": ["df=df[['ten qt-nt','dia chi co so','so dt chu hs','ten nguoi ptcm']]"]}, {"cell_type": "code", "execution_count": 8, "id": "407162a3", "metadata": {}, "outputs": [{"ename": "ModuleNotFoundError", "evalue": "No module named 'god_class'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mModuleNotFoundError\u001b[0m                       <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[8], line 7\u001b[0m\n\u001b[1;32m      3\u001b[0m parent_dir \u001b[38;5;241m=\u001b[39m os\u001b[38;5;241m.\u001b[39mpath\u001b[38;5;241m.\u001b[39mdirname(notebook_dir)\n\u001b[1;32m      4\u001b[0m sys\u001b[38;5;241m.\u001b[39mpath\u001b[38;5;241m.\u001b[39mappend(parent_dir)\n\u001b[0;32m----> 7\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mgod_class\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m insert_stt\n\u001b[1;32m     10\u001b[0m insert_stt(df)\n", "\u001b[0;31mModuleNotFoundError\u001b[0m: No module named 'god_class'"]}], "source": ["import sys\n", "notebook_dir = os.path.abspath('')\n", "parent_dir = os.path.dirname(notebook_dir)\n", "sys.path.append(parent_dir)\n", "\n", "\n", "from god_class import insert_stt\n", "\n", "\n", "insert_stt(df)"]}, {"cell_type": "code", "execution_count": null, "id": "6e478a06", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "/home/<USER>/python313/bin/python", "name": "/home/<USER>/python313/bin/python"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "i/home/<USER>/python313/bin/python", "version": "3.12.5"}}, "nbformat": 4, "nbformat_minor": 5}