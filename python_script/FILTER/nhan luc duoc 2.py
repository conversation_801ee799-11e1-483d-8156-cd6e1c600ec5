import os

import pandas as pd

os.chdir('/home/<USER>/Dropbox/hnd/csv_source')
df=pd.read_csv('du_lieu_gpp_all.csv',dtype=str)
df.fillna('',inplace=True)
thong_ke=df.groupby('trinh do cm').size().to_dict()
df2=pd.read_csv('dshn_duoc.csv',dtype=str)
df2.fillna('',inplace=True)
df2=df2[(df2['ten co so dang ky hn']!='') & (df2['ten qt-nt']!='')]
len(df2) + len(df)
df2.groupby('trinh do cm').size().to_dict()
df=df[(df['ten co so dang ky hn']!='') & (df['ten qt-nt']!='')]
df=df[~df['ten co so dang ky hn'].str.contains('Nhà thuốc')]
df=df[~df['ten co so dang ky hn'].str.contains('Quầy thuốc')]
df=df[~df['ten co so dang ky hn'].str.contains('Công ty')]
df=df[~df['ten co so dang ky hn'].str.contains('/')]

trinhdo_grouped_counts = df.groupby('trinh do cm').size().to_dict()
trinhdo_grouped_counts