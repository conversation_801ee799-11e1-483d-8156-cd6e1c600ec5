import os

import pandas as pd

os.chdir('/home/<USER>/Dropbox/hnd/csv_source')
df = pd.read_csv('dshn_duoc.csv', dtype=str)
df.fillna('', inplace=True)
mask = ((df['ten co so dang ky hn'] != '') & ~df['ten co so dang ky hn'].str.contains('<PERSON><PERSON><PERSON> thuốc|Quầy thuốc|Công ty|/',
                                                                                      regex=True))
df1 = df[mask]

trinhdo_grouped_counts = df1.groupby('trinh do cm').size().to_dict()
print(trinhdo_grouped_counts)
df2 = df[~mask]
trinhdo_grouped_counts_2 = df2.groupby('trinh do cm').size().to_dict()
print(trinhdo_grouped_counts_2)