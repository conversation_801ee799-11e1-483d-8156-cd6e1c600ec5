{"cells": [{"cell_type": "code", "source": ["import pandas as pd\n", "import os \n", "os.chdir(r\"D:\\Dropbox\\latexall\\code_manager\\pandas\")"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-12-27T13:46:50.643278500Z", "start_time": "2023-12-27T13:46:50.635245700Z"}}, "id": "3b7f1a15fd45e069", "execution_count": 34, "outputs": []}, {"cell_type": "code", "source": ["df=pd.read_csv('du_lieu_gpp_all.csv',dtype=str)"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-12-27T13:46:51.033340700Z", "start_time": "2023-12-27T13:46:50.644306700Z"}}, "id": "1e1e2e9aa92a7b03", "execution_count": 35, "outputs": []}, {"cell_type": "code", "source": ["df.fillna('',inplace=True)"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-12-27T13:46:51.048921900Z", "start_time": "2023-12-27T13:46:51.034588800Z"}}, "id": "2f3a5feca1e988a7", "execution_count": 36, "outputs": []}, {"cell_type": "code", "source": ["df.groupby('trinh do cm').size().to_dict()"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-12-27T13:46:51.077559600Z", "start_time": "2023-12-27T13:46:51.050370300Z"}}, "id": "f22baacad468c73d", "execution_count": 37, "outputs": []}, {"cell_type": "code", "source": ["df2=pd.read_csv('dshn_duoc.csv',dtype=str)"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-12-27T13:46:52.364403900Z", "start_time": "2023-12-27T13:46:51.064951Z"}}, "id": "f8a63ea72ff1583d", "execution_count": 38, "outputs": []}, {"cell_type": "code", "source": ["df2.fillna('',inplace=True)"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-12-27T13:46:52.380063200Z", "start_time": "2023-12-27T13:46:52.365827300Z"}}, "id": "aecbe8b80ac981f1", "execution_count": 39, "outputs": []}, {"cell_type": "code", "source": ["df2=df2[(df2['ten co so dang ky hn']!='') & (df2['ten qt-nt']=='')]"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-12-27T13:46:52.395290200Z", "start_time": "2023-12-27T13:46:52.380063200Z"}}, "id": "59eb664cff41c255", "execution_count": 40, "outputs": []}, {"cell_type": "code", "source": ["df2.groupby('trinh do cm').size().to_dict()"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-12-27T13:46:52.411003100Z", "start_time": "2023-12-27T13:46:52.395290200Z"}}, "id": "177ba27703fbfb9", "execution_count": 41, "outputs": []}, {"cell_type": "code", "source": ["df3=pd.read_csv('nguoi_thuc_hanh_duoc.csv',dtype=str)"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-12-27T13:46:52.456793Z", "start_time": "2023-12-27T13:46:52.412007900Z"}}, "id": "92bedae2586791f6", "execution_count": 42, "outputs": []}, {"cell_type": "code", "source": ["df3.fillna('',inplace=True)"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-12-27T13:46:52.472154500Z", "start_time": "2023-12-27T13:46:52.459002Z"}}, "id": "668b190984bfeb83", "execution_count": 43, "outputs": []}, {"cell_type": "code", "source": ["df3.groupby('trinh do cm').size().to_dict()"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-12-27T13:46:52.504336900Z", "start_time": "2023-12-27T13:46:52.473206600Z"}}, "id": "64175dd3815e181b", "execution_count": 44, "outputs": []}, {"cell_type": "code", "source": ["len(df2) + len(df) + len(df3)\n"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-12-27T13:46:52.505351400Z", "start_time": "2023-12-27T13:46:52.488017400Z"}}, "id": "9e0bda691afa12db", "execution_count": 45, "outputs": []}, {"cell_type": "code", "source": [], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2023-12-27T13:46:52.528318300Z", "start_time": "2023-12-27T13:46:52.503166700Z"}}, "id": "2774e1577ad8664c", "execution_count": 45, "outputs": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "/home/<USER>/python313/bin/python", "name": "/home/<USER>/python313/bin/python"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "/home/<USER>/python313/bin/python", "nbconvert_exporter": "/home/<USER>/python313/bin/python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}