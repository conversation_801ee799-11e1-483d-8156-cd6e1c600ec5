import os

import pandas as pd

os.chdir("/home/<USER>/Dropbox/hnd/csv_source")
df = pd.read_csv("du_lieu_gpp_all.csv", dtype=str, header=10)
df = df[
    [
        "Tên nhà thuốc",
        " loai hinh",
        "Tỉnh/ Thành phố",
        "Quận/ Huyện",
        "Xã/ Phường",
        "Địa chỉ",
        "Tên người đại diện",
        "Số điên thoại NĐD",
        "Số chứng chỉ hành nghề",
        "Trình độ chuyên môn",
    ]
]
df["Tỉnh/ Thành phố"] = df["Tỉnh/ Thành phố"].str.replace("Tỉnh", "tỉnh")
df["Quận/ Huyện"] = (
    df["Quận/ Huyện"]
    .str.replace("Thị xã Phúc Yên", "thành phố Phúc Yên")
    .str.replace("Thành phố Vĩnh Yên", "thành phố Vĩnh Yên")
)
df["dia chi"] = (
    df["Địa chỉ"]
    + ", "
    + df["Xã/ Phường"]
    + ", "
    + df["Quận/ Huyện"]
    + ", "
    + df["Tỉnh/ Thành phố"]
)
df = df[
    [
        "Tên nhà thuốc",
        " loai hinh",
        "dia chi",
        "Tên người đại diện",
        "Số điên thoại NĐD",
        "Số chứng chỉ hành nghề",
        "Trình độ chuyên môn",
    ]
]
df.rename(
    columns={
        "Tên nhà thuốc": "TÊN CƠ SỞ BÁN LẺ",
        " loai hinh": "loai hinh",
        "Tên người đại diện": "ten nguoi ptcm DƯỢC",
        "Số điên thoại NĐD": "SĐT",
        "Số chứng chỉ hành nghề": "so cchnd",
        "Trình độ chuyên môn": "TRÌNH ĐỘ CHUYÊN MÔN",
    },
    inplace=True,
)
df.drop("TRÌNH ĐỘ CHUYÊN MÔN", axis=1, inplace=True)
df_nha_thuoc = df[df["loai hinh"] == "Nhà thuốc"]
df_quay_thuoc = df[df["loai hinh"] == "Quầy thuốc"]
df_ban_buon = df[df["loai hinh"] == "Doanh nghiệp bán buôn thuốc"]
df_nha_thuoc.reset_index(drop=True, inplace=True)
df_nha_thuoc.index += 1
df_quay_thuoc.reset_index(drop=True, inplace=True)
df_quay_thuoc.index += 1
df_ban_buon.reset_index(drop=True, inplace=True)
df_ban_buon.index += 1
df_ban_buon.rename(columns={"TÊN CƠ SỞ BÁN LẺ": "TÊN CƠ SỞ BÁN BUÔN"}, inplace=True)
df_ban_buon.drop(["ten nguoi ptcm DƯỢC", "so cchnd"], axis=1, inplace=True)
# Tạo Excel writer với pandas và openpyxl làm engine
with pd.ExcelWriter("cơ sở kinh doanh dược.csv") as writer:
    # Ghi từng DataFrame vào từng sheet khác nhau trong cùng một file Excel
    df_nha_thuoc.to_csv(writer, sheet_name="Nhà thuốc", index_label="stt")
    df_quay_thuoc.to_csv(writer, sheet_name="Quầy thuốc", index_label="stt")
    df_ban_buon.to_csv(writer, sheet_name="Cơ sở bán buôn thuốc", index_label="stt")
