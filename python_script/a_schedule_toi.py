import subprocess
import logging

# Thi<PERSON>t lập logging
logging.basicConfig(
    filename="/home/<USER>/Dropbox/sang_debug.log",
    level=logging.INFO,
    format="%(asctime)s - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)


def run_script(script_path):
    try:
        subprocess.run(
            [
                "/home/<USER>/python313/bin/python",
                script_path,
            ],
            check=True,
        )
        logging.info(f"Đã hoàn thành {script_path}")
    except subprocess.CalledProcessError as e:
        logging.error(f"Lỗi khi chạy {script_path}: {e}")


def main():
    logging.info("Bắt đầu chạy script...")

    scripts = [
        "/home/<USER>/Dropbox/hnd/python_script/get_van_ban_den.py",
        "/home/<USER>/Dropbox/hnd/python_script/all_chi_le.py",
        "/home/<USER>/Dropbox/hnd/python_script/all_vanban_da_phathanh.py",
        "/home/<USER>/Dropbox/hnd/python_script/all_chuyen_hs.py",
        "/home/<USER>/Dropbox/hnd/python_script/all_check_finished_hs.py",
        "/home/<USER>/Dropbox/hnd/python_script/get_van_ban_di.py",
    ]

    for script in scripts:
        run_script(script)

    logging.info("Tất cả các script đã chạy xong.")


if __name__ == "__main__":
    main()
