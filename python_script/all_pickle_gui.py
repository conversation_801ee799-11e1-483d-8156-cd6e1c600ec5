import os
import pickle

from god_class import send_notification, yes_no
from top_most_get_text import input_dialog
os.chdir("/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pickle_file")


def load_pickle(file):
    try:
        with open(file, "rb") as f:
            return pickle.load(f)
    except FileNotFoundError:
        return None


# %%
def save_last_dict(so_qd, file):
    with open(file, "wb") as f:
        pickle.dump(so_qd, f)

if yes_no("Hoi","<PERSON><PERSON> phải cấp chứng chỉ hành nghề dược?"):
    so_qd = input_dialog("Nhap","Nhập số quyết định cấp chứng chỉ hành nghề dược","")
    save_last_dict(so_qd,"soqd_cc.pk")
    send_notification("Đ<PERSON> lưu số quyết định cấp chứng chỉ hành nghề dược: {}".format(so_qd))
else:
    so_qd = input_dialog("Nhap","<PERSON><PERSON><PERSON><PERSON> số quyết định cấp giấy gpp","")
    save_last_dict(so_qd,"soqd_gpp.pk")
    send_notification("Đã lưu số quyết định cấp giấy gpp: {}".format(so_qd))
