import os
import re
import shutil
from datetime import datetime
import pyperclip

from all_pickle import load_pickle
from god_class import (
    open_with_foxit,
    close_app,
    auto_day_vb_phucdap,
    get_lastest_file,
    send_notification,
    wait_for_file_word,
    auto_day_vb,
)


class DocumentProcessor:
    def __init__(self):
        self.pickle_data = None
        self.filename = None
        os.chdir("/home/<USER>/Dropbox/hnd/latexall/mylatex")

    def extract_and_modify_string(self, input_path):
        match = re.search(r"vb_cho_in/(.*?)-\d+", input_path)
        if match:
            result = match.group(1)
            result = result.replace("-", "/", 1)
            return result
        return None

    def load_data(self):
        self.pickle_data = load_pickle(
            "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pickle_file/tieude.pk"
        )
        self.filename = self.pickle_data[1].split("/")[-1].replace(".tex", ".pdf")

    def handle_special_files(self):
        if "THÔNG_BÁO_KẾT_QUẢ" in self.filename:
            self._copy_file("tb_khong_dat.pk")
        elif "Yêu_cầu" in self.filename:
            self._copy_file("tb_bo_sung.pk")

        if "V/v" in self.pickle_data[1]:
            self.pickle_data[2] = "CV"

    def _copy_file(self, pickle_name):
        file_path = os.path.expanduser(load_pickle(pickle_name))
        source_path = os.path.expanduser(self.pickle_data[1])
        if os.path.exists(file_path):
            shutil.copy(source_path, file_path)
        else:
            send_notification(f"Tệp nguồn không tồn tại: {source_path}")

    def process_existing_file(self):
        file_path = f"/home/<USER>/Dropbox/hnd/latexall/vb_cho_in/{self.filename}"
        if os.path.isfile(file_path):
            open_with_foxit(f"Z:/lediem/Dropbox/hnd/latexall/vb_cho_in/{self.filename}")
            return True
        return False

    def process_new_file(self):
        clipboard_content = pyperclip.paste().strip("\n")
        iso_time = datetime.now().strftime("%Y%m%d_%H%M%S")

        original_pdf_path = clipboard_content
        original_filename = os.path.basename(original_pdf_path)
        original_dir = os.path.dirname(original_pdf_path)

        new_filename = f"{iso_time}-{original_filename}"
        target_pdf_path = f"/home/<USER>/Dropbox/hnd/latexall/vb_cho_in/{new_filename}"

        shutil.copy2(original_pdf_path, target_pdf_path)

        latex_filename = os.path.splitext(original_filename)[0] + ".tex"
        original_latex_path = os.path.join(original_dir, latex_filename)

        if os.path.exists(original_latex_path):
            new_latex_filename = f"{iso_time}-{latex_filename}"
            target_latex_path = (
                f"/home/<USER>/Dropbox/hnd/latexall/vb_cho_in/{new_latex_filename}"
            )
            shutil.copy2(original_latex_path, target_latex_path)

        path_file = target_pdf_path.replace("/home/", "Z:/")
        open_with_foxit(path_file)

    def process_word_file(self):
        wait_for_file_word(180)
        close_app("FoxitPDFEditor.")
        lastest_word_file = get_lastest_file("/home/<USER>/Desktop", ".docx")
        tieude_dict = {"tieu de": self.pickle_data[0]}
        tieude = creat_and_get(tieude_dict)["tieu de"]
        pyperclip.copy(tieude)

        if "SYT" in self.pickle_data[1]:
            auto_day_vb_phucdap(
                lastest_word_file,
                tieude,
                self.extract_and_modify_string(self.pickle_data[1]),
                self.pickle_data[2],
            )
        elif ("TB" in self.pickle_data[2] or "CV" in self.pickle_data[2]) and len(
            self.pickle_data[2]
        ) > 2:
            auto_day_vb_phucdap(
                lastest_word_file, tieude, self.pickle_data[2], self.pickle_data[3]
            )
        else:
            auto_day_vb(lastest_word_file, tieude, self.pickle_data[2])


def main():
    processor = DocumentProcessor()
    processor.load_data()
    processor.handle_special_files()
    if not processor.process_existing_file():
        processor.process_new_file()
    processor.process_word_file()


if __name__ == "__main__":
    main()
