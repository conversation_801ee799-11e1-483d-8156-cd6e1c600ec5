import subprocess
from all_pickle import load_pickle
from cchnd_hs_cap_dc import dc_cchnd
from cchnd_hs_cap_lai import cchnd_caplai
from cchnd_hs_cap_moi import hs_cchnd
from gdp_cap_dc_cap_lai import gdp_cap_dc_cap_lai
from gdp_hs_new import gdp_new
from gdp_cap_dinh_ky import run_main
from gpp_cap_dc_caplai import gpp_capdc_caplai
from Hoi_thao_gt_thuoc import hoi_thao_gt_thuoc
from gpp_hs_cap_moi import gpp_hs_moi
from mp_nhap_ho_so import mp_nhap_hs
import mp_tncb
from mp_xnqc import xnqc_mp
from god_class import send_notification, setup_logging, yes_no, show_message
import gpp_hs_ttd_solid
import os
import pyperclip
import shutil


@setup_logging("all_nhap_ho_so.log")
def main():
    global path
    os.chdir("/home/<USER>/Dropbox/hnd/csv_source")
    path = load_pickle(
        "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pickle_file/hs_path.pk"
    )

    mhs, thutuc, _ = path.split("/")[-1].split("--")
    pyperclip.copy(mhs)

    thu_tuc_mapping = {
        "CAP GPP VA DKKD": gpp_hs_moi,
        "CAP GDP VA DKKD": gdp_new,
        "DANH GIA DUY TRI DAP UNG GPP": gpp_hs_ttd_solid.gpp_hs_ttd,
        "DANH GIA DAP UNG GPP NTBV": gpp_hs_moi,
        "CAP SO TN PCBMP SAN XUAT TRONG NUOC": mp_tncb.run_main,
        "DIEU CHINH ND CCHND": dc_cchnd,
        "CAP LAI CCHND DO": cchnd_caplai,
        "CAP CCHND": hs_cchnd,
        "XAC NHAN QC MP": xnqc_mp,
        "CAP THAY DOI DKKD": lambda x: (
            gdp_cap_dc_cap_lai(x)
            if yes_no("CÓ PHẢI GDP KHÔNG", "CÓ PHẢI GDP KHÔNG")
            else gpp_hs_moi(x)
        ),
        "DIEU CHINH DKKD": lambda x: (
            gdp_cap_dc_cap_lai(x)
            if yes_no("CÓ PHẢI GDP KHÔNG", "CÓ PHẢI GDP KHÔNG")
            else gpp_capdc_caplai(x)
        ),
        "CAP GCN DU DIEU KIEN SX MP": mp_nhap_hs,
        "TAI THAM DINH GDP CONG TY": run_main,
        "DANH GIA DUY TRI DAP UNG GDP": run_main,
        "CAP LAI DKKD": gpp_capdc_caplai,
        "HOI THAO GIOI THIEU THUOC": hoi_thao_gt_thuoc,
    }

    for key, func in thu_tuc_mapping.items():
        if key in thutuc:
            func(mhs)
            break
    else:
        show_message("THÔNG BÁO", "thu tuc KHÔNG ĐƯỢC HỖ TRỢ")  # noqa: F405


if __name__ == "__main__":
    main()
    # Thêm mã để kill chương trình kitty bằng subprocess
    subprocess.run(["pkill", "kitty"], check=True)
    shutil.move(
        path, "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pdf_documents/file_hs_backup"
    )
    send_notification("da move thu muc")
