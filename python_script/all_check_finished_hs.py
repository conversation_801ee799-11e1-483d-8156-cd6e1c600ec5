import os
import subprocess
import pandas as pd
from god_class import TelegramSend, send_notification, send_error_to_telegram
from playwright_class import <PERSON>ch<PERSON>u<PERSON>ong
from loguru import logger
import sys


class CheckFinishedHs(DichVuCong):
    def __init__(self, user):
        super().__init__(user)
        self.user = user
        self.df_daxuly = None

    def get_hs_dxl(self):
        os.chdir("/home/<USER>/Dropbox/hnd/csv_source")
        self.df_daxuly = pd.read_csv("da_tra_kq.csv", dtype=str, index_col="ma ho so")
        if self.df_daxuly.empty:
            send_notification("ĐÃ TRẢ HẾT KQ")
            sys.exit()

    def convert_hs_dxl(self, elements):
        list_mhs = []
        for element in elements:
            if self.get_attribute("value", element) in self.df_daxuly.index.tolist():
                list_mhs.append(self.get_attribute("value", element))
        if not list_mhs:
            send_notification("ĐÃ TRẢ HẾT KQ", True)
            sys.exit()
        self.df_daxuly = self.df_daxuly.loc[list_mhs]

    def update_hs_dxl(self):
        self.df_daxuly.reset_index(inplace=True)
        self.df_daxuly.index += 1
        self.df_daxuly.to_csv("da_tra_kq.csv", index=False)

    def sort_df_by_han(self):
        self.df_daxuly["han back_up"] = pd.to_datetime(
            self.df_daxuly["han back_up"], format="%d/%m/%Y %H:%M:%S"
        )
        self.df_daxuly.sort_values(by="han back_up", ascending=True, inplace=True)
        self.df_daxuly["han back_up"] = self.df_daxuly["han back_up"].dt.strftime(
            "%d/%m/%Y %H:%M:%S"
        )

        self.update_hs_dxl()

    def creat_report_and_send(self):
        report = ""
        self.sort_df_by_han()
        self.df_daxuly.reset_index(inplace=True)
        self.df_daxuly.index += 1
        for index, row in self.df_daxuly.iterrows():
            report += f"{index}. {row['thu tuc']} - {row['ten nguoi ptcm']} - {row['han back_up']}\n\n"
        if report:
            report = "DANH SÁCH HỒ SƠ CHƯA TRẢ KẾT QUẢ:\n\n" + report
            tele = TelegramSend("hnd")
            tele.send_message_warp(report)

    @logger.catch
    @send_error_to_telegram
    def run(self):
        send_notification("start: all_check_finished_hs")
        self.get_hs_dxl()
        super().setup(headless=False)
        super().login_dvc_by_user(self.user)
        self.go_to_trangthai("DA_XU_LY")
        self.expand_ds()
        elements = self.get_row_elements()
        self.convert_hs_dxl(elements)
        self.creat_report_and_send()
        self.cleanup()
        subprocess.run(
            [
                "/home/<USER>/python313/bin/python",
                "/home/<USER>/Dropbox/hnd/python_script/all_chi_le_check_tky.py",
            ]
        )


dvc = CheckFinishedHs("tungson")
dvc.run()
