import os
import sys

import pandas as pd

# Tắt cảnh báo SettingWithCopyWarning
pd.options.mode.chained_assignment = None  # default='warn'

os.chdir('/home/<USER>/Dropbox/hnd/csv_source')

# <PERSON><PERSON><PERSON> các tham số từ dòng lệnh
file_path = sys.argv[1]
num_rows = sys.argv[2]


def filter_rows_by_condition(file_paths, numrows):
    # Đọc file Excel
    df = pd.read_csv(file_paths, dtype=str)
    df.fillna('', inplace=True)
    last_rows = df.tail(int(numrows))
    for index, row in last_rows.iterrows():
        print(row.to_dict())
        print('\n')


# <PERSON><PERSON><PERSON> hàm và in kết quả
filter_rows_by_condition(file_path, num_rows)  # filter_rows_by_condition('vanbandi.csv','trich_yeu','cấp điều chỉnh')