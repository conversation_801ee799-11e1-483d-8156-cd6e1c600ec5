import os
import subprocess
from datetime import datetime, timedelta


import pandas as pd

from all_pickle import load_pickle
from god_class import (
    send_notification,
    setup_logging,
    update_df_name_da_<PERSON>han,
    get_dict_from_index_df,
)
from god_class import show_message
from python312 import run_python_312

from create_snippets import creat_and_get
from solid_string_format import DictStringFormatter


def get_df_from_excel(path_df, index_col):
    dff = pd.read_csv(path_df, dtype=str)
    dff.set_index(index_col, inplace=True)
    dff.fillna("", inplace=True)
    return dff


def check_cchnd(so_cccd, df_cchnd):
    df_dshn = pd.read_csv("dshn_duoc.csv", dtype=str, index_col="cmnd")
    if so_cccd in df_dshn.index:
        matching_rows = df_dshn.loc[df_dshn.index == so_cccd]
        for _, row in matching_rows.iterrows():
            if pd.notna(row["so qd"]) and row["so qd"] != "":
                show_message("THÔNG BÁO", f"{row['ten nguoi ptcm']} ĐÃ ĐƯỢC CẤP CCHND")
    if so_cccd in df_cchnd["cmnd"].tolist():
        send_notification(f"THÔNG BÁO {so_cccd} ĐÃ TỪNG NỘP HỒ SƠ ĐỀ NGHỊ CẤP CCHND")


@setup_logging("cchnd.log")
def hs_cchnd(mhs):
    os.chdir("/home/<USER>/Dropbox/hnd/csv_source")
    df_name = pd.read_csv("name.csv", dtype=str, index_col="ma ho so")
    ngay_tiep_nhan = df_name.loc[mhs, "ngay tiep nhan"]

    df_name.fillna("", inplace=True)
    df_cchnd = pd.read_csv("cchnd.csv", dtype=str, index_col="ma ho so")

    so_cccd = df_name.loc[mhs, "cmnd"]
    send_notification("lưu ý nhập đúng trình độ cm")
    ten_nguoi_ptcm = df_name.loc[mhs, "ten nguoi ptcm"]
    ngay_sinh = df_name.loc[mhs, "ngay sinh"]
    dia_chi_thuong_tru = df_name.loc[mhs, "dia chi thuong tru"]
    check_cchnd(so_cccd, df_cchnd)
    df_dkth = pd.read_csv("nguoi_thuc_hanh_duoc.csv", dtype=str, index_col="cmnd")
    dict_dkth = get_dict_from_index_df(df_dkth, so_cccd)

    nc_cmnd = (
        "Cục Cảnh sát Quản lý hành chính về trật tự xã hội"
        if so_cccd.startswith("0")
        else "Công an tỉnh Vĩnh Phúc"
    )
    default_val = {
        "gioi tinh": "0",
        "noi cap cmnd": nc_cmnd,
        "ngay cap cmnd": "",
        "noi tot nghiep": "",
        "ngay tot nghiep": "",
        "cong ty xac nhan": dict_dkth.get("TÊN CƠ SỞ ĐĂNG KÝ TH", ""),
        "ngay bat dau th": "",
        "ngay ket thuc th": "",
        "ten nguoi ptcm": ten_nguoi_ptcm,
        "dia chi thuong tru": dia_chi_thuong_tru,
        "ngay sinh": ngay_sinh,
        "so dt chu hs": df_name.loc[mhs, "so dt chu hs"],
    }
    if so_cccd in df_dkth.index:
        default_val.update(
            {
                key: value
                for key, value in dict_dkth.items()
                if key in default_val.keys() and value
            }
        )

    list_upper = ["ten nguoi ptcm"]
    list_ngay = [
        "ngay sinh",
        "ngay cap cmnd",
        "ngay bat dau th",
        "ngay ket thuc th",
        "ngay tot nghiep",
    ]
    list_phone = ["so dt chu hs"]

    values = creat_and_get(default_val)
    formatter = DictStringFormatter(values)
    values = (
        formatter.apply_upper(list_upper)
        .apply_date_format(list_ngay)
        .apply_phone_format(list_phone)
        .get_result()
    )

    values["ma ho so"] = mhs
    values["cmnd"] = so_cccd

    # get default value
    if (
        "đại học" in values["noi tot nghiep"].lower()
        or "học viện" in values["noi tot nghiep"].lower()
    ):
        trinh_do_cm = "Đại học dược"
        pham_vi = "Nhà thuốc"
    elif "cao đẳng" in values["noi tot nghiep"].lower():
        trinh_do_cm = "Cao đẳng dược"
        pham_vi = "Quầy thuốc"
    else:
        trinh_do_cm = "Trung cấp dược"
        pham_vi = "Quầy thuốc"

    cong_ty_xnth = values["cong ty xac nhan"].lower()
    if "công ty" in cong_ty_xnth or "ct" in cong_ty_xnth:
        noidung_th = "Phân phối thuốc và bảo quản thuốc"
    else:
        noidung_th = "Bán lẻ thuốc"

    default_val_update = {
        "van de hs": "",
        "trinh do cm": trinh_do_cm,
        "dang ky pham vi": pham_vi,
        "noi dung thuc hanh": noidung_th,
    }

    values_update = creat_and_get(default_val_update)
    values.update(values_update)

    if values["trinh do cm"] == "đại học":
        values["trinh do cm"] = "Đại học dược"
    elif values["trinh do cm"] == "cao đẳng":
        values["trinh do cm"] = "Cao đẳng dược"
    elif values["trinh do cm"] == "trung cấp":
        values["trinh do cm"] = "Trung cấp dược"

    values["trinh do tat"] = (
        values["trinh do cm"]
        .replace("Đại học dược", "đại học")
        .replace("Cao đẳng dược", "cao đẳng")
        .replace("Trung cấp dược", "trung cấp")
    )

    values["vi tri hanh nghe"] = values["dang ky pham vi"]
    values["thu tuc"] = "CAP CCHND"
    values["da nhan"] = "1"
    values["dia chi thuong tru"] = (
        values["dia chi thuong tru"][0:1].upper() + values["dia chi thuong tru"][1:]
    )
    df_cchnd.loc[mhs] = values
    df_cchnd.to_csv("cchnd.csv", index_label="ma ho so")
    ngay_bat_dau = datetime.strptime(values["ngay bat dau th"], "%d/%m/%Y")
    ngay_ket_thuc = datetime.strptime(values["ngay ket thuc th"], "%d/%m/%Y")
    if (
        datetime.strptime(ngay_tiep_nhan, "%d/%m/%Y %H:%M:%S") - ngay_ket_thuc
    ).days >= 365:
        show_message(
            "THÔNG BÁO",
            "Ngày tiếp nhận cách ngày kết thúc thực hành 1 năm trở lên",
        )
    ngay_tot_nghiep = datetime.strptime(values["ngay tot nghiep"], "%d/%m/%Y")

    def check_thoi_gian():
        if values["trinh do cm"] == "Đại học dược":
            if ngay_ket_thuc < max(ngay_bat_dau, ngay_tot_nghiep) + timedelta(days=730):
                show_message("THÔNG BÁO", "KHÔNG ĐỦ 730 NGÀY THỰC HÀNH")
        else:
            if "Tủ thuốc" in values["dang ky pham vi"]:
                if ngay_ket_thuc < max(ngay_bat_dau, ngay_tot_nghiep) + timedelta(
                    days=365
                ):
                    show_message("THÔNG BÁO", "KHÔNG ĐỦ 365 NGÀY THỰC HÀNH")
            else:
                if ngay_ket_thuc < max(ngay_bat_dau, ngay_tot_nghiep) + timedelta(
                    days=540
                ):
                    show_message("THÔNG BÁO", "KHÔNG ĐỦ 540 NGÀY THỰC HÀNH")

    check_thoi_gian()
    update_df_name_da_nhan(df_name, values, mhs)
    subprocess.run("wmctrl -xa file_hs.file_hs", shell=True)
    run_python_312("tach_nen")


if __name__ == "__main__":
    path = load_pickle(
        "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pickle_file/hs_path.pk"
    )
    mhs, _, _ = path.split("/")[-1].split("--")
    hs_cchnd(mhs)
