import subprocess
from god_class import setup_logging


@setup_logging("python312.log")
def run_python_312(name: str) -> None:
    # <PERSON><PERSON><PERSON> tra xem name đã c<PERSON> đuôi .py chưa để tránh trùng lặp
    file_name = name if name.endswith(".py") else f"{name}.py"

    _ = subprocess.run(
        [
            "/home/<USER>/python312/bin/python",
            f"/home/<USER>/Dropbox/hnd/python_script/{file_name}",
        ],
        check=True,
    )


if __name__ == "__main__":
    run_python_312("tach_nen.py")
