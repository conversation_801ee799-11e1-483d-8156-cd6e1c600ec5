import os
import time

import pandas as pd
from selenium import webdriver
from selenium.webdriver import Keys
from selenium.webdriver.chrome.service import Service as ChromeService
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as ec
from selenium.webdriver.support.wait import WebDriverWait
from webdriver_manager.chrome import ChromeDriverManager

from god_class import show_message
from top_most_get_text import input_dialog

os.chdir("/home/<USER>/Dropbox/hnd/csv_source")
soqd = input_dialog(
    "so qd", "NHẬP so qd\nBỎ TRỐNG LÀ CHƯA VÀO SỐ", "", 800, 400, 400, 400
)

df = pd.read_csv("ds_da_cham_dut.csv", dtype="str")

filter_df = None
if soqd is not None and soqd != "":
    filter_df = df[df["so qd cham dut"] == f"{soqd}/QĐ-SYT"]
    t = filter_df["so dkkd cd"].tolist()
else:
    filter_df = df[df["so qd cham dut"].isnull()]
    t = filter_df["so dkkd cd"].tolist()
if len(t) > 0:
    options = webdriver.ChromeOptions()
    options.add_experimental_option(
        "prefs",
        {
            "download.default_directory": "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pdf_documents/filehs",
            "download.prompt_for_download": False,
            "download.directory_upgrade": True,
            "plugins.always_open_pdf_externally": True,
        },
    )
    driver = webdriver.Chrome(
        service=ChromeService(ChromeDriverManager().install()), options=options
    )
    driver.maximize_window()
    driver.implicitly_wait(10)
    driver.get(
        "http://duocquocgia.com.vn/Application/Index#!/cosokinhdoanh/quanlytaikhoan"
    )
    driver.find_element(By.NAME, "usernameOrEmailAddress").send_keys("syt_vinhphuc")
    driver.find_element(By.NAME, "password").send_keys("@lovelyly92H")
    driver.find_element(By.XPATH, "//button[@type='submit']").click()
    WebDriverWait(driver, 30).until(
        ec.presence_of_element_located(
            (By.XPATH, "//span[contains(text(),'Sở y tế Tỉnh Vĩnh Phúc')]")
        )
    )
    driver.find_element(
        By.XPATH, "//div[@id='form-search-tai-khoan-co-so']/div[2]/div/input"
    ).send_keys(t[0])
    driver.find_element(
        By.XPATH, "//div[@id='form-search-tai-khoan-co-so']/div[2]/div/input"
    ).send_keys(Keys.ENTER)

    for i in range(1, len(t)):
        driver.execute_script("window.open('');")
        driver.switch_to.window(driver.window_handles[i])
        driver.get(
            "http://duocquocgia.com.vn/Application/Index#!/cosokinhdoanh/quanlytaikhoan"
        )
        WebDriverWait(driver, 30).until(
            ec.presence_of_element_located(
                (By.XPATH, "//span[contains(text(),'Sở y tế Tỉnh Vĩnh Phúc')]")
            )
        )
        driver.find_element(
            By.XPATH, "//div[@id='form-search-tai-khoan-co-so']/div[2]/div/input"
        ).send_keys(t[i] + "/")
        driver.find_element(
            By.XPATH, "//div[@id='form-search-tai-khoan-co-so']/div[2]/div/input"
        ).send_keys(Keys.ENTER)
    time.sleep(3600)
    driver.close()
else:
    show_message("THÔNG BÁO", "NHẬP SAI HOẶC ĐÃ VÀO SỐ")
    quit()
