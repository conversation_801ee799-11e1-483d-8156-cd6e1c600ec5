import subprocess
import sys

from PyQt5.QtCore import Qt
from PyQt5.QtWidgets import QApplication, QMessageBox


def run_script(path):
    # Chạy script Python
    subprocess.run(
        ["/home/<USER>/python313/bin/python", path],
        check=True,
    )


def run_script_dialog(WINDOWS_TITLE, QUESTION, path):
    app = QApplication(sys.argv)
    msgBox = QMessageBox()
    msgBox.setWindowTitle(WINDOWS_TITLE)
    msgBox.setText(QUESTION)
    msgBox.setStandardButtons(QMessageBox.Yes | QMessageBox.No)
    msgBox.setDefaultButton(QMessageBox.No)
    msgBox.setWindowFlags(msgBox.windowFlags() | Qt.WindowStaysOnTopHint)

    returns = msgBox.exec()
    if returns == QMessageBox.Yes:
        run_script(path)


import sys
from PyQt5.QtWidgets import QApplication, QDialog, QPushButton, QVBoxLayout


class StyledDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        # self.setWindowTitle("Styled Dialog")
        # self.setObjectName("MyCustomDialog")  # Đặt tên đối tượng để áp dụng style

        # Tạo layout và widget
        layout = QVBoxLayout()
        button = QPushButton("Click Me", self)
        layout.addWidget(button)
        self.setLayout(layout)

        # Áp dụng stylesheet
        # self.setStyleSheet("""
        #     QDialog#MyCustomDialog {
        #         background-color: #f0f0f0;
        #         border: 2px solid #333;
        #     }
        #     QPushButton {
        #         background-color: #4CAF50;
        #         color: white;
        #         padding: 5px;
        #     }
        # """)


if __name__ == "__main__":
    app = QApplication(sys.argv)
    dialog = StyledDialog()
    dialog.show()
    sys.exit(app.exec_())
