-- Hàm toggle REPL không phụ thuộc vào phiên debug
function toggle_debug_repl()
  local repl_buf = vim.fn.bufnr('dap-repl')
  
  if repl_buf ~= -1 and vim.api.nvim_buf_is_valid(repl_buf) then
    -- Tìm window hiển thị REPL
    local win_ids = vim.fn.win_findbuf(repl_buf)
    if #win_ids > 0 then
      -- REPL đang hiển thị, đóng lại
      for _, win_id in ipairs(win_ids) do
        vim.api.nvim_win_close(win_id, true)
      end
      return
    end
  end
  
  -- Mở REPL trong split hoặc float window
  dap.repl.open({
    height = math.floor(vim.o.lines * 0.4)
  })
end

-- Đặt phím tắt cho toggle REPL
vim.keymap.set('n', '<leader>dr', toggle_debug_repl, { desc = "Toggle Debug REPL" })

-- Tự động mở console khi có exception và giữ nó mở sau khi debug kết thúc
dap.listeners.after.event_stopped["show_exception"] = function(session, body)
  if body.reason == 'exception' or body.reason == 'error' then
    -- Hiển thị console
    dapui.float_element("console", {
      width = math.floor(vim.o.columns * 0.9),
      height = math.floor(vim.o.lines * 0.8),
      enter = true
    })
    console_visible = true
    
    -- Hiện chi tiết lỗi như đã cài đặt trước đó...
  end
end

-- Sửa lại listener kết thúc để KHÔNG đóng console
dap.listeners.after.event_terminated["keep_console"] = function()
  -- Không làm gì để giữ console mở
  -- Để người dùng tự đóng bằng toggle_debug_console khi cần
end 