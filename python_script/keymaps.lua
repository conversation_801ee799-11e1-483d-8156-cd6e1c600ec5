vim.keymap.set("n", "<leader>tp", function()
	-- <PERSON><PERSON>y nội dung từ clipboard
	local content = vim.fn.getreg("+")

	-- Thay thế \r\n hoặc \n hoặc \r thành \n\n
	content = content:gsub("\r\n", "\n\n")
	content = content:gsub("\r", "\n\n")
	content = content:gsub("\n", "\n\n")

	-- Lưu vào register "" và paste ngay lập tức
	vim.fn.setreg('"', content)
	vim.cmd("normal! p")

	vim.notify("Đã paste nội dung với khoảng cách đôi", vim.log.levels.INFO)
end, { desc = "Paste với khoảng cách đôi" })
