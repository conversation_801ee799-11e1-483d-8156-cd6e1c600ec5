import os
import sys
from unidecode import unidecode
from datetime import datetime

from loguru import logger

from all_pickle import load_pickle
from god_class import (
    get_current_date,
    convert_ngay,
    update_df_from_dict_by_index,
    update_df_name_cho_tra,
    show_message,
    TextProcess,
)
from create_snippets import creat_and_get


class Config:
    """Configuration class containing constants for the application."""

    COMPANY_DICT: dict[str, str] = {
        "gsk": "Công ty TNHH Dược phẩm GSK Việt Nam",
        "tnhh sanofi": "Công ty TNHH Sanofi-Aventis Việt Nam",
        "co phan duoc": "Công ty Cổ phần Dược - Trang thiết bị Y tế Bình Định (BIDIPHAR)",
        "sandoz": "Công ty TNHH Sandoz Việt Nam",
        "astra": "Công ty TNHH AstraZeneca Việt Nam",
        "merap": "Công ty Cổ phần Tập lđoàn MERAP",
        "merck": "Công ty TNHH Merck Healthcare Việt Nam",
        "sang": "Công ty TNHH thương mại và dược phẩm Sang",
        "imex": "Chi nhánh Công ty cổ phần Dược phẩm Imexpharm",
        "hoang duc": "Công ty TNHH dược phẩm và trang thiết bị Y tế Hoàng Đức",
    }

    EXCLUDED_COLUMNS: list[str] = [
        "ten cong ty",
        "ma ho so",
        "SỐ GTN SYT",
        "ngay",
    ]


class DataManager:
    def __init__(self):
        os.chdir("/home/<USER>/Dropbox/hnd/csv_source")
        from csv_load_and_export import CsvLoaderFactory

        self.df_name = CsvLoaderFactory.create_fillna_loader().load_df(
            "name", "ma ho so"
        )
        self.df_ht_gtt = CsvLoaderFactory.create_fillna_loader().load_df(
            "hoi_thao_gioi_thieu_thuoc", "ma ho so"
        )

    def get_update_columns(self) -> list[str]:
        return [
            col for col in self.df_ht_gtt.columns if col not in Config.EXCLUDED_COLUMNS
        ]


class PathParser:
    def __init__(self, pickle_path: str):
        self.path = load_pickle(pickle_path)
        self.mhs, self.thutuc, self.nguoi_dang_ky = self.path.split("/")[-1].split("--")


class CompanyNameFinder:
    def __init__(self, df_name, mhs: str, company_dict: dict[str, str]):
        self.df_name = df_name
        self.mhs = mhs
        self.company_dict = company_dict
        self.registrant = self.df_name.loc[mhs, "ten nguoi ptcm"]

    def find_company_name(self) -> str:
        try:
            company_name = [
                self.company_dict[key]
                for key in self.company_dict.keys()
                if key.lower() in unidecode(self.registrant).lower()
            ][0]
            if company_name == "":
                show_message("THÔNG BÁO", "Tên người đăng ký SAI")
                sys.exit()
            return company_name
        except IndexError:
            show_message(
                "THÔNG BÁO",
                f"Không tìm thấy công ty phù hợp với người đăng ký: {self.registrant}, dòng 89",
            )
            sys.exit()


class ThuocMatcher:
    def __init__(self, ten_thuoc: str, list_thuoc: list[str]):
        self.ten_thuoc = ten_thuoc.lower()
        self.list_thuoc = [x.lower() for x in list_thuoc]

    def find_match(self) -> str:
        matches = [thuoc for thuoc in self.list_thuoc if self.ten_thuoc in thuoc]

        if len(matches) == 0:
            show_message(
                "THÔNG BÁO", f"Không tìm thấy thuốc phù hợp với tên: {self.ten_thuoc}"
            )
            sys.exit()
        elif len(matches) > 1:
            show_message(
                "THÔNG BÁO", f"Tìm thấy nhiều thuốc phù hợp với tên: {self.ten_thuoc}"
            )
            sys.exit()
        else:
            return matches[0]


class DocumentProcessor:
    def __init__(self, data_manager: DataManager):
        self.data_manager = data_manager
        self.path_parser = PathParser(
            "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pickle_file/hs_path.pk"
        )
        self.dict_htt = {}

    def process(self):
        if self.path_parser.mhs in self.data_manager.df_ht_gtt.index:
            self._process_existing_record()
        else:
            self._process_new_record()

        self._update_common_fields()
        self._format_and_save()

    def _process_existing_record(self):
        from god_class import get_dict_from_index_df

        self.dict_htt = get_dict_from_index_df(
            self.data_manager.df_ht_gtt, self.path_parser.mhs
        )
        self.dict_htt = creat_and_get(self.dict_htt)

    def _process_new_record(self):
        company_finder = CompanyNameFinder(
            self.data_manager.df_name, self.path_parser.mhs, Config.COMPANY_DICT
        )
        ten_cong_ty = company_finder.find_company_name()
        df_filter = self.data_manager.df_ht_gtt[
            self.data_manager.df_ht_gtt["ten cong ty"] == ten_cong_ty
        ]
        list_thuoc = df_filter["TÊN THUỐC"].unique().tolist()

        if list_thuoc:
            self._process_existing_thuoc(df_filter, list_thuoc)
        else:
            # Chuyển list thành dict với giá trị rỗng
            columns_dict = {col: "" for col in self.data_manager.get_update_columns()}
            self.dict_htt = creat_and_get(columns_dict)

        self.dict_htt["ten cong ty"] = ten_cong_ty

    def _process_existing_thuoc(self, df_filter, list_thuoc):
        from god_class import get_dict_from_column_df

        # Tạo dict để nhập tên thuốc tắt
        thuoc_input_dict = {"Tên thuốc tắt": ""}
        thuoc_dict = creat_and_get(thuoc_input_dict)
        ten_thuoc = thuoc_dict["Tên thuốc tắt"]

        thuoc_matcher = ThuocMatcher(ten_thuoc, list_thuoc)
        ten_thuoc = thuoc_matcher.find_match()

        dict_thuoc = get_dict_from_column_df(df_filter, ten_thuoc, "TÊN THUỐC")
        defaul_val = {
            key: dict_thuoc[key] for key in self.data_manager.get_update_columns()
        }
        self.dict_htt = creat_and_get(defaul_val)

    def _update_common_fields(self):
        self.dict_htt["ma ho so"] = self.path_parser.mhs
        self.dict_htt["NGÀY GTN CT"] = convert_ngay(self.dict_htt["NGÀY GTN CT"])
        self.dict_htt["ten cong ty rut gon"] = (
            self.dict_htt["ten cong ty"]
            .replace("Công ty cổ phần", "CTCP")
            .replace("công ty cổ phần", "CTCP")
        )

        self._update_syt_number()
        self._update_dates()
        self._update_fax()

    def _update_syt_number(self):
        df = self.data_manager.df_ht_gtt
        stnsyt = (
            df["SỐ GTN SYT"].iloc[-1]
            if df["SỐ GTN SYT"].iloc[-1] != ""
            else df["SỐ GTN SYT"].iloc[-2]
        )
        lstday = (
            df["ngay"].iloc[-1] if df["ngay"].iloc[-1] != "" else df["ngay"].iloc[-2]
        )

        thisyear = datetime.today().strftime("%Y")
        if self.path_parser.mhs not in df.index:
            # Kiểm tra format của lstday trước khi split
            try:
                lstday_parts = lstday.split("/")
                if len(lstday_parts) >= 3:
                    lstday_year = lstday_parts[2]
                else:
                    # Nếu format không đúng, mặc định là năm khác
                    lstday_year = "0000"

                if thisyear == lstday_year:
                    # Kiểm tra format của stnsyt trước khi split
                    stnsyt_parts = stnsyt.split("/")
                    if len(stnsyt_parts) >= 1 and stnsyt_parts[0].isdigit():
                        self.dict_htt["stnsytnew"] = str(
                            int(stnsyt_parts[0]) + 1
                        ).zfill(2)
                    else:
                        self.dict_htt["stnsytnew"] = "01"
                else:
                    self.dict_htt["stnsytnew"] = "01"
            except (IndexError, ValueError, AttributeError):
                # Nếu có lỗi bất kỳ, mặc định là 01
                self.dict_htt["stnsytnew"] = "01"
        else:
            self.dict_htt["stnsytnew"] = df.at[self.path_parser.mhs, "SỐ GTN SYT"]

    def _update_dates(self):
        (
            self.dict_htt["ngay"],
            self.dict_htt["thang"],
            self.dict_htt["nam"],
            self.dict_htt["today"],
        ) = get_current_date()
        self.dict_htt["SỐ GTN SYT"] = self.dict_htt["stnsytnew"]
        self.dict_htt["SỐ GTN CT"] = self.dict_htt["SỐ GTN CT"].replace("_", r"\_")

    def _update_fax(self):
        self.dict_htt["fax"] = (
            f"; fax: {self.dict_htt['fax']}" if self.dict_htt["fax"] else ""
        )

    def _format_and_save(self):
        formatter = TextProcess("hoi_thao_gt_thuoc")
        formatter.format_text(self.dict_htt)
        formatter.compile_latex()
        formatter.open_pdf_with_mupdf()
        formatter.copy_pdf_to_path(
            f"/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pdf_documents/kq/{self.dict_htt['ten cong ty']}-{self.path_parser.mhs}.pdf"
        )

        update_df_from_dict_by_index(
            self.data_manager.df_ht_gtt,
            "hoi_thao_gioi_thieu_thuoc.csv",
            self.dict_htt,
            self.path_parser.mhs,
        )
        update_df_name_cho_tra(
            self.data_manager.df_name, self.dict_htt, self.path_parser.mhs
        )


@logger.catch
def hoi_thao_gt_thuoc(mhs: str = None):
    """Hàm main để xử lý hồ sơ hội thảo giới thiệu thuốc.

    Args:
        mhs: Mã hồ sơ (tùy chọn)
    """
    data_manager = DataManager()
    processor = DocumentProcessor(data_manager)
    processor.process()


if __name__ == "__main__":
    hoi_thao_gt_thuoc()
