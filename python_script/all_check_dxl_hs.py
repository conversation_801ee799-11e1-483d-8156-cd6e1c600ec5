import os

import pandas as pd
import telepot
from loguru import logger
from selenium.webdriver.common.by import By

from god_class import update_with_vlookup, expand_ds, TelegramSend, selenium_start
from god_class import send_notification


def get_selenium(chrome):
    all_links = chrome.find_elements(By.TAG_NAME, "td")
    matching_links = [link.text for link in all_links]
    rows = []
    row = []
    for line in matching_links:
        if line.strip().isdigit():
            if row:
                rows.append(row)
            row = [line.strip()]
        elif line.strip() != "Toggle Dropdown":
            row.append(line.strip())

    # Thêm dòng cuối cùng vào danh sách dữ liệu
    if row:
        rows.append(row)

    # Tạo DataFrame từ danh sách dữ liệu
    columns = [
        "stt",
        "ma ho so",
        "ve viec",
        "ten nguoi ptcm",
        "dia chi",
        "HẠN XỬ LÝ",
        "TIME",
        "THANH TOÁN LỆ PHÍ",
        "CÁN BỘ CHUYỂN",
        "LỆ PHÍ",
        "trang thai",
    ]
    df = pd.DataFrame(rows[1:], columns=columns)
    chrome.quit()
    return df


def convert_mhs_col(df_selenium):
    df_selenium["ma ho so"] = df_selenium["ma ho so"].str.split(" ").str[0]
    df_selenium["ma ho so"] = df_selenium["ma ho so"].str.strip()


def pd_to_datetime(value, format="%d/%m/%Y %H:%M:%S", dayfirst=True):
    try:
        return pd.to_datetime(value, format=format, dayfirst=dayfirst)
    except ValueError:
        return value


def format_to_datetime(value, format="%d/%m/%Y %H:%M:%S"):
    try:
        return pd.to_datetime(value).strftime(format)
    except ValueError:
        return value


def sort_by_han(df, col):
    df.sort_values(
        by=col,
        key=lambda x: pd.to_datetime(x, format="%d/%m/%Y %H:%M:%S", errors="coerce"),
    )

    # Dịch chuyển các giá trị không phù hợp xuống cuối
    df.sort_values(
        by=col,
        key=lambda x: pd.to_datetime(x, format="%d/%m/%Y %H:%M:%S", errors="coerce"),
        na_position="last",
        inplace=True,
    )


def create_report_and_send(df):
    report = "DANH SÁCH HỒ SƠ CHƯA TRẢ KẾT QUẢ\n\n"
    df = df.reset_index(drop=True)  # Reset index
    for index, row in df.iterrows():
        line = f'{index + 1}. {row["ve viec"]}--{row["ma ho so"]}--{row["ten nguoi ptcm"]}--HẠN XỬ LÝ: {row["HẠN XỬ LÝ"]}\n\n'
        report += line
    tele = TelegramSend("hnd")
    tele.send_message_warp(report)


@logger.catch
def run_main():
    send_notification("start: check dxl")
    os.chdir("/home/<USER>/Dropbox/hnd/csv_source")
    api = "2143046655:AAE5iwz9KY8ofLZ_Vm3xhBrjpEyILDYzRy8"  # telegram token
    df_name = pd.read_csv("name.csv", dtype=str)
    chatid = "-1001512252982"
    bot = telepot.Bot(api)
    driver = selenium_start("DA_XU_LY")
    expand_ds(driver)
    df_dxl = get_selenium(driver)
    convert_mhs_col(df_dxl)
    update_with_vlookup(df_dxl, df_name, "ma ho so", "han", "HẠN XỬ LÝ")
    update_with_vlookup(df_dxl, df_name, "ma ho so", "thu tuc", "ve viec")
    pd_to_datetime(df_dxl, "HẠN XỬ LÝ")
    sort_by_han(df_dxl, "HẠN XỬ LÝ")
    format_to_datetime(df_dxl, "HẠN XỬ LÝ")
    df_dxl = df_dxl[["stt", "ma ho so", "ve viec", "ten nguoi ptcm", "HẠN XỬ LÝ"]]
    create_report_and_send(df_dxl, bot, chatid)


if __name__ == "__main__":
    run_main()
