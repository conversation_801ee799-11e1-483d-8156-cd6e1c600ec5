#!/usr/bin/env python
# -*- coding: utf-8 -*-

import pandas as pd
from top_most_get_text import input_dialog
from god_class import TextProcess, dataframe_to_latex, insert_stt
from datetime import datetime

import os


def format_date_input(date_str: str) -> str:
    """
    Định dạng lại chuỗi ngày tháng để phù hợp với format %d/%m/%Y.

    Parameters
    ----------
    date_str : str
        Chuỗi ngày tháng đầu vào (có thể là '18042025' hoặc '18/04/2025')

    Returns
    -------
    str
        Chuỗi ngày tháng đã được định dạng (dạng '18/04/2025')
    """
    # Loại bỏ khoảng trắng
    date_str = date_str.strip()

    # Nếu đã có định dạng d/m/Y hoặc d-m-Y, chuyển về d/m/Y
    if "/" in date_str or "-" in date_str:
        # Thay thế dấu - bằng dấu / nếu có
        date_str = date_str.replace("-", "/")
        return date_str

    # Trường hợp không có dấu / và có đúng 8 ký tự (ddmmyyyy)
    if len(date_str) == 8 and date_str.isdigit():
        return f"{date_str[0:2]}/{date_str[2:4]}/{date_str[4:8]}"

    # Trường hợp không có dấu / và có đúng 6 ký tự (ddmmyy)
    if len(date_str) == 6 and date_str.isdigit():
        return f"{date_str[0:2]}/{date_str[2:4]}/20{date_str[4:6]}"

    # Trả về nguyên mẫu nếu không xử lý được
    return date_str


os.chdir("/home/<USER>/Dropbox/hnd/csv_source")
df = pd.read_csv(
    "co_so_ban_le.csv",
    dtype=str,
    usecols=[
        "ten qt-nt",
        "dia chi co so",
        "noi nhan",
        "ten nguoi ptcm",
        "ngay_lam",
        "so dt chu hs",
    ],
)
mask = df["ngay_lam"].notna()
mask2 = df["noi nhan"].str.upper().str.contains("S")
mask3 = df["noi nhan"].str.upper().str.contains("L")
df = df[mask & (mask2 | mask3)]
df.loc[df["noi nhan"].str.upper().str.contains("S"), "noi nhan"] = "SON"
df.loc[df["noi nhan"].str.upper().str.contains("L"), "noi nhan"] = "LAN"

df["ngay_lam"] = pd.to_datetime(df["ngay_lam"], format="%d/%m/%Y")

# Hiển thị hộp thoại nhập ngày và xử lý định dạng ngày
ngay_tinh = input_dialog(
    "Lọc dữ liệu", "Nhập ngày tính (dd/mm/yyyy hoặc ddmmyyyy):", ""
)

# Kiểm tra nếu người dùng không nhập gì, sử dụng ngày mặc định
if not ngay_tinh or ngay_tinh.strip() == "":
    print("Không có ngày tính được nhập, sử dụng tất cả dữ liệu.")
    filtered_df = df
else:
    # Định dạng lại ngày tháng từ input
    formatted_date = format_date_input(ngay_tinh)
    try:
        ngay_tinh = pd.to_datetime(formatted_date, format="%d/%m/%Y")
        print(f"Lọc dữ liệu từ ngày: {ngay_tinh.strftime('%d/%m/%Y')}")
        mask = df["ngay_lam"].dt.date >= ngay_tinh.date()
        filtered_df = df[mask]
    except ValueError as e:
        print(f"Lỗi định dạng ngày: {e}")
        print("Sử dụng tất cả dữ liệu.")
        filtered_df = df

# Xử lý dữ liệu đã lọc
filtered_df = filtered_df.sort_values(
    by=["noi nhan", "ngay_lam"], ascending=[False, True]
)
filtered_df["ngay_lam"] = filtered_df["ngay_lam"].dt.strftime("%d/%m/%Y")
insert_stt(filtered_df)
formatter = TextProcess("template_hs")
values = {}
values["bang"] = dataframe_to_latex(filtered_df)

formatter.format_text(values)
formatter.compile_latex()

formatter.copy_pdf_to_kq_and_send_hnd(
    f"thong_ke_hs_den_ngay_{datetime.now().strftime('%d-%m-%Y')}"
)
