{
 "cells": [
  {
   "cell_type": "code",
   "execution_count": 112,
   "id": "19427cd2a75cb7de",
   "metadata": {
    "ExecuteTime": {
     "end_time": "2024-04-16T07:34:15.579786Z",
     "start_time": "2024-04-16T07:34:15.551251Z"
    }
   },
   "outputs": [],
   "source": [
    "import os\n",
    "\n",
    "import pandas as pd\n",
    "\n",
    "os.chdir('/home/<USER>/Dropbox/hnd/latexall/')\n",
    "df = pd.read_csv('CẤP GPHD CHO TTYT.csv', dtype=str)"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 113,
   "id": "13214e0612024640",
   "metadata": {
    "ExecuteTime": {
     "end_time": "2024-04-16T07:34:15.993269Z",
     "start_time": "2024-04-16T07:34:15.990320Z"
    }
   },
   "outputs": [],
   "source": [
    "df_short = df"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 114,
   "id": "970cc698ef511b35",
   "metadata": {
    "ExecuteTime": {
     "end_time": "2024-04-16T07:34:16.018561Z",
     "start_time": "2024-04-16T07:34:16.012873Z"
    }
   },
   "outputs": [],
   "source": [
    "\n",
    "import shutil\n",
    "import subprocess\n",
    "\n",
    "\n",
    "def compile_latex(text, ten_file_pdf):\n",
    "    os.chdir('/home/<USER>/Dropbox/hnd/latexall')\n",
    "    f = open(f\"{ten_file_pdf}.tex\", \"w\", encoding=\"utf-8\")\n",
    "    f.write(text)\n",
    "    f.close()\n",
    "    try:\n",
    "        subprocess.run([\"pdflatex\", f\"{ten_file_pdf}.tex\"])\n",
    "    except Exception as e:\n",
    "        print(e)\n",
    "        pass\n",
    "    shutil.copy(f\"{ten_file_pdf}.pdf\", f\"/home/<USER>/Dropbox/hnd/latexall/giay_phep/{ten_file_pdf}.pdf\")\n",
    "    subprocess.Popen([\"okular\", f\"/home/<USER>/Dropbox/hnd/latexall/giay_phep/{ten_file_pdf}.pdf\"])\n"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 115,
   "id": "51db3f2ca87b3df6",
   "metadata": {
    "ExecuteTime": {
     "end_time": "2024-04-16T07:34:35.192439Z",
     "start_time": "2024-04-16T07:34:16.027511Z"
    }
   },
   "outputs": [],
   "source": [
    "from god import convert_ngay\n",
    "\n",
    "tram_vp_xuoc = ['00375', '00372', '00363', '00360', '00375', '00372', '00363', '00359']\n",
    "tram_them = ['00362']\n",
    "so_gp = 356\n",
    "for index, value in df_short.iterrows():\n",
    "    so_gp += 1\n",
    "    so_gphd = f'00{so_gp}'\n",
    "    ngay, thang, nam = value['NGÀY'].split('/')\n",
    "    so_cchn = value['SỐ CHỨNG CHỈ HÀNH NGHỀ']\n",
    "    ten_cs = 'Trạm Y tế ' + value['TÊN CƠ SỞ'].split(',')[0]\n",
    "    ten_cs = ten_cs.upper()\n",
    "    nguoi_ptcm = value['ten nguoi ptcm'].title()\n",
    "    ngay_cap = convert_ngay(value['NGÀY CẤP'])\n",
    "    dia_diem = value['ĐỊA ĐIỂM'].replace('TDP', 'Tổ dân phố')\n",
    "\n",
    "    if len(dia_diem) < 59:\n",
    "        gian_dong = 1.5\n",
    "    else:\n",
    "        gian_dong = 1.35\n",
    "    if so_gphd in tram_vp_xuoc:\n",
    "        dia_diem = dia_diem.replace('tỉnh Vĩnh Phúc', r'\\\\tỉnh Vĩnh Phúc')\n",
    "    elif so_gphd in tram_them:\n",
    "        gian_dong = 1.5\n",
    "    text = fr'''\n",
    "\\documentclass{{article}}\n",
    "\\usepackage[utf8]{{inputenc}}\n",
    "\\usepackage[T5]{{fontenc}}\n",
    "\\nonstopmode\n",
    "\\usepackage{{amsmath}} \n",
    "\\usepackage{{graphicx}}\n",
    "\\usepackage[a4paper, landscape,right=2cm,left=2cm,top=2.5cm,bottom=2cm]{{geometry}}\n",
    "\\usepackage[fontsize=14pt]{{scrextend}}\n",
    "\\usepackage{{times}}\n",
    "\\usepackage{{setspace}}\n",
    "\\usepackage{{eso-pic}}\n",
    "\\usepackage{{parskip}}\n",
    "\\setlength{{\\parskip}}{{6pt}}\n",
    "\\usepackage{{tabularray}}\n",
    "\\usepackage{{ulem}}\n",
    "\\renewcommand{{\\ULdepth}}{{5pt}}\n",
    "\\renewcommand{{\\ULthickness}}{{0.6pt}}\n",
    "\\setlength{{\\parindent}}{{0pt}}\n",
    "\n",
    "\\begin{{document}}
}
\n",
    "\n",
    "\n",
    "\\setstretch{{1.2}}\n",
    "\\pagestyle{{empty}}\n",
    "\\AddToShipoutPictureBG{{\\includegraphics[width=\\paperwidth, height=\\paperheight]{{GPHD KCB}}}}\n",
    "\n",
    "\\begin{{tblr}}{{width=1\\textwidth,\n",
    "        colspec={{X[1.7,c] X[4,c] X[1.7,c]}},\n",
    "        colsep=0pt,row{{2}}={{font=\\bfseries}},\n",
    "        rowsep=0pt}}\n",
    "    UBND TỈNH VĨNH PHÚC                        & \\textbf{{CỘNG HÒA XÃ HỘI  CHỦ NGHĨA VIỆT NAM}} & \\\\\n",
    "    \\textbf{{\\uline{{SỞ Y TẾ}}}}                   & \\uline{{Độc lập - Tự do - Hạnh Phúc}}          & \\\\[10pt]\n",
    "    Số: {{\\color{{red}} \\bfseries {so_gphd}}} /VP-GPHĐ &                                                \\\\\n",
    "\\end{{tblr}}
\end{{minipage}}\n",
    "\n",
    "\\vspace{{-0.3cm}}\n",
    "\n",
    "\\begin{{center}}\n",
    "    \\color{{red}} \\bfseries  \\fontsize{{20pt}}{{0pt}}\\selectfont\n",
    "    GIẤY PHÉP HOẠT ĐỘNG\\\\\n",
    "    KHÁM BỆNH, CHỮA BỆNH\\\\\n",
    "    \\vspace*{{0.2cm}}\n",
    "    \\fontsize{{14pt}}{{0pt}}\\selectfont \\color{{black}}\n",
    "    GIÁM ĐỐC SỞ Y TẾ\\\\\n",
    "\\end{{center}}\n",
    "\n",
    "\n",
    "Căn cứ Luật Khám bệnh, chữa bệnh ngày 23 tháng 11 năm 2009;\\\\\n",
    "Xét đề nghị của Trưởng phòng Nghiệp vụ Dược, Sở Y tế.\n",
    "\\vspace{{0.2cm}}\n",
    "\n",
    "\n",
    "\n",
    "\\begin{{center}}\n",
    "    \\setstretch{{1.4}}\n",
    "    \\bfseries CẤP PHÉP HOẠT ĐỘNG KHÁM BỆNH, CHỮA BỆNH\\\\\n",
    "    \\textit{{}}\n",
    "\\end{{center}}\n",
    "\n",
    "\\vspace{{-0.6cm}}\n",
    "\n",
    "\n",
    "\\setlength{{\\parskip}}{{0pt}}\n",
    "\\setstretch{{{gian_dong}}} %gian dòng\n",
    "Tên cơ sở khám bệnh, chữa bệnh: \\textbf{{{ten_cs}}}\\par\n",
    "{{\\centering \\bfseries\\par}}\n",
    "\\begin{{minipage}}[t]{{0.65\\textwidth}}\n",
    "    Tên người chịu trách nhiệm chuyên môn kỹ thuật: \\textbf{{Bác sĩ {nguoi_ptcm}}}\n",
    "\n",
    "    Số chứng chỉ hành nghề: {so_cchn}/VP-CCHN. Ngày cấp: {ngay_cap}\n",
    "\n",
    "    Nơi cấp: Sở Y tế tỉnh Phú Thọ\n",
    "\n",
    "    Hình thức tổ chức: Trạm Y tế cấp xã\n",
    "\n",
    "    Địa điểm hành nghề: {dia_diem}\n",
    "\n",
    "    Phạm vi hoạt động chuyên môn: Thực hiện các kỹ thuật chuyên môn được Giám đốc\\\\Sở Y tế phê duyệt ban hành kèm theo Giấy phép hoạt động khám bệnh, chữa bệnh\n",
    "\n",
    "    Thời gian làm việc hằng ngày: 24h/24h, 07 ngày/tuần.\n",
    "\n",
    "    \n",
    "\\end{{minipage}}\\hfill\n",
    "\\begin{{minipage}}[t]{{0.35\\textwidth}}\\setstretch{{1.2}}\n",
    "    \n",
    "    \\begin{{center}}\n",
    "        \\textit{{Phú Thọ, ngày {ngay} tháng {thang} năm  {nam}}}\\\\\n",
    "        \\textbf{{KT. GIÁM ĐỐC}}\\\\\n",
    "        \\textbf{{PHÓ GIÁM ĐỐC}}\\\\\n",
    "        \\vspace{{3.5cm}}\n",
    "        \\textbf{{Nguyễn Đắc Ca}}\\\\\n",
    "    \\end{{center}}\n",
    "\\end{{minipage}}\n",
    "\\end{{document}}\n",
    "'''\n",
    "    compile_latex(text, ten_cs)\n",
    "\n",
    "\n",
    "\n",
    "\n"
   ]
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3",
   "language": "/home/<USER>/python313/bin/python",
   "name": "/home/<USER>/python313/bin/python"
  },
  "language_info": {
   "codemirror_mode": {
    "name": "ipython",
    "version": 2
   },
   "file_extension": ".py",
   "mimetype": "text/x-python",
   "name": "/home/<USER>/python313/bin/python",
   "nbconvert_exporter": "/home/<USER>/python313/bin/python",
   "pygments_lexer": "ipython2",
   "version": "2.7.6"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 5
}
