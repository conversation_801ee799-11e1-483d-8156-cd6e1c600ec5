import os
from datetime import datetime, timedelta
from god_class import (
    dataframe_to_latex,
    TextProcess,
    get_current_date,
    insert_stt,
)
from csv_load_and_export import CsvLoaderFactory


class GPPExpirationNotifier:
    def __init__(self, csv_path="/home/<USER>/Dropbox/hnd/csv_source"):
        """Khởi tạo class với đường dẫn đến thư mục chứa file CSV"""
        self.csv_path = csv_path
        os.chdir(self.csv_path)
        self.df_gpp = None
        self.filtered_df = None
        self.values = {}

    def load_data(self):
        """Load dữ liệu từ file CSV"""
        self.df_gpp = CsvLoaderFactory.create_datetime_loader(
            ["ngay het han gpp", "ngay qd", "ngay cap cchnd"]
        ).load_df("du_lieu_gpp_all")
        self.df_name = CsvLoaderFactory.create_basic_loader().load_df("name")

    def filter_expiring_licenses(self, min_days=0, max_days=30):
        """<PERSON>ọc c<PERSON>c <PERSON>P sắp hết hạn trong khoảng thời gian"""
        now = datetime.now()
        mask1 = self.df_gpp["ngay het han gpp"] >= now + timedelta(days=min_days)
        mask2 = self.df_gpp["ngay het han gpp"] <= now + timedelta(days=max_days)

        list_cchnd_da_nop_hs = self.df_name["so cchnd"].to_list()
        mask3 = ~self.df_gpp["so cchnd"].isin(list_cchnd_da_nop_hs)

        self.filtered_df = self.df_gpp.loc[mask1 & mask2 & mask3]

    def process_data(self):
        """Xử lý và định dạng dữ liệu"""
        self.filtered_df = self.filtered_df.sort_values(by="ngay het han gpp")
        self.filtered_df["ngay het han gpp"] = self.filtered_df[
            "ngay het han gpp"
        ].dt.strftime("%d/%m/%Y")

        insert_stt(self.filtered_df)
        self.filtered_df["so cchnd"] = self.filtered_df["so cchnd"].apply(
            lambda x: x + "/CCHND-" + "\par SYT-VP" if "-" not in x else x
        )
        self.filtered_df["ten qt-nt"] = (
            self.filtered_df["ten qt-nt"].str.title().str.replace("Thuốc", "thuốc")
        )
        self.filtered_df["so gpp"] = self.filtered_df["so gpp"] + "/GPP"
        self.filtered_df["ten nguoi ptcm"] = self.filtered_df[
            "ten nguoi ptcm"
        ].str.title()

        self.filtered_df = self.filtered_df[
            [
                "stt",
                "ten qt-nt",
                "dia chi co so",
                "ten nguoi ptcm",
                "so cchnd",
                "so gpp",
                "ngay het han gpp",
            ]
        ]

    def prepare_document_values(self):
        """Chuẩn bị các giá trị cho văn bản"""
        (
            self.values["ngay"],
            self.values["thang"],
            self.values["nam"],
            self.values["today"],
        ) = get_current_date()
        self.values["so_cs"] = str(self.filtered_df.shape[0]).zfill(2)
        self.values["clip"] = dataframe_to_latex(self.filtered_df)

    def generate_document(self):
        """Tạo văn bản thông báo"""
        text = TextProcess("gpp_thong_bao_den_han")
        text.format_text(self.values)
        tieude = "Yêu cầu nộp hồ sơ đề nghị đánh giá định kỳ việc duy trì đáp ứng Thực hành tốt cơ sở bán lẻ thuốc năm 2025"
        text.auto_day_van_ban(tieude, "TB", "0")

    def run(self):
        """Chạy toàn bộ quy trình"""
        self.load_data()
        self.filter_expiring_licenses()
        self.process_data()
        self.prepare_document_values()
        self.generate_document()


if __name__ == "__main__":
    notifier = GPPExpirationNotifier()
    notifier.run()
