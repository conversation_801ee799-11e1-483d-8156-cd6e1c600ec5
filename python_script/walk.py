import glob
import os

from god_class import send_notification


def remove_file_by_extensions_direct_folder(dir_name):
    extensions = (
        ".docx",
        ".PDF",
        ".webm",
        ".srt",
        ".docx",
        ".~lock.*#",
        ".part",
        ".pdf",
        ".doc",
        ".zip",
        ".msi",
        ".exe",
        ".jpeg",
        ".jpg",
        ".png",
        ".rar",
        ".log",
        ".xls",
        ".xlsx",
        ".aux",
        ".gz",
        ".fdb_latexmk",
        ".fls",
        ".tgz",
        ".log~",
        ".out",
        ".txt",
        ".bbl",
        ".blg",
        ".tex#",
        ".tex~",
        ".pdf",
        ".torrent",
    )
    for ext in extensions:
        for file in glob.glob(dir_name + "/*" + ext):
            os.remove(file)


def cleanup_latex_files_deep(directory):
    latex_file_extensions = [
        ".aux",
        ".log~",
        ".log",
        ".out",
        ".gz",
        ".bbl",
        ".toc",
        ".blg",
        ".fls",
        ".fdb_latexmk",
        ".tex#",
        ".tex~",
        ".pdf",
    ]

    for root, _, files in os.walk(directory):
        if "pdf_documents" in root.split(os.path.sep):
            continue  # Bỏ qua toàn bộ cây thư mục chứa 'pdf_documents'
        for file in files:
            if any(file.endswith(ext) for ext in latex_file_extensions):
                file_path = os.path.join(root, file)
                os.remove(file_path)


def remove_latex_files_direct_folder(dir_name):
    latex_files = glob.glob(dir_name + "/*.tex")
    for file in latex_files:
        os.remove(file)


def remove_empty_files(directory):
    for root, _, files in os.walk(directory):
        for file in files:
            file_path = os.path.join(root, file)
            try:
                if os.path.exists(file_path) and os.path.getsize(file_path) == 0:
                    os.remove(file_path)
            except (FileNotFoundError, OSError):
                continue  # Bỏ qua nếu file không tồn tại hoặc không thể truy cập


remove_empty_files("/home/<USER>/Dropbox/hnd")

remove_latex_files_direct_folder("/home/<USER>/Dropbox")
remove_file_by_extensions_direct_folder("/home/<USER>/Pcloud_ssd/Pcloud/Zalo_Download")
remove_file_by_extensions_direct_folder("/home/<USER>/Pcloud_ssd/Pcloud/scanned_pdf")


remove_file_by_extensions_direct_folder("/home/<USER>/Pcloud_ssd/Pcloud")
remove_file_by_extensions_direct_folder("/home/<USER>")
remove_file_by_extensions_direct_folder("/home/<USER>/Desktop")
remove_file_by_extensions_direct_folder("/home/<USER>/Dropbox")
remove_file_by_extensions_direct_folder("/home/<USER>/Downloads/AyuGram Desktop")
remove_file_by_extensions_direct_folder("/home/<USER>/Downloads")
remove_file_by_extensions_direct_folder("/home/<USER>/Dropbox/hnd/csv_source")
remove_file_by_extensions_direct_folder(
    "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pdf_documents/file_hs_online"
)

cleanup_latex_files_deep("/home/<USER>/Dropbox/hnd/latexall")
cleanup_latex_files_deep("/home/<USER>/Pcloud_ssd/Pcloud/HND_Document")
cleanup_latex_files_deep("/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pdf_documents/kq")


# Thay 'latexall' bằng đường dẫn thực tế tới thư mục chứa file LaTeX của bạn
def remove_conflict_files(directory):
    for root, _, files in os.walk(directory):
        for file in files:
            if "conflict" in file.lower():
                file_path = os.path.join(root, file)
                try:
                    os.remove(file_path)
                except (FileNotFoundError, OSError):
                    continue


remove_conflict_files("/home/<USER>/Dropbox/hnd/.git/refs/heads")

send_notification("walk: done")
