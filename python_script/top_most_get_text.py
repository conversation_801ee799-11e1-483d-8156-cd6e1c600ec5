import subprocess
import sys

from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont
from PyQt5.QtWidgets import (
    QApplication,
    QInputDialog,
    QDialog,
    QVBoxLayout,
    QLabel,
    QTextEdit,
    QPushButton,
)


def input_dialog(title, question, default_text, width=500, height=500):
    app = QApplication.instance()
    if app is None:
        app = QApplication([])

    # Tạo timer để chạy script sau 0.5s
    timer = QTimer()
    timer.setSingleShot(True)
    timer.timeout.connect(
        lambda: subprocess.run(["/home/<USER>/Dropbox/hnd/other_script/switch_vn.sh"])
    )
    timer.start(500)  # 500ms = 0.5s

    # Tạo một hộp thoại nhập liệu
    input_string = QInputDialog()
    input_string.setWindowTitle(title)
    input_string.setLabelText(question)
    input_string.setTextValue(default_text)

    # Thiết lập objectName để có thể nhận dạng là "input.py"
    # Thiết lập thuộc tính "always on top"
    input_string.setWindowFlag(Qt.WindowType.WindowStaysOnTopHint)

    # Đặt kích thước cửa sổ
    input_string.setFixedSize(width, height)

    # Đặt font
    font = QFont()
    font.setPointSize(16)  # Bạn có thể thay đổi giá trị này để thay đổi kích thước font
    input_string.setFont(font)

    # Di chuyển hộp thoại tới vị trí chỉ định

    # Hiển thị hộp thoại và lấy giá trị trả về
    ok = input_string.exec()

    if ok == QInputDialog.DialogCode.Accepted:
        # Trả về giá trị nhập vào nếu người dùng bấm "OK"
        return input_string.textValue()
    else:
        # Trả về None nếu người dùng bấm "Cancel"
        return None


def input_dialog_wrap(
    title, question, defaulttext, xposition, yposition, width, height
):
    app = QApplication.instance()
    if app is None:
        app = QApplication([])

    # Tạo một hộp thoại
    dialog = QDialog()
    dialog.setWindowTitle(title)

    # Tạo layout
    layout = QVBoxLayout()

    # Tạo label và thêm vào layout
    label = QLabel(question)
    layout.addWidget(label)

    # Tạo text edit, thiết lập giá trị mặc định và thêm vào layout
    text_edit = QTextEdit()
    text_edit.setPlainText(defaulttext)
    layout.addWidget(text_edit)

    # Tạo nút lưu và thêm vào layout
    save_button = QPushButton("Save")
    layout.addWidget(save_button)
    # Tạo nút hủy và thêm vào layout
    cancel_button = QPushButton("Cancel")
    layout.addWidget(cancel_button)

    # Thiết lập layout cho dialog
    dialog.setLayout(layout)

    # Cài đặt kích thước và vị trí cho dialog
    dialog.setGeometry(xposition, yposition, width, height)

    # Cài đặt font
    font = QFont()
    font.setPointSize(16)  # Có thể điều chỉnh kích thước font tại đây
    dialog.setFont(font)

    def keyPressEvent(self, event):
        if event.key() in (Qt.Key_Return, Qt.Key_Enter):
            self.on_save()

    # Hàm được gọi khi nút save được nhấn

    def on_save():
        dialog.accept()

        # Hàm được gọi khi nút cancel được nhấn

    def on_cancel():
        dialog.reject()
        sys.exit()

        # Kết nối signal của các nút với các hàm tương ứng

    save_button.clicked.connect(on_save)
    cancel_button.clicked.connect(on_cancel)

    # Hiển thị dialog và chờ cho tới khi có hành động từ người dùng
    result = dialog.exec()

    # Kiểm tra kết quả và trả về nội dung từ QTextEdit
    if result == QDialog.Accepted:
        return text_edit.toPlainText()
    else:
        return None


def input_zenity(title, question, default_value):
    command = [
        "zenity",
        "--entry",
        "--width=400",
        "--height=200",
        f"--text={question}",
        f"--title={title}",
        f"--entry-text={default_value}",
    ]
    result = subprocess.run(command, capture_output=True, text=True)
    user_input = result.stdout.strip()
    return user_input


if __name__ == "__main__":
    input_dialog("Title", "Question", "Default Text")
