import os
import re


def convert_srt_to_text(input_file, output_file):
    # Đọc nội dung file .srt
    with open(input_file, "r", encoding="utf-8") as f:
        content = f.read()

    # Tách các phần subtitle bằng dòng trống
    subtitle_blocks = content.strip().split("\n\n")

    # Lấy chỉ phần text, bỏ qua số thứ tự và timestamp
    text_lines = []
    for block in subtitle_blocks:
        lines = block.split("\n")
        # Bỏ qua dòng số thứ tự và timestamp
        text = " ".join(lines[2:])
        text_lines.append(text)

    # <PERSON><PERSON>t hợp các dòng text thành một đoạn văn
    full_text = " ".join(text_lines)

    # Ghi ra file output
    with open(output_file, "w", encoding="utf-8") as f:
        f.write(full_text)


def process_directory(root_dir):
    # Duyệt qua tất cả các thư mục và file
    for dirpath, dirnames, filenames in os.walk(root_dir):
        # <PERSON><PERSON><PERSON> l<PERSON> c<PERSON>c file .srt
        srt_files = [f for f in filenames if f.endswith(".srt")]

        for srt_file in srt_files:
            input_path = os.path.join(dirpath, srt_file)
            # Tạo tên file output bằng cách thay .srt thành .txt
            output_file = srt_file.replace(".srt", ".txt")
            output_path = os.path.join(dirpath, output_file)

            try:
                convert_srt_to_text(input_path, output_path)
                print(f"Đã chuyển đổi {input_path} thành {output_path}")
            except Exception as e:
                print(f"Lỗi khi xử lý file {input_path}: {str(e)}")


if __name__ == "__main__":
    root_directory = "/home/<USER>/Dropbox/linux/subtitle"
    process_directory(root_directory)
