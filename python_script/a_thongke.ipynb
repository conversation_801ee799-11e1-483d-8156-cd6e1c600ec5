{"cells": [{"cell_type": "code", "execution_count": 3, "id": "86c8a584697349ae", "metadata": {"ExecuteTime": {"end_time": "2024-08-15T08:55:40.995876Z", "start_time": "2024-08-15T08:55:40.991118Z"}}, "outputs": [], "source": ["import os\n", "import pandas as pd\n", "\n"]}, {"cell_type": "code", "execution_count": 7, "id": "8267c92a2169060f", "metadata": {"ExecuteTime": {"end_time": "2024-08-15T08:57:03.277416Z", "start_time": "2024-08-15T08:57:03.252992Z"}}, "outputs": [], "source": ["import pandas as pd\n", "os.chdir('/home/<USER>/Dropbox/hnd/csv_source')\n", "df_cchnd=pd.read_csv('cchnd.csv')\n"]}, {"cell_type": "code", "execution_count": 8, "id": "cd287979266196f0", "metadata": {"ExecuteTime": {"end_time": "2024-08-15T08:57:08.256119Z", "start_time": "2024-08-15T08:57:08.246727Z"}}, "outputs": [{"ename": "AttributeError", "evalue": "'Series' object has no attribute 'values_counts'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "\u001b[0;32m/tmp/ipykernel_402676/1781377655.py\u001b[0m in \u001b[0;36m?\u001b[0;34m()\u001b[0m\n\u001b[0;32m----> 1\u001b[0;31m \u001b[0mdf_cchnd\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m'loai cap'\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mvalues_counts\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[0;32m~/.local/lib//home/<USER>/python313/bin/python.12/site-packages/pandas/core/generic.py\u001b[0m in \u001b[0;36m?\u001b[0;34m(self, name)\u001b[0m\n\u001b[1;32m   6295\u001b[0m             \u001b[0;32mand\u001b[0m \u001b[0mname\u001b[0m \u001b[0;32mnot\u001b[0m \u001b[0;32min\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_accessors\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   6296\u001b[0m             \u001b[0;32mand\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_info_axis\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_can_hold_identifiers_and_holds_name\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mname\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   6297\u001b[0m         \u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   6298\u001b[0m             \u001b[0;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mname\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 6299\u001b[0;31m         \u001b[0;32mreturn\u001b[0m \u001b[0mobject\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m__getattribute__\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mname\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[0;31mAttributeError\u001b[0m: 'Series' object has no attribute 'values_counts'"]}], "source": ["df_cchnd['loai cap'].values_counts()"]}, {"cell_type": "code", "execution_count": 9, "id": "5302e821b8eefd53", "metadata": {"ExecuteTime": {"end_time": "2024-08-15T08:59:02.642429Z", "start_time": "2024-08-15T08:59:02.637408Z"}}, "outputs": [], "source": ["value_counts = df_cchnd['loai cap'].value_counts()\n"]}, {"cell_type": "code", "execution_count": 12, "id": "82bd4f269e226c3b", "metadata": {"ExecuteTime": {"end_time": "2024-08-15T08:59:28.985436Z", "start_time": "2024-08-15T08:59:28.980973Z"}}, "outputs": [{"data": {"text/plain": ["loai cap\n", "(<PERSON><PERSON><PERSON> lại lần 1 sau khi thu hồi do thiếu cập nhật kiến thức chuyên môn dư<PERSON>)                  157\n", "(<PERSON><PERSON><PERSON> l<PERSON> lầ<PERSON> 1)                                                                               66\n", "(<PERSON><PERSON><PERSON> điều chỉnh lần 1 - <PERSON><PERSON> đổi phạm vi hoạt động chuyên môn)                                54\n", "(<PERSON><PERSON><PERSON> điều chỉnh lần 1: <PERSON>hay đổi phạm vi hoạt động chuyên môn)                                 53\n", "(<PERSON><PERSON><PERSON> điều chỉnh lần 1 - Thay đổi trình độ chuyên môn)                                         13\n", "(<PERSON><PERSON>p lại lần 1 sau khi thu hồi do không cập nhật kiến thức chuyên môn dư<PERSON><PERSON> theo quy định)     13\n", "(<PERSON><PERSON><PERSON> điều chỉnh lần 1 - <PERSON><PERSON> sung phạm vi hoạt động chuyên môn)                                 11\n", "(<PERSON><PERSON><PERSON> điều chỉnh lần 1: Thay đổi trình độ chuyên môn)                                          10\n", "(<PERSON><PERSON><PERSON> điều chỉnh lần 2: <PERSON><PERSON> đổi phạm vi hoạt động chuyên môn)                                  4\n", "(<PERSON><PERSON><PERSON> điều chỉnh lần 1 - Thay đổi thông tin cá nhân trên chứng chỉ hành nghề dược)              3\n", "(<PERSON><PERSON><PERSON> điều chỉnh lần 1 - <PERSON><PERSON> đổi thông tin)                                                    2\n", "(<PERSON><PERSON><PERSON> điều chỉnh lần 1: <PERSON><PERSON> đổi ngày cấp Thẻ căn cước)                                    1\n", "(<PERSON><PERSON><PERSON> điều chỉnh lần 3: <PERSON><PERSON> đổi ngày cấp Thẻ căn cước)                                    1\n", "Name: count, dtype: int64"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["value_counts"]}, {"cell_type": "code", "execution_count": null, "id": "9e8e8122782dad35", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "/home/<USER>/python313/bin/python", "name": "/home/<USER>/python313/bin/python"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "/home/<USER>/python313/bin/python", "pygments_lexer": "ipython2", "version": "3.12.6"}}, "nbformat": 4, "nbformat_minor": 5}