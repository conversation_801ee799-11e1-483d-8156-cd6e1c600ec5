import os

import pandas as pd
from loguru import logger

from god_class import (
    get_dict_from_index_df,
    get_current_date,
    update_df_from_dict_by_index,
    update_df_name_da_<PERSON>han,
    get_and_set_index,
    creat_latex,
    auto_text_to_ioffice,
)
from top_most_get_text import input_dialog

os.chdir("/home/<USER>/Dropbox/hnd/csv_source")


@logger.catch
def gdp_cap_dc_cap_lai(mhs):
    so_dkkd = input_dialog("NHẬP", "NHẬP SỐ GCN DKKD", "")
    if not so_dkkd:
        return
    df_gdp = get_and_set_index("gdp.csv", "so dkkd")
    df_dsth = pd.read_csv("dsthuhoigpp.csv", dtype="str", index_col="ten qt-nt")
    df_name = pd.read_csv("name.csv", dtype="str", index_col="ma ho so")
    old_dict = get_dict_from_index_df(df_gdp, so_dkkd)  # lấy dữ liệu t
    list_upper = ["ten nguoi ptcm"]
    multiline = ""
    list_ngay = ["ngay cap cchnd", "ngay sinh", "ngay cap cmnd", "ngay het han gdp"]
    list_combo = []
    list_phone = ["so dt chu hs"]
    list_title = []
    dict_radio = {}
    dict_widget = {
        "ly do": [
            "- Thay đổi người phụ trách chuyên môn dược",
            "- Thay đổi địa chỉ công ty theo địa giới hành chính mà không thay đổi địa điểm",
            "- Điều chỉnh giảm phạm vi kinh doanh thuốc hướng thần, thuốc tiền chất",
            "- Thay đổi thông tin người phụ trách chuyên môn dược",
        ],
        "loai cap": [
            "Cấp lại lần 1",
            "Cấp lại lần 2",
            "Điều chỉnh lần 1",
            "Điều chỉnh lần 2",
            "Điều chỉnh lần 3",
            "Điều chỉnh lần 4",
            "Điều chỉnh lần 5",
        ],
    }
    list_include = [
        col
        for col in df_gdp.columns.tolist()
        if col not in ["ma ho so", "so qd", "ngay qd", "so dkkd", "so gdp"]
    ]

    def func(values):
        values["ma ho so"] = mhs
        values["so dkkd"] = values["so gdp"] = so_dkkd
        values["so qd"] = ""
        ngay, thang, nam, values["ngay qd"] = get_current_date()
        values["ngay dkkd cu"] = old_dict["ngay qd"]
        if values["KHO 2"]:
            dia_chi_kho = f"\n- Kho 1: {values['KHO 1']}\n- Kho 2: {values['KHO 2']}"
        else:
            dia_chi_kho = values["KHO 1"]
        ngayhan = values["ngay het han gdp"]
        han_gdp = f"Giấy chứng nhận này có giá trị đến ngày {ngayhan.split('/')[0]} tháng {ngayhan.split('/')[1]} năm {ngayhan.split('/')[2]}./."
        df_gdp["so dkkd"] = df_gdp.index
        df_gdp.set_index("ma ho so", inplace=True, drop=True)
        update_df_from_dict_by_index(df_gdp, "gdp.csv", values, mhs)
        update_df_name_da_nhan(df_name, values, mhs)
        update_ds_thu_hoi(df_dsth, old_dict, values)
        # TODO 12. ghi vào file excel

        # TODO 13. chạy lệnh latex, tạo ra gdp, GCN ĐĐKKĐ và cả qđ (không copymylatex)
        text = rf"""\documentclass[a4paper]{{article}}
\usepackage[T5]{{fontenc}}
\nonstopmode
\usepackage{{graphicx}}
\usepackage[right=2cm,left=2cm,vmargin=2cm]{{geometry}}
\usepackage[fontsize=14pt]{{scrextend}}
\usepackage{{times}}
\usepackage[onehalfspacing]{{setspace}}
\usepackage{{eso-pic}}
\usepackage{{parskip}}
\setlength{{\parskip}}{{6pt}}
\usepackage{{tabularray}}
\usepackage{{ulem}}
\renewcommand{{\ULdepth}}{{7pt}}
 \renewcommand{{\ULthickness}}{{0.5pt}}
\setlength{{\parindent}}{{0pt}}
\usepackage{{transparent}}
\renewcommand{{\baselinestretch}}{{1.2}}
\begin{{document}}
  
\pagestyle{{empty}}            
\AddToShipoutPictureBG{{\raisebox{{-1mm}}{{\includegraphics[width=\paperwidth, height=\paperheight]{{gfx/gpp4}}}}}}
\setstretch{{1}}
\noindent
\begin{{minipage}}{{\textwidth}}
\begin{{tblr}}{{width=1\linewidth,
colspec={{X[1,c] X[2,c]}},
colsep=0pt,
rowsep=-3pt,
row{{2}} = {{font=\bfseries}}}}
\fontsize{{13pt}}{{0pt}}\selectfont UBND TỈNH VĨNH PHÚC & \fontsize{{13pt}}{{0pt}}\selectfont \textbf{{CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM}}\\
{{\fontsize{{13pt}}{{0pt}}\selectfont SỞ Y TẾ\\[3pt]
\rule[0.6\baselineskip]{{.14\linewidth}}{{.5pt}}}} & \fontsize{{14pt}}{{0pt}}\selectfont \uline{{Độc lập - Tự do - Hạnh phúc}}\\[12pt]
\fontsize{{14pt}}{{0pt}}\selectfont Số: {{\color{{red}} \bfseries {values["so gdp"]}}} /GDP & \\
\end{{tblr}}
\end{{minipage}}

\vspace{{0.5cm}}

\begin{{center}} \setstretch{{1.1}}
{{\color{{teal}} \Large \textbf{{GIẤY CHỨNG NHẬN \vphantom{{Ố}}\\ĐẠT THỰC HÀNH TỐT PHÂN PHỐI THUỐC \vphantom{{Ố}}\\
GOOD DISTRIBUTION PRACTICES (GDP)}} \vphantom{{Ố}}\\[6pt]\color{{black}} \small ({values["loai cap"]} {values["ly do"]})\vphantom{{Ố}}\par}}

\vspace{{0.5\baselineskip}}

{{\color{{red}} \Large\textbf{{GIÁM ĐỐC SỞ Y TẾ CHỨNG NHẬN}}}}

\end{{center}}

\hspace{{2.1cm}} 
\makebox[0pt][l]{{%
\raisebox{{-\totalheight}}[0pt][0pt]{{%
\transparent{{0.1}}\includegraphics[width=5in]{{logo_syt.png}}}}}}

Cơ sở: {{\color{{blue}}\textbf{{{values["ten cong ty"].upper()}}}}}

Trụ sở: {values["tru so"]}.

 {{\color{{red}} \centering \bfseries \textit{{Đạt ``Thực hành tốt phân phối thuốc'' - GDP}} \par}}

Tại địa chỉ kho:
 
 {dia_chi_kho}.

Người quản lý chuyên môn/chủ cơ sở: Dược sĩ đại học {{\color{{blue}} \bfseries {values["ten nguoi ptcm"].title()}}}

Phạm vi kinh doanh: {{\color{{blue}} \bfseries {values["pham vi kd"]}}}.

{han_gdp}

\hfill\begin{{minipage}}{{0.5\textwidth}}\singlespacing
\begin{{center}}
\textit{{Phú Thọ, ngày {ngay} tháng {thang} năm {nam}}}\\
\textbf{{KT. GIÁM ĐỐC}}\\
\textbf{{PHÓ GIÁM ĐỐC}}\\
\vspace{{3cm}}
\textbf{{Nguyễn Đắc Ca}}\\
\end{{center}}
\end{{minipage}}
 \newpage
\null\vspace{{\stretch{{1}}}}
\begin{{center}}
{{\color{{red}} \bfseries  \LARGE GIẤY CHỨNG NHẬN ĐẠT\\THỰC HÀNH TỐT PHÂN PHỐI THUỐC}}
\end{{center}}
\vspace{{\stretch{{1}}}}\null
\end{{document}}"""
        creat_latex(
            text,
            f"/home/<USER>/Dropbox/hnd/latexall/gdp_tex/GDP-{values['ten cong ty']}.tex",
        )
        text = rf"""\documentclass[a4paper]{{article}}
\usepackage[T5]{{fontenc}}
\nonstopmode
\usepackage{{graphicx}}
\usepackage[right=1.5cm,left=1.5cm,top=1.5cm,bottom=1cm]{{geometry}}
\usepackage[fontsize=14pt]{{scrextend}}
\usepackage{{times}}
\usepackage[onehalfspacing]{{setspace}}
\usepackage{{eso-pic}}
\usepackage{{parskip}}
\setlength{{\parskip}}{{6pt}}
\usepackage{{tabularx}}
\usepackage{{microtype}}
\usepackage{{tabularray}}
\usepackage{{setspace}}
\usepackage{{ulem}}
\renewcommand{{\ULdepth}}{{7pt}}
 \renewcommand{{\ULthickness}}{{0.5pt}}
\setlength{{\parindent}}{{0pt}}
\renewcommand{{\baselinestretch}}{{1}}
\usepackage{{transparent}}
\begin{{document}}

\pagestyle{{empty}}
\AddToShipoutPictureBG{{\raisebox{{7mm}}{{\hspace{{0.5cm}}\includegraphics[width=0.95\paperwidth, height=0.95\paperheight]{{gfx/final frame.png}}}}}}

\begin{{center}}
  \textbf{{CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM }}

  \uline{{\textbf{{Độc lập - Tự do - Hạnh phúc}}}}
\end{{center}}

\vspace{{-0.5cm}}

\begin{{minipage}}{{\textwidth}}
  \Large \singlespacing\centering
  {{\color{{red}} \bfseries GIẤY CHỨNG NHẬN\\
    ĐỦ ĐIỀU KIỆN KINH DOANH DƯỢC}}

  \setstretch{{1.5}}

  {{\normalsize Số: {{\color{{red}} \textbf{{{values["so gdp"]}}}}} /ĐKKDD-VP\\({values["loai cap"]} {values["ly do"]})\par
  }}

  {{\color{{red}} \bfseries \Large SỞ Y TẾ CHỨNG NHẬN}}

\end{{minipage}}

\vspace{{0.5cm}}

\hspace{{2.7cm}}
\makebox[0pt][l]{{%
  \raisebox{{-\totalheight}}[0pt][0pt]{{%
    \transparent{{0.1}}\includegraphics[width=5in]{{logo_syt.png}}}}}}

Cơ sở kinh doanh: {{\color{{blue}}\textbf{{{values["ten cong ty"].upper()}}}}}

Trụ sở chính: {values["tru so"]}.

Địa chỉ kho:	

{dia_chi_kho}.

Người chịu trách nhiệm chuyên môn về dược của cơ sở:

Họ và tên: {{\color{{blue}} \bfseries {values["ten nguoi ptcm"].title()}}}; Trình độ chuyên môn: {{\color{{blue}} \bfseries Dược sĩ đại học.}}

Chứng chỉ hành nghề dược số {values["so cchnd"]}, do {values["noi cap cchnd"]} cấp ngày {values["ngay cap cchnd"]}.

Đủ điều kiện kinh doanh dược loại hình: {{\color{{blue}} \bfseries Cơ sở bán buôn thuốc.}}

Phạm vi kinh doanh: {{\color{{blue}} \bfseries {values["pham vi kd"]}}}.

Giấy chứng nhận này có hiệu lực kể từ ngày ký, được cấp theo Quyết định số {values["so qd"]}/QĐ-SYT ngày {values["ngay qd"]} của Giám đốc Sở Y tế tỉnh Phú Thọ. Giấy chứng nhận này thay thế giấy chứng nhận số {values["so dkkd"]}/ĐKKDD-VP do Sở Y tế tỉnh Phú Thọ cấp ngày {values["ngay dkkd cu"]}./.

\hfill\begin{{minipage}}{{0.5\textwidth}}\singlespacing
  \begin{{center}}
\textit{{Phú Thọ, ngày {ngay} tháng {thang} năm {nam}}}\\
\textbf{{KT. GIÁM ĐỐC}}\\
\textbf{{PHÓ GIÁM ĐỐC}}\\
\vspace{{3cm}}
\textbf{{Nguyễn Đắc Ca}}\\
\end{{center}}
\end{{minipage}}
\newpage
\newgeometry{{right=1cm,left=1cm,vmargin=2cm}}
\null\vspace{{\stretch{{1}}}}
\begin{{center}}
  {{\color{{red}} \bfseries  \huge GIẤY CHỨNG NHẬN\\ĐỦ ĐIỀU KIỆN KINH DOANH DƯỢC}}
\end{{center}}
\vspace{{\stretch{{1}}}}\null
\end{{document}}"""
        creat_latex(
            text,
            f"/home/<USER>/Dropbox/hnd/latexall/gdp_tex/DKKD-{values['ten cong ty']}.tex",
        )
        text = rf"""
\documentclass{{article}}
\usepackage[T5]{{fontenc}}
\usepackage[fontsize=14pt]{{scrextend}}
\usepackage[a4paper,right=2cm,left=3cm,vmargin=2cm]{{geometry}}
\usepackage{{mathptmx}}
\usepackage{{ulem}}
\usepackage{{setspace}}
\usepackage{{tabularray}}
\usepackage{{everypage}}
\usepackage{{parskip}}
\setlength{{\parskip}}{{0pt}}
\usepackage{{pdflscape}}
\renewcommand{{\ULdepth}}{{7pt}}
 \renewcommand{{\ULthickness}}{{0.5pt}}
\usepackage{{indentfirst}}
\setlength{{\parindent}}{{1.27cm}}
\thispagestyle{{empty}}
\usepackage{{fancyhdr}}
\fancyhf{{}}
\fancyhead[C]{{\small\thepage}}
\renewcommand{{\headrulewidth}}{{0pt}}
\pagestyle{{fancy}}
\begin{{document}}

\setstretch{{1}}
\noindent
\begin{{minipage}}{{\textwidth}}
\begin{{tblr}}{{width=1\linewidth,
colspec={{X[1,c] X[2,c]}},
colsep=0pt,
rowsep=0pt,
row{{2}} = {{font=\bfseries}}}}
\fontsize{{13pt}}{{0pt}}\selectfont UBND TỈNH VĨNH PHÚC & \fontsize{{13pt}}{{0pt}}\selectfont \textbf{{CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM}}\\
{{\fontsize{{13pt}}{{0pt}}\selectfont SỞ Y TẾ\\[3pt]
\rule[0.6\baselineskip]{{.14\linewidth}}{{.5pt}}}} & \fontsize{{14pt}}{{0pt}}\selectfont \uline{{Độc lập - Tự do - Hạnh phúc}}\\[12pt]
\fontsize{{14pt}}{{0pt}}\selectfont Số: \hspace{{1.5cm}}/QĐ-SYT & \fontsize{{14pt}}{{0pt}}\selectfont \textit{{Phú Thọ, ngày {ngay} tháng {thang} năm {nam}}}\\[6pt]
\end{{tblr}}
\end{{minipage}}

\vspace{{0.5\baselineskip}}

\begin{{center}}
\textbf{{QUYẾT ĐỊNH\\Về việc hủy bỏ giấy chứng nhận đạt Thực hành tốt\\phân phối thuốc và giấy chứng nhận đủ điều kiện kinh doanh dược}}\\[1pt]
\rule[0.6\baselineskip]{{.4\linewidth}}{{.5pt}}

    \vspace{{0.2cm}}

\textbf{{GIÁM ĐỐC SỞ Y TẾ}}
\end{{center}}


\vspace{{0.4cm}}

\setstretch{{1.15}}

\textit{{Căn cứ Luật Dược năm 2016; 

Căn cứ Luật sửa đổi, bổ sung một số điều của Luật Dược 2024;}}

\textit{{Căn cứ Nghị định số 163/2025/NĐ-CP ngày 29/6/2025 của Chính phủ quy định chi tiết một số điều và biện pháp tổ chức, hướng dẫn thi hành Luật Dược;}}

\textit{{Căn cứ Nghị định số 155/2018/NĐ-CP ngày 12/11/2018 của Chính phủ sửa đổi, bổ sung một số quy định liên quan đến điều kiện đầu tư kinh doanh thuộc phạm vi quản lý nhà nước của Bộ Y tế;}}

\textit{{Căn cứ Quyết định số 50/2022/QĐ-UBND ngày 23/12/2022 của UBND tỉnh Vĩnh Phúc quy định vị trí, chức năng, nhiệm vụ, quyền hạn và cơ cấu tổ chức của Sở Y tế tỉnh Phú Thọ;
}}

\textit{{Căn cứ hồ sơ đề nghị điều chỉnh giấy chứng nhận đủ điều kiện kinh doanh dược của {values["ten cong ty"]};}}

\textit{{Theo đề nghị của Trưởng phòng Nghiệp vụ dược Sở Y tế.}}

\vspace{{1\baselineskip}}

\begin{{center}}
\textbf{{QUYẾT ĐỊNH:}}
\end{{center}}


\vspace{{1\baselineskip}}


\textbf{{Điều 1.}} Hủy bỏ giấy chứng nhận đạt Thực hành tốt phân phối thuốc và giấy chứng nhận đủ điều kiện kinh doanh dược do Sở Y tế tỉnh Phú Thọ cấp cho {values["ten cong ty"]}. 

Lý do hủy bỏ: {values["ly do"]}.

Chi tiết theo Phụ lục đính kèm Quyết định.

\textbf{{Điều 2.}} Giấy chứng nhận đạt Thực hành tốt phân phối thuốc và giấy chứng nhận đủ điều kiện kinh doanh dược trong danh sách bị hủy bỏ không còn giá trị sử dụng từ ngày ký ban hành Quyết định.

\textbf{{Điều 3.}} Quyết định này có hiệu lực kể từ ngày ký.

Chánh Văn phòng, Trưởng các phòng chức năng Sở Y tế, Thủ trưởng các đơn vị có liên quan, Giám đốc {values["ten cong ty"]} căn cứ Quyết định thực hiện./.

\setstretch{{1}}
\noindent
\begin{{minipage}}[t]{{0.5\textwidth}} \singlespacing
\fontsize{{12pt}}{{14pt}}\selectfont \textbf{{\textit{{Nơi nhận:}}}}\\
\fontsize{{11pt}}{{13pt}}\selectfont - Như Điều 3;\\
\fontsize{{11pt}}{{13pt}}\selectfont - Giám đốc, các PGĐ Sở;\\
\fontsize{{11pt}}{{13pt}}\selectfont - TT KSBT (đăng tải Website Sở);\\
\fontsize{{11pt}}{{13pt}}\selectfont - Lưu: VT, NVD.\\
\end{{minipage}}
\begin{{minipage}}[t]{{0.5\textwidth}}
\singlespacing \centering
\textbf{{KT. GIÁM ĐỐC}}\\
\textbf{{PHÓ GIÁM ĐỐC}}\\
\vspace{{3cm}}
\textbf{{Nguyễn Đắc Ca}}\\
\end{{minipage}}

\newpage
\newcommand{{\Lpagenumber}}{{\ifdim\textwidth=\linewidth\else\bgroup
\dimendef\margin=0
\ifodd\value{{page}}\margin=\oddsidemargin
\else\margin=\evensidemargin
\fi
\raisebox{{\dimexpr -\topmargin-\headheight-\headsep-0.5\linewidth}}[0pt][0pt]{{%
\rlap{{\hspace{{-1.5cm}}
\llap{{\rotatebox{{90}}{{\thepage}}}}}}}}
\egroup\fi}}
\AddEverypageHook{{\fontsize{{14pt}}{{0pt}}\Lpagenumber}}
\fancyhf{{}}
\pagestyle{{empty}}
\newgeometry{{margin=2cm}}
\begin{{landscape}}
\begin{{center}}
\textbf{{Phụ lục\\DANH SÁCH GIẤY CHỨNG NHẬN ĐẠT THỰC HÀNH TỐT PHÂN PHỐI THUỐC (GDP) VÀ\\GIẤY CHỨNG NHẬN ĐỦ ĐIỀU KIỆN KINH DOANH DƯỢC (GCN ĐĐKKD) BỊ HỦY BỎ}}

\textit{{(Ban hành kèm theo Quyết định số \hspace{{1.5cm}}
/QĐ-SYT ngày {values["ngay qd"]} của Sở Y tế tỉnh Phú Thọ)}}

\end{{center}}

\vspace{{1\baselineskip}}
\noindent
    \begin{{tblr}}{{width=\linewidth,hlines,vlines,
        colspec={{X[0.3,c] X[0.7,c] X[0.9,c]X[0.6,c] X[0.7,c] X[0.5,c]X[0.5,c] X[0.3,c]X[0.5,c] X[0.7,c]}},
        colsep=3pt,
        rowsep=1pt,
        rows={{font=\small,m,c}},
        row{{1,2,3}}={{font=\small\bfseries}}}}
        \SetCell[r= 2]{{c}} STT & \SetCell[r= 2]{{c}} Tên cơ sở& \SetCell[r= 2]{{c}} Địa chỉ kinh doanh & \SetCell[r= 2]{{c}} Tên người phụ trách chuyên môn & \SetCell[r= 2]{{c}} Số chứng chỉ hành nghề dược & \SetCell[c= 2]{{c}} {{GCN ĐĐKKDD}} && \SetCell[c= 2]{{c}} {{GDP}} && \SetCell[r= 2]{{c}} Lý do hủy bỏ \\
        &&&&& Số& Ngày cấp & Số& Ngày cấp& \\
        1 & 2& 3& 4& 5& 6 & 7 & 8& 9 & 10 \\\
1 & {old_dict["ten cong ty"]} & {old_dict["tru so"]} & {old_dict["ten nguoi ptcm"].title()} & {old_dict["so cchnd"]} & {values["so dkkd"]} \par//ĐKKDD-VP & {old_dict["ngay qd"]} & {old_dict["so gdp"]} \par/GDP & {old_dict["ngay het han gdp"]} & {values["ly do"]}\\
\end{{tblr}}
\vspace{{0.2cm}} 

Danh sách này có 01 cơ sở./.

\end{{landscape}}
\end{{document}}
        """
        os.chdir("/home/<USER>/Dropbox/hnd/latexall/mylatex")
        creat_latex(text, "mylatex2.tex")
        text = rf"""\documentclass{{article}}
\usepackage[T5]{{fontenc}}
\usepackage{{parskip}}
\usepackage[fontsize=14pt]{{scrextend}}
\usepackage[a4paper,vmargin=2cm,right=2cm,left=3cm,]{{geometry}}
\usepackage{{mathptmx}}
\usepackage{{pdflscape}}
\usepackage{{ulem}}
\renewcommand{{\ULdepth}}{{7pt}}
 \renewcommand{{\ULthickness}}{{0.5pt}}
\usepackage{{setspace}}
\usepackage{{tabularray}}
\usepackage{{indentfirst}}
\sloppy
\setlength{{\parindent}}{{1.27cm}}
\setlength{{\parskip}}{{6pt}}
\thispagestyle{{empty}}
\usepackage{{fancyhdr}}
\fancyhf{{}}
\fancyhead[C]{{\small\thepage}}
\renewcommand{{\headrulewidth}}{{0pt}}
\pagestyle{{fancy}}
\begin{{document}}

\setstretch{{1}}
\noindent
\begin{{minipage}}{{\textwidth}}
\begin{{tblr}}{{width=1\linewidth,
colspec={{X[1,c] X[2,c]}},
colsep=0pt,
rowsep=-3pt,
row{{2}} = {{font=\bfseries}}}}
\fontsize{{13pt}}{{0pt}}\selectfont UBND TỈNH VĨNH PHÚC & \fontsize{{13pt}}{{0pt}}\selectfont \textbf{{CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM}}\\
{{\fontsize{{13pt}}{{0pt}}\selectfont SỞ Y TẾ\\[3pt]
\rule[0.6\baselineskip]{{.14\linewidth}}{{.5pt}}}} & \fontsize{{14pt}}{{0pt}}\selectfont \uline{{Độc lập - Tự do - Hạnh phúc}}\\[12pt]
\fontsize{{14pt}}{{0pt}}\selectfont Số: \hspace{{1.5cm}}/QĐ-SYT & \fontsize{{14pt}}{{0pt}}\selectfont \textit{{Phú Thọ, ngày {ngay} tháng {thang} năm {nam}}}\\[6pt]
\end{{tblr}}
\end{{minipage}}

\begin{{center}}

\vspace{{-0.5cm}}

    \textbf{{QUYẾT ĐỊNH\\Về việc điều chỉnh giấy chứng nhận đạt Thực hành tốt phân phối thuốc\\và giấy chứng nhận đủ điều kiện kinh doanh dược cho\\{values["ten cong ty"]}}}\\[2pt]
    \rule[0.6\baselineskip]{{.4\linewidth}}{{.5pt}}

    \textbf{{GIÁM ĐỐC SỞ Y TẾ}}

\end{{center}}\par
\vspace{{-0.2cm}}
\setstretch{{1}}
\textit{{Căn cứ Luật Dược năm 2016; 

Căn cứ Luật sửa đổi, bổ sung một số điều của Luật Dược 2024;}}

\textit{{Căn cứ Nghị định số 163/2025/NĐ-CP ngày 29/6/2025 của Chính phủ quy định chi tiết một số điều và biện pháp tổ chức, hướng dẫn thi hành Luật Dược;}}

\textit{{Căn cứ Nghị định số 155/2018/NĐ-CP ngày 12/11/2018 của Chính phủ sửa đổi, bổ sung một số quy định liên quan đến điều kiện đầu tư kinh doanh thuộc phạm vi quản lý nhà nước của Bộ Y tế;}}

\textit{{Căn cứ Thông tư số 03/2018/TT-BYT ngày 09/02/2018 của Bộ trưởng Bộ Y tế quy định về Thực hành tốt phân phối thuốc, nguyên liệu làm thuốc;}}

\textit{{Căn cứ Thông tư số 09/2020/TT-BYT ngày 10/6/2020 của Bộ trưởng Bộ Y tế, sửa đổi, bổ sung một số điều của Thông tư số 03/2018/TT-BYT ngày 09/02/2018 của Bộ trưởng Bộ Y tế quy định về Thực hành tốt phân phối thuốc, nguyên liệu làm thuốc;}}

\textit{{Căn cứ Quyết định số 50/2022/QĐ-UBND ngày 23/12/2022 của UBND tỉnh Vĩnh Phúc quy định vị trí, chức năng, nhiệm vụ, quyền hạn và cơ cấu tổ chức của Sở Y tế tỉnh Phú Thọ;
}}

\textit{{Căn cứ hồ sơ đề nghị điều chỉnh giấy chứng nhận đủ điều kiện kinh doanh dược của {values["ten cong ty"]}}};

\textit{{Theo đề nghị của Trưởng phòng Nghiệp vụ dược Sở Y tế.}}

\begin{{center}}
    \textbf{{QUYẾT ĐỊNH:}}
\end{{center}}

\textbf{{Điều 1.}} Điều chỉnh giấy chứng nhận đạt Thực hành tốt phân phối thuốc và giấy chứng nhận đủ điều kiện kinh doanh dược cho {values["ten cong ty"]}: 


\textbf{{Điều 2.}} {values["ten cong ty"]} phải chấp hành đúng quy định của pháp luật trong quá trình hoạt động kinh doanh dược và đảm bảo duy trì các điều kiện Thực hành tốt phân phối thuốc.

\textbf{{Điều 3.}} Quyết định này có hiệu lực kể từ ngày ký.

Chánh Văn phòng, Trưởng các phòng chức năng Sở Y tế, Thủ trưởng các đơn vị có liên quan, Giám đốc {values["ten cong ty"]} và người phụ trách chuyên môn dược của {"Chi nhánh" if "CHI NHÁNH" in values["ten cong ty"].upper() else "Công ty"} căn cứ Quyết định thực hiện./.

 \vspace{{-0.5cm}}

\setstretch{{1}}
\noindent
\begin{{minipage}}[t]{{0.5\textwidth}}
    \singlespacing
    \fontsize{{12pt}}{{14pt}}\selectfont \textbf{{\textit{{Nơi nhận:}}}}\\
    \fontsize{{11pt}}{{13pt}}\selectfont - Như Điều 3;\\
    \fontsize{{11pt}}{{13pt}}\selectfont - Giám đốc, các PGĐ Sở;\\
    \fontsize{{11pt}}{{13pt}}\selectfont - TT KSBT (đăng tải Website Sở);\\
    \fontsize{{11pt}}{{13pt}}\selectfont - Lưu: VT, NVD.\\
\end{{minipage}}
\begin{{minipage}}[t]{{0.5\textwidth}}\singlespacing
    \begin{{center}}
\textbf{{KT. GIÁM ĐỐC}}\\
\textbf{{PHÓ GIÁM ĐỐC}}\\
\vspace{{3cm}}
\textbf{{Nguyễn Đắc Ca}}\\
    \end{{center}}
\end{{minipage}}
\end{{document}}
                """
        creat_latex(text, "mylatex.tex")
        tieude = f"QUYẾT ĐỊNH Về việc điều chỉnh giấy chứng nhận đạt Thực hành tốt phân phối thuốc và giấy chứng nhận đủ điều kiện kinh doanh dược cho {values['ten cong ty']}"
        auto_text_to_ioffice(tieude, "QĐ", 0, text)

    one_index(
        df_gdp,
        list_include,
        so_dkkd,
        so_dkkd,
        dict_radio,
        list_combo,
        dict_widget,
        list_upper,
        list_title,
        old_dict,
        multiline,
        list_ngay,
        list_phone,
        "CẤP GDP",
        func,
        0,
        0,
        600,
        1000,
    )


def update_ds_thu_hoi(df_dsth_id_tenqt, old_dict, values):
    dict_to_update = {
        "trinh do cm": "Đại học dược",
        "ngay cap gdp": old_dict["ngay qd"],
        "ngay cap gpp": old_dict["ngay qd"],
        "ten qt-nt": old_dict["ten cong ty"],
        "dia chi co so": old_dict["tru so"],
        "ly do": values["ly do"],
    }
    old_dict.update(dict_to_update)
    update_df_from_dict_by_index(
        df_dsth_id_tenqt, "dsthuhoigpp.csv", old_dict, old_dict["ten cong ty"]
    )


if __name__ == "__main__":
    gdp_cap_dc_cap_lai()
