import pandas as pd
import pya<PERSON>gui
from god_class import wait_for_window_with_class, upload_file_dialog
from playwright_class import Dich<PERSON>u<PERSON><PERSON>
from god_class import send_notification
from loguru import logger
import os
import sys
from top_most_get_text import input_dialog

path_home = "/home/<USER>/Dropbox/hnd/csv_source"
path_pcloud = "/home/<USER>/Pcloud_ssd/Pcloud/"
os.chdir(path_home)
global DF_NAME
DF_NAME = pd.read_csv("name.csv", dtype=str, index_col="ma ho so")


class YeuCauBsHs(DichVuCong):
    DF_NAME.fillna("", inplace=True)

    def __init__(self, user):
        super().__init__(user)
        self.user = user
        is_kp = DF_NAME["van de hs"].str.match(r"^\d{8}_\d{6}$")
        df_kp = DF_NAME[is_kp]
        try:
            # Đường dẫn đến thư mục <PERSON>cloud
            pcloud_path = "/home/<USER>/Pcloud_ssd/Pcloud/"

            # L<PERSON><PERSON> danh sách các file trong thư mục gốc (không bao gồm thư mục con)
            pcloud_files = [
                f
                for f in os.listdir(pcloud_path)
                if os.path.isfile(os.path.join(pcloud_path, f))
            ]

            # Tạo chuỗi đường dẫn đầy đủ cho các file
            pcloud_file_paths = [os.path.join(pcloud_path, f) for f in pcloud_files]

            # Ghi log danh sách các file tìm thấy
            logger.info(
                f"Đã tìm thấy {len(pcloud_file_paths)} file trong thư mục gốc Pcloud"
            )
            for file_path in pcloud_file_paths:
                logger.debug(f"File Pcloud: {file_path}")

            # Thông báo số lượng file tìm thấy
            pcloud_file_paths = " ".join(pcloud_file_paths)
        except Exception as e:
            logger.error(f"Lỗi khi đọc thư mục Pcloud: {str(e)}")
            send_notification(f"Lỗi khi đọc thư mục Pcloud: {str(e)}")
            pcloud_file_paths = ""

        # Lọc các hồ sơ có mã trong pcloud_file_paths
        df_kp_filtered = df_kp.copy()
        if pcloud_file_paths:
            # Lọc bỏ các hồ sơ có mã không có trong pcloud_file_paths
            mask = df_kp_filtered.apply(
                lambda row: row["van de hs"] in pcloud_file_paths, axis=1
            )
            df_kp_filtered = df_kp_filtered[mask]
            logger.info(
                f"Đã lọc từ {len(df_kp)} xuống còn {len(df_kp_filtered)} hồ sơ có file trong Pcloud"
            )
        else:
            logger.warning("Không có đường dẫn Pcloud, sử dụng tất cả hồ sơ")

        # Phân loại hồ sơ theo trạng thái
        self.df_kp_thamdinh = df_kp_filtered[
            df_kp_filtered["trang thai"] == "THẨM ĐỊNH"
        ]
        self.df_kp_tdtt = df_kp_filtered[
            df_kp_filtered["trang thai"] == "THẨM ĐỊNH THỰC TẾ"
        ]
        if self.df_kp_thamdinh.empty and self.df_kp_tdtt.empty:
            send_notification("Không có hồ sơ để yêu cầu bổ sung")
            sys.exit()
        # tạo thông báo chi tiết với mã hồ sơ và tên người phụ trách chuyên môn
        thong_bao = f"Chuẩn bị yêu cầu bổ sung:\n"

        if not self.df_kp_thamdinh.empty:
            thong_bao += f"\n{len(self.df_kp_thamdinh)} hồ sơ thẩm định:\n"
            for idx, row in self.df_kp_thamdinh.iterrows():
                thong_bao += f"- Mã HS: {idx}, Người PTCM: {row['ten nguoi ptcm']}\n"

        if not self.df_kp_tdtt.empty:
            thong_bao += f"\n{len(self.df_kp_tdtt)} hồ sơ thẩm định thực tế:\n"
            for idx, row in self.df_kp_tdtt.iterrows():
                thong_bao += f"- Mã HS: {idx}, Người PTCM: {row['ten nguoi ptcm']}\n"

        send_notification(thong_bao)

    def click_ycbs(self, index, string):
        elements = self.get_row_elements()
        for element in elements:
            if self.get_attribute("value", element) == str(index):
                element.locator("button.btn-warning").click()
                break
        first_xpath = f"(//a[normalize-space()='{index}'])[1]"
        relative_xpath = "../../td[2]/div/button"
        self.click_dependent_element(first_xpath, relative_xpath)
        # self.page.locator(
        #     "//div[@class='btn-group open']//a[@title='Yêu cầu bổ sung hồ sơ']"
        # ).click()
        frame = self.page.wait_for_selector(
            "iframe[id*='bs']", state="attached", timeout=600000
        ).content_frame()
        input_element = frame.wait_for_selector("#_fcsoNgayTamDung", state="visible")
        input_element.fill(str(self.so_ngay))
        input_element = frame.wait_for_selector("#_fclyDoTamDung", state="visible")
        input_element.fill(self.notify)
        icheck_element = frame.wait_for_selector(".iCheck-helper", state="visible")
        icheck_element.click()
        input_element = frame.wait_for_selector("#_fctepTinDinhKem", state="visible")
        input_element.click()
        wait_for_window_with_class("kitty")
        # upload_file_dialog(string, Pcloud=True)

    def process_dataframe(self, df, trang_thai, loai_ho_so):
        if df.empty:
            return
        send_notification(f"chuẩn bị ycbs {len(df)} hồ sơ {loai_ho_so}")
        self.go_to_trangthai(trang_thai)
        self.expand_ds()
        self.wait_for_load_done()
        for index, values in df.iterrows():
            self.click_ycbs(index, values["van de hs"])
            frame = self.page.frame_locator("iframe[id*='bs']")
            frame.locator(
                "(//button[contains(text(),'Đồng ý')])[1]"
            ).click()  # click đồng ý
            self.wait_for_frame_to_disappear("iframe[id*='bs']")
            self.wait_for_load_done()
            DF_NAME.loc[index, "van de hs"] = "ĐÃ YÊU CẦU BỔ SUNG"
            DF_NAME.to_csv("name.csv", index_label="ma ho so")

    def yeu_cau_bo_sung(self):
        if not self.df_kp_thamdinh.empty:
            self.so_ngay = input_dialog(
                "Số ngày yc bo sung hồ sơ thẩm định",
                "Số ngày yc bo sung hồ sơ thẩm định của {}".format(
                    self.df_kp_thamdinh["ten nguoi ptcm"].iloc[0]
                ),
                "5",
            )
            self.notify = "Yêu cầu bổ sung hồ sơ"
            self.process_dataframe(self.df_kp_thamdinh, "TD", "TD")
        if not self.df_kp_tdtt.empty:
            self.so_ngay = input_dialog(
                "Số ngày yc bo sung hồ sơ thẩm định thực tế",
                "Số ngày yc bo sung hồ sơ thẩm định thực tế của {}".format(
                    self.df_kp_tdtt["ten nguoi ptcm"].iloc[0]
                ),
                "7",
            )
            self.notify = input_dialog(
                "Lý do yc bo sung hồ sơ thẩm định thực tế",
                "Lý do yc bo sung hồ sơ thẩm định thực tế của {}".format(
                    self.df_kp_tdtt["ten nguoi ptcm"].iloc[0]
                ),
                "Khắc phục tồn tại sau thẩm định thực tế",
            )
            self.process_dataframe(self.df_kp_tdtt, "TDTT", "TDTT")

    @logger.catch
    def run(self):
        super().setup()
        super().login_dvc_by_user("tungson")
        self.yeu_cau_bo_sung()
        self.cleanup()


dvc = YeuCauBsHs("tungson")
dvc.run()
