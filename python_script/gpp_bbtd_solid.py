import datetime
import os
import shutil
import subprocess
import sys

from docxtpl import DocxTemplate

from god_class import (
    TelegramSend,
    TextProcess,
    insert_stt,
    print_pdf,
    convert_ngay,
    send_notification,
    yes_no,
    change_xa,
    format_name,
)
from top_most_get_text import input_dialog
from csv_load_and_export import CsvLoaderFactory


os.chdir("/home/<USER>/Dropbox/hnd/latexall/mylatex")


#
def convert_word_to_pdf(input_file):
    subprocess.run(
        ["libreoffice", "--headless", "--convert-to", "pdf", input_file, "--outdir", ""]
    )


os.chdir("/home/<USER>/Dropbox/hnd/csv_source")

ngaytd = convert_ngay(
    input_dialog(
        "ngay td",
        "Nhập ngày đi thẩm định (viết tắt):",
        datetime.datetime.now().strftime("%d%m%Y"),
    )
)

if not ngaytd:
    send_notification("quit")
    sys.exit()

dt = CsvLoaderFactory.create_basic_loader()
df_name = dt.load_df("name")
df_ds_ca_tuan = dt.load_df("lichthamdinh")
tele = TelegramSend("hnd")
values = {}
list_mhs_td = df_ds_ca_tuan[df_ds_ca_tuan["ngay td"] == ngaytd]["ma ho so"].tolist()
df_ds_today = df_name[df_name["ma ho so"].isin(list_mhs_td)]
insert_stt(df_ds_today)
df_danh_sach = df_ds_today[
    [
        "stt",
        "ten qt-nt",
        "ten nguoi ptcm",
        "dia chi co so",
        "so dt chu hs",
        "ngay sinh",
        "han",
        "van de hs",
    ]
]
form = {
    "DANH GIA DUY TRI DAP UNG GPPQuầy thuốc": "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/word_document/form_bbtd_gpp/Quay TTD ko nv.docx",
    "DANH GIA DUY TRI DAP UNG GPPNhà thuốc": "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/word_document/form_bbtd_gpp/NT TTD ko nv.docx",
    "CAP GPP VA DKKDNhà thuốc": "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/word_document/form_bbtd_gpp/NT moi ko nv.docx",
    "CAP GPP VA DKKDQuầy thuốc": "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/word_document/form_bbtd_gpp/Quay lan dau khong nv.docx",
    "0DANH GIA DUY TRI DAP UNG GPPQuầy thuốc": "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/word_document/form_btd_gpp/Quay TTD ko nv.docx",
    "0DANH GIA DUY TRI DAP UNG GPPNhà thuốc": "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/word_document/form_bbtd_gpp/NT TTD ko nv.docx",
    "0CAP GPP VA DKKDNhà thuốc": "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/word_document/form_bbtd_gpp/NT moi ko nv.docx",
    "0CAP GPP VA DKKDQuầy thuốc": "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/word_document/form_bbtd_gpp/Quay lan dau khong nv.docx",
    "1DANH GIA DUY TRI DAP UNG GPPNhà thuốc": "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/word_document/form_bbtd_gpp/NT TTD co nv.docx",
    "1DANH GIA DUY TRI DAP UNG GPPQuầy thuốc": "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/word_document/form_bbtd_gpp/Quay TTĐ co nv.docx",
    "1CAP GPP VA DKKDNhà thuốc": "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/word_document/form_bbtd_gpp/NT moi co nv.docx",
    "1CAP GPP VA DKKDQuầy thuốc": "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/word_document/form_bbtd_gpp/Quay lan dau co nv.docx",
}
if yes_no("Có phải đoàn mình không", "có phải đoàn mình không"):
    #     thanhvien = r"""
    # \textls[-40]{- Bà Nguyễn Thị Kiều Anh, Phó trưởng phòng Nghiệp vụ Dược, Trưởng đoàn;}
    #
    # \textls[-40]{- Bà Đỗ Thị Châm, Phó trưởng phòng Nghiệp vụ Dược, thành viên;}
    #
    # - Bà Hoàng Thị Thu Thủy, chuyên viên phòng Nghiệp vụ Y dược, thành viên;
    #
    # \textls[-30]{- Ông Lê Tùng Sơn, nhân viên phòng Nghiệp vụ Dược, thành viên.}
    #     """
    #     ky = r"""
    # 1. Đỗ Thị Châm \dotfill\par
    # 2. Hoàng Thị Thu Thủy\dotfill\par
    # 3. Lê Tùng Sơn \dotfill\par"""
    thanhvien = r"""
- Bà Nguyễn Thị Kiều Anh, Phó trưởng phòng Nghiệp vụ Dược, trưởng đoàn;

- Ông Trần Trọng Phương, chuyên viên phòng Nghiệp vụ Dược, thư ký;

- Ông Vũ Xuân Trường, chuyên viên phòng Nghiệp vụ Dược, thành viên;

- Ông Lê Tùng Sơn, viên chức biệt phái phòng Nghiệp vụ Dược, thành viên.
    """
    ky = r"""
1. Trần Trọng Phương\dotfill\par
2. Vũ Xuân Trường\dotfill\par
3. Lê Tùng Sơn \dotfill\par"""
    truong_doan = "Nguyễn Thị Kiều Anh"
else:
    thanhvien = r"""

- Ông Vũ Tài Thắng, Phó trưởng phòng Nghiệp vụ Dược, trưởng đoàn;

- Bà Hoàng Thị Thu Thủy, chuyên viên phòng Nghiệp vụ y dược, thư ký; 

- Ông Nguyễn Chí Công, chuyên viên phòng Nghiệp vụ Dược, thành viên;

- Bà Nguyễn Thị Quỳnh Thêu, viên chức biệt phái phòng Nghiệp vụ Dược, thành viên;
            """
    ky = r"""
1. Hoàng Thị Thu Thủy \dotfill\par
2. Nguyễn Chí Công\dotfill\par
3. Nguyễn Thị Quỳnh Thêu \dotfill\par"""
    truong_doan = "Vũ Tài Thắng"

values["dsdi"] = r"\\".join(df_danh_sach.astype(str).agg("&".join, axis=1))
values["ngaytd"] = ngaytd
text = TextProcess("gpp_bbtd_solid/form_dstd")
text.format_old_text(values)
text.compile_pdflatex()
text.copy_pdf_to_kq_and_send_hnd("dstd")
text.print_pdf()
df_ds_today.fillna("0", inplace=True)
df_ds_today["dia chi co so new"] = df_ds_today["dia chi co so"].apply(change_xa)
df_ds_today["dia chi co so"] = (
    df_ds_today["dia chi co so new"]
    + " (trước đây là: "
    + df_ds_today["dia chi co so"]
    + ")"
)
for _, dict_csbl in df_ds_today.iterrows():
    dict_csbl["ngay"] = ngaytd.split("/")[0]
    dict_csbl["thang"] = ngaytd.split("/")[1]
    dict_csbl["nam"] = ngaytd.split("/")[2]
    dict_csbl["loai hinh"] = (
        "Quầy thuốc" if "quầy" in dict_csbl["ten qt-nt"].lower() else "Nhà thuốc"
    )
    phamvint = r"Mua thuốc để bán lẻ (có bao gồm: thuốc dạng phối hợp có chứa dược chất gây nghiện; thuốc dạng phối hợp có chứa dược chất hướng thần; thuốc dạng phối hợp có chứa tiền chất; thuốc độc; thuốc trong danh mục thuốc, dược chất thuộc danh mục chất bị cấm sử dụng trong một số ngành, lĩnh vực), trừ vắc xin; bảo quản ở điều kiện thường."
    phamviqt = r"Mua và bán lẻ thuốc thuộc Danh mục thuốc thiết yếu và Danh mục thuốc không kê đơn (có bao gồm: thuốc dạng phối hợp có chứa dược chất gây nghiện; thuốc dạng phối hợp có chứa dược chất hướng thần; thuốc dạng phối hợp có chứa tiền chất; thuốc độc; thuốc trong danh mục thuốc, dược chất thuộc danh mục chất bị cấm sử dụng trong một số ngành, lĩnh vực), trừ vắc xin; bảo quản ở điều kiện thường."
    dict_csbl["pham_vi"] = phamviqt if "Quầy" in dict_csbl["loai hinh"] else phamvint
    dict_csbl["ten qt-nt"] = dict_csbl["ten qt-nt"].title().replace("Thuốc", "thuốc")
    dia_chi_length = len(str(dict_csbl["dia chi co so"]))
    dict_csbl["thanh_vien"] = thanhvien
    dict_csbl["ky"] = ky
    dict_csbl["truong_doan"] = truong_doan
    dict_csbl["ten nguoi ptcm"] = dict_csbl["ten nguoi ptcm"].title()
    dict_csbl["so cchnd"] = (
        dict_csbl["so cchnd"] + "/CCHND-SYT-VP"
        if "/" not in dict_csbl["so cchnd"]
        else dict_csbl["so cchnd"]
    )

    dict_csbl["ab"] = "a" if dict_csbl["loai hinh"] == "Nhà thuốc" else "b"
    if dict_csbl["co nhan vien khong"] == "0":
        dict_csbl["nhan_vien"] = (
            "cơ sở bán lẻ thuốc đăng ký 01 nhân sự là người phụ trách chuyên môn dược."
        )
    else:
        dict_csbl["nhan_vien"] = (
            "cơ sở bán lẻ thuốc đăng ký .... nhân sự gồm 01 người phụ trách chuyên môn và .... nhân viên"
        )
    dict_csbl["nguoiptcm"] = dict_csbl["ten nguoi ptcm"].title()

    # Thêm các key còn thiếu để tránh lỗi IndexError
    dict_csbl["trinh do tat"] = dict_csbl.get("trinh do tat", "dược sĩ")
    dict_csbl["noi cap cchnd"] = dict_csbl.get("noi cap cchnd", "Sở Y tế tỉnh Phú Thọ")
    dict_csbl["ngay cap cchnd"] = dict_csbl.get("ngay cap cchnd", "")

    if "CAP THAY DOI DKKD" in dict_csbl["thu tuc"]:
        send_notification("CAP THAY DOI DKKD: " + dict_csbl["ten qt-nt"], True)
    formtype = (
        dict_csbl["co nhan vien khong"].replace(".0", "")
        + dict_csbl["thu tuc"]
        .replace("DANH GIA DAP UNG GPP NTBV", "CAP GPP VA DKKD")
        .replace("CAP THAY DOI DKKD", "CAP GPP VA DKKD")
        + dict_csbl["loai hinh"]
    )
    sodkkdcu = ""
    if dict_csbl["thu tuc"] == "CAP GPP VA DKKD":
        text_bbtd = TextProcess("gpp_bbtd_solid/form_bbtd_new")
    else:
        text_bbtd = TextProcess("gpp_bbtd_solid/form_bbtd_ttd")
    text_bbtd.format_old_text(dict_csbl)
    text_bbtd.compile_pdflatex()
    ten = "BBTD " + dict_csbl["ten qt-nt"] + " " + dict_csbl["ten nguoi ptcm"]
    ten = format_name(ten)
    text_bbtd.print_pdf()
    shutil.copy(
        "/home/<USER>/Dropbox/hnd/latexall/mylatex/mylatex.pdf",
        os.path.join(
            "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/word_document/checklist_gpp_word",
            ten + ".pdf",
        ),
    )
    shutil.copy(
        "/home/<USER>/Dropbox/hnd/latexall/mylatex/mylatex.tex",
        os.path.join("/home/<USER>/Dropbox/hnd/latexall/bbtd_csbl", ten + ".tex"),
    )
    path = form[formtype]
    doc = DocxTemplate(path)
    context = {
        "tencs": dict_csbl["ten qt-nt"],
        "diachi": dict_csbl["dia chi co so"],
        "nguoiptcm": dict_csbl["ten nguoi ptcm"],
        "sodkkdcu": sodkkdcu,
        "truong_doan": truong_doan,
    }
    doc.render(context)

    ten = f"checklist_gpp_{dict_csbl['ten qt-nt']}_{dict_csbl['ten nguoi ptcm']}.docx"
    ten = format_name(ten)
    doc.save(
        os.path.join(
            "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/word_document/checklist_gpp_word",
            ten,
        )
    )
    shutil.copy(
        os.path.join(
            "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/word_document/checklist_gpp_word",
            ten,
        ),
        "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/word_document/checklist_gpp_word/checklist.docx",
    )
    convert_word_to_pdf(
        "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/word_document/checklist_gpp_word/checklist.docx"
    )
    print_pdf("/home/<USER>/Dropbox/hnd/csv_source/checklist.pdf")
