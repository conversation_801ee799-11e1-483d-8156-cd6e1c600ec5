from abc import ABC, abstractmethod
from god_class import show_message
import os
import pandas as pd

os.chdir("/home/<USER>/Dropbox/hnd/csv_source")


class IFilterCondition(ABC):
    @abstractmethod
    def is_satisfied(self, row):
        pass


class EmptySoQdCondition(IFilterCondition):
    def is_satisfied(self, row):
        value = row["so qd"]
        return value == ""


class DatHoSoCondition(IFilterCondition):
    def is_satisfied(self, row):
        value = row["van de hs"]
        return value.lower() == "đạt"


class FilterMaskCreator:
    @staticmethod
    def create_mask(df: pd.DataFrame, conditions: list[IFilterCondition]) -> pd.Series:
        mask = pd.Series(True, index=df.index)
        for condition in conditions:
            mask &= df.apply(condition.is_satisfied, axis=1)
        return mask


class IFilter(ABC):
    @abstractmethod
    def filter_df(self, df: pd.DataFrame) -> pd.DataFrame:
        pass


class FilterImpl(IFilter):
    def __init__(self, conditions: list[IFilterCondition]):
        self.conditions = conditions

    def filter_df(self, df: pd.DataFrame) -> pd.DataFrame:
        df = df.fillna("")
        mask = FilterMaskCreator.create_mask(df, self.conditions)
        if df.empty:
            show_message("DataFrame rong", "Dieu kien loc la 'van de hs' = 'dat'")
            return pd.DataFrame()
        return df[mask]
