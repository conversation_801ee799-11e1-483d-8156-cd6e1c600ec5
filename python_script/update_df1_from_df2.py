import pandas as pd
from typing import Union, List, Optional
from loguru import logger


def update_df1_from_df2(
    df1: pd.DataFrame,
    df2: pd.DataFrame,
    key_column: str,
    columns_to_update: Optional[List[str]] = None,
) -> pd.DataFrame:
    """
    Cập nhật DataFrame 1 từ DataFrame 2 dựa trên một cột chỉ định.
    Giả định rằng cả hai DataFrame có chung tất cả các cột.
    Các hàng có trong df2 nhưng không có trong df1 sẽ được thêm vào df1.

    Parameters
    ----------
    df1 : pd.DataFrame
        DataFrame cần được cập nhật.
    df2 : pd.DataFrame
        DataFrame chứa dữ liệu cập nhật.
    key_column : str
        Tên cột dùng để khớp giữa hai DataFrame.
    columns_to_update : Optional[List[str]], default=None
        Danh sách các cột cần cập nhật. <PERSON><PERSON><PERSON>, tất cả các cột ngoại trừ cột khóa sẽ được cập nhật.

    Returns
    -------
    pd.DataFrame
        DataFrame đã được cập nhật và thêm các hàng mới.

    Raises
    ------
    ValueError
        Nếu cột khóa không tồn tại trong một trong hai DataFrame.
    """
    # Kiểm tra xem cột khóa có tồn tại trong cả hai DataFrame không
    if key_column not in df1.columns or key_column not in df2.columns:
        raise ValueError(
            f"Cột khóa '{key_column}' không tồn tại trong một trong hai DataFrame"
        )

    # Tạo bản sao của df1 để tránh thay đổi DataFrame gốc
    result_df = df1.copy()

    # Xác định các cột cần cập nhật
    if columns_to_update is None:
        columns_to_update = [col for col in df1.columns if col != key_column]

    # Đặt index cho cả hai DataFrame
    df1_indexed = result_df.set_index(key_column)
    df2_indexed = df2.set_index(key_column)

    # Cập nhật các hàng hiện có
    update_count = len(df1_indexed.index.intersection(df2_indexed.index))
    df1_indexed.update(df2_indexed[columns_to_update])

    # Thêm các hàng mới
    new_rows = df2_indexed[~df2_indexed.index.isin(df1_indexed.index)]
    add_count = len(new_rows)
    result_df = pd.concat([df1_indexed, new_rows]).reset_index()

    logger.info(
        f"Đã cập nhật {update_count} hàng và thêm {add_count} hàng mới vào DataFrame"
    )
    return result_df


if __name__ == "__main__":
    df1 = pd.read_csv("/home/<USER>/Dropbox/hnd/csv_source/dkkd.csv", dtype=str)
    df2 = pd.read_csv("/home/<USER>/Dropbox/hnd/csv_source/dkkd_backup.csv", dtype=str)
    df1 = update_df1_from_df2(df1, df2, "ma ho so")
    df1.to_csv("/home/<USER>/Dropbox/hnd/csv_source/dkkd_update.csv", index=False)
