from abc import ABC, abstractmethod
import pandas as pd
from pd_solid_filter import <PERSON>lter<PERSON>mp<PERSON>, FilterMaskCreator, IFilter, IFilterCondition


class IConvertDf(ABC):
    @abstractmethod
    def convert(self, df: pd.DataFrame) -> pd.DataFrame:
        pass


class ISetValueByCd(ABC):
    @abstractmethod
    def set_value(self, df: pd.DataFrame) -> pd.DataFrame:
        pass


class SetValueAfterFilter(ISetValueByCd):
    def __init__(
        self,
        filter_service: IFilter,
        mask_creator: FilterMaskCreator,
        column_name: str,
        value: str,
    ):
        self.filter = filter_service
        self.mask_creator = mask_creator
        self.column_name = column_name
        self.value = value

    def set_value(self, df: pd.DataFrame) -> pd.DataFrame:
        df_copy = df.copy()
        mask = self.mask_creator.create_mask(df_copy, self.filter)
        df_copy.loc[mask, self.column_name] = self.value
        return df_copy


def set_value_after_filter(
    df: pd.DataFrame,
    filter_conditions: list[IFilterCondition],
    column_name: str,
    value: str,
) -> pd.DataFrame:
    filter_service = FilterImpl(filter_conditions)
    mask_creator = FilterMaskCreator()
    return SetValueAfterFilter(
        filter_service, mask_creator, column_name, value
    ).set_value(df)
