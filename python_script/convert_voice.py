#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re
import subprocess
import pyperclip


def convert_voice_input(text):
    # X<PERSON> lý chuỗi chỉ chứa số, dấu cách và dấu /
    if all(c.isdigit() or c.isspace() or c == "/" for c in text):
        return text.replace(" ", "").replace("/", "")

    # Danh sách các từ khóa cần kiểm tra
    keywords = [
        "số",
        "đường",
        "ngõ",
        "tổ",
        "TDP",
        "thôn",
        "xã",
        "thị trấn",
        "phường",
        "huyện",
        "quận",
        "thành phố",
        "tỉnh",
    ]

    # Kiểm tra nếu không có từ khóa nào trong văn bản thì trả về nguyên văn bản
    if not any(keyword in text.lower() for keyword in keywords):
        return text

    # Thêm dấu phẩy sau các từ khóa nếu không có dấu phẩy
    for keyword in keywords:
        # Tạo pattern để tìm từ khóa và text sau nó cho đến từ khóa tiếp theo hoặc hết chuỗi
        pattern = f"({keyword}\\s+)([^,.]+?)(?=\\s+(?:{'|'.join(keywords)})|$)"
        # Thay thế bằng từ khóa + text + dấu phẩy
        text = re.sub(pattern, lambda m: m.group(1) + m.group(2) + ",", text)

    # Tạo pattern để tìm các từ khóa và text sau nó cho đến dấu phẩy hoặc chấm
    patterns = []
    for keyword in keywords:
        patterns.append(f"({keyword}\\s+)([^,.]+)([,.])")

    # Xử lý text
    result = text
    for pattern in patterns:
        result = re.sub(
            pattern, lambda m: m.group(1) + m.group(2).title() + m.group(3), result
        )
    # Xóa dấu phẩy ở cuối chuỗi nếu có
    result = result.rstrip(",").replace(r"/", "")

    return result


def main():
    # Lấy text từ clipboard
    text = pyperclip.paste()
    subprocess.run(["notify-send", "Đã lấy text từ clipboard"])

    # Xử lý text
    result = convert_voice_input(text)

    # Ghi lại vào clipboard
    pyperclip.copy(result)

    print("Đã xử lý xong và lưu vào clipboard!")


if __name__ == "__main__":
    main()
