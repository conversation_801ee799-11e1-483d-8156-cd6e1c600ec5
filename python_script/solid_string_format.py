from abc import ABC, abstractmethod
from unidecode import unidecode


class IDateConverter(ABC):
    """Interface cho việc chuyển đổi ngày"""

    @abstractmethod
    def convert(self, date: str) -> str:
        pass


def format_unix_style(text: str) -> str:
    return unidecode(text).lower().replace(" ", "_")


class IPhoneFormatter(ABC):
    """Interface cho việc định dạng số điện thoại"""

    @abstractmethod
    def format(self, phone: str) -> str:
        pass


class IUnixStyleConverter(ABC):
    """Interface cho việc chuyển đổi chuỗi sang định dạng unix style"""

    @abstractmethod
    def convert(self, text: str) -> str:
        pass


class DateConverter(IDateConverter):
    """Class xử lý chuyển đổi định dạng ngày"""

    def convert(self, date: str) -> str:
        """
        Chuyển đổi chuỗi ngày sang định dạng dd/mm/yyyy
        Args:
            date (str): Chuỗi ngày đầu vào
        Returns:
            str: Chuỗi ngày đã được định dạng
        """
        if not date:
            return ""
        if "/" in date and len(date) < 10:
            return date
        date = date.replace(" ", "")
        date = date.replace("/", "") if "/" in date else date

        # Xử lý tháng
        if str(date)[2:4] in ["01", "02", "10", "11", "12"]:
            month = str(date)[2:4]
        else:
            month = str(date)[3:4]

        return f"{str(date)[:2]}/{month}/{str(date)[-4:]}"


class PhoneFormatter(IPhoneFormatter):
    """Class xử lý định dạng số điện thoại"""

    def format(self, phone: str) -> str:
        """
        Định dạng số điện thoại theo mẫu xxx.xxx.xxxx
        Args:
            phone (str): Số điện thoại cần định dạng
        Returns:
            str: Số điện thoại đã định dạng
        """
        if not phone or len(phone) <= 5:
            return phone

        phone = phone.replace(".", "").replace(" ", "")
        return f"{phone[:3]}.{phone[3:6]}.{phone[-4:]}"


class UnixStyleConverter(IUnixStyleConverter):
    """Class xử lý chuyển đổi chuỗi sang định dạng unix style"""

    def convert(self, text: str) -> str:
        """
        Chuyển đổi chuỗi sang định dạng unix style
        Args:
            text (str): Chuỗi cần chuyển đổi
        Returns:
            str: Chuỗi đã chuyển đổi
        """
        return unidecode(text).lower().replace(" ", "_")


class DictStringFormatter:
    """Class xử lý chuỗi trong dictionary theo key"""

    def __init__(self, values: dict):
        self.values = values.copy()
        self.date_converter = DateConverter()
        self.phone_formatter = PhoneFormatter()
        self.unix_converter = UnixStyleConverter()

    def apply_upper(self, keys: list) -> "DictStringFormatter":
        for key in keys:
            if key in self.values and isinstance(self.values[key], str):
                self.values[key] = self.values[key].upper()
        return self

    def apply_lower(self, keys: list) -> "DictStringFormatter":
        for key in keys:
            if key in self.values and isinstance(self.values[key], str):
                self.values[key] = self.values[key].lower()
        return self

    def apply_title(self, keys: list) -> "DictStringFormatter":
        for key in keys:
            if key in self.values and isinstance(self.values[key], str):
                self.values[key] = self.values[key].title()
        return self

    def apply_capitalize(self, keys: list) -> "DictStringFormatter":
        for key in keys:
            if key in self.values and isinstance(self.values[key], str):
                self.values[key] = self.values[key].capitalize()
        return self

    def apply_date_format(self, keys: list) -> "DictStringFormatter":
        """
        Áp dụng định dạng ngày cho các key được chỉ định
        Args:
            keys (list): Danh sách các key cần xử lý
        Returns:
            dict: Dictionary sau khi xử lý
        """
        for key in keys:
            if key in self.values and isinstance(self.values[key], str):
                self.values[key] = self.date_converter.convert(self.values[key])
        return self

    def apply_phone_format(self, keys: list) -> "DictStringFormatter":
        """
        Áp dụng định dạng số điện thoại cho các key được chỉ định
        """
        for key in keys:
            if key in self.values and isinstance(self.values[key], str):
                self.values[key] = self.phone_formatter.format(self.values[key])
        return self

    def apply_unix_style(self, keys: list) -> "DictStringFormatter":
        """
        Áp dụng chuyển đổi unix style cho các key được chỉ định
        """
        for key in keys:
            if key in self.values and isinstance(self.values[key], str):
                self.values[key] = self.unix_converter.convert(self.values[key])
        return self

    def get_result(self) -> dict:
        return self.values


# Ví dụ sử dụng:
#
if __name__ == "__main__":
    date = "097 560 137"
    print(PhoneFormatter().format(date))
