import os
from datetime import datetime

import pandas as pd
import unidecode
from loguru import logger
from god_class import (
    TextProcess,
    update_df_from_dict_by_index,
    update_df_name_cho_tra,
)

# from all_pickle import load_pickle
from top_most_get_text import input_dialog


@logger.catch
def cap_gcn_mp(mhs):
    os.chdir("/home/<USER>/Dropbox/hnd/csv_source")
    path = "ds_ct_mp.csv"
    indexs = "ma ho so"
    df_mp = pd.read_csv(path, dtype=str, index_col=indexs)
    df_name = pd.read_csv("name.csv", dtype=str, index_col="ma ho so")
    # lấy ra danh sách diction để cho vào latex
    values = df_mp.loc[mhs].to_dict()
    # tính toán số Giấy Chứng Nhận Mới
    df_mp["so_gcn_dksxmp_new"] = pd.to_numeric(df_mp["so_gcn_dksxmp"], errors="coerce")
    max_so_gcn = df_mp["so_gcn_dksxmp_new"].max()
    if not values["so_gcn_dksxmp"]:
        max_so_gcn = values["so_gcn_dksxmp_new"]
        values["so_gcn_dksxmp"] = str(int(max_so_gcn) + 1)
    df_mp.drop(columns=["so_gcn_dksxmp_new"], inplace=True)
    # set số Giấy Chứng Nhận
    values["ma ho so"] = mhs
    values["ngay"] = datetime.now().day
    values["thang"] = datetime.now().month
    values["nam"] = datetime.now().year
    update_df_from_dict_by_index(df_mp, path, values, mhs)
    update_df_name_cho_tra(df_name, values, mhs)

    gcn_sx_mp = TextProcess("form_mp/form_gcn_sx_mp")
    gcn_sx_mp.format_text(values)
    gcn_sx_mp.compile_latex()
    filename = unidecode.unidecode(values["ten cong ty mp"]) + mhs

    gcn_sx_mp.copy_latex_file(
        "/home/<USER>/Dropbox/hnd/latexall/vb_cho_in/" + filename + ".tex"
    )
    gcn_sx_mp.copy_pdf_to_kq(filename + ".pdf")


if __name__ == "__main__":
    mhs = input_dialog("Title", "Nhap ma ho so so", "")
    cap_gcn_mp(mhs)
