function remove_broken_symlinks() {
    echo "Đang tìm và xóa các symlink bị hỏng trong thư mục hiện tại..."
    
    # Tìm tất cả các symlink bị hỏng chỉ trong thư mục hiện tại (không đệ quy)
    find . -maxdepth 1 -type l ! -exec test -e {} \; -print | while read -r link; do
        echo "Xóa symlink hỏng: $link"
        rm "$link"
    done
    
    echo "Hoàn tất xóa các symlink bị hỏng trong thư mục hiện tại."
} 