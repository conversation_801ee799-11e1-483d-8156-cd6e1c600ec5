import os
import subprocess

os.environ["PATH"] = (
    "/usr/local/texlive/2024/bin/x86_64-linux:"
    "/home/<USER>/.local/bin:"
    "/usr/local/bin:"
    "/usr/bin:"
    "/usr/local/sbin:"
    "/var/lib/flatpak/exports/bin:"
    "/usr/lib/jvm/default/bin:"
    "/usr/bin/site_perl:"
    "/usr/bin/vendor_perl:"
    "/usr/bin/core_perl:"
    "/home/<USER>/.local/bin:"
    "/home/<USER>/.cargo/bin:"
    "/opt/nvim-linux64/bin:"
    "/home/<USER>/.local/share/flatpak/exports/bin"
)

# Get the latest .tex file in the directory
path = "/home/<USER>/Dropbox/hnd/latexall/vb_cho_in/"
latest_file = max(
    [f for f in os.listdir(path) if f.endswith(".tex")],
    key=lambda x: os.path.getctime(os.path.join(path, x)),
)

command = f"nvim {os.path.join(path, latest_file)}"
subprocess.run(["tmux", "new-window", "-t", "hnd:", "-n", "latex-edit", command])
subprocess.run("wmctrl -xa terminator.Terminator", shell=True)
