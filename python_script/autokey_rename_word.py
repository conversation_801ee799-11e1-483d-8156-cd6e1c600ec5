import os
import glob
import pyperclip

from god_class import send_notification, show_message


def rename_latest_pdf_to_latest_word(directory):
    # Tìm file Word mới nhất
    latest_word_file = max(
        glob.iglob(os.path.join(directory, "*.docx")),
        key=os.path.getctime,
        default=None,
    )
    # Tìm file PDF mới nhất
    latest_pdf_file = max(
        glob.iglob(os.path.join(directory, "*.pdf")), key=os.path.getctime, default=None
    )

    if not latest_word_file or not latest_pdf_file:
        send_notification("Không tìm thấy file Word hoặc PDF trong thư mục.")
        return False

    # Lấy tên file Word mới nhất (không bao gồm phần mở rộng)
    latest_word_filename = os.path.splitext(os.path.basename(latest_word_file))[0]
    if latest_word_filename.startswith("0"):
        number_string = "0-" + latest_word_filename.split("-")[1]
    else:
        number_string = latest_word_filename.split("-")[0]
    pyperclip.copy(number_string)

    # Tạo tên mới cho file PDF
    new_pdf_filename = latest_word_filename + ".pdf"
    new_pdf_path = os.path.join(directory, new_pdf_filename)

    # Đổi tên file PDF
    os.rename(latest_pdf_file, new_pdf_path)
    show_message(
        "THONG BAO",
        f"Đã đổi tên file {new_pdf_filename} và copy {number_string} vào clipboard",
    )
    return True


# Sử dụng hàm với đường dẫn thư mục
directory = "/home/<USER>/Pcloud_ssd/Pcloud"
rename_latest_pdf_to_latest_word(directory)
