import requests

# TODO get all van ban
url = "https://qlvb-api.vinhphuc.gov.vn/api/van-ban-di/danh-sach-van-ban-di-da-xu-ly-cua-chuyen-vien"

payload = "co_tep_tin=-1&ma_can_bo=27683&ma_ctcb_cv=21365&ma_don_vi_quan_tri=1187&ma_loai_ttdh=0&nam=0&ngay_tao_den_ngay=15%2F01%2F2025&ngay_tao_tu_ngay=15%2F12%2F2024&page=1&size=20&trang_thai_ttdh_gui=-1"
headers = {
    "User-Agent": "Mozilla/5.0 (X11; Linux x86_64; rv:128.0) Gecko/20100101 Firefox/128.0",
    "Accept": "application/json, text/plain, */*",
    "Accept-Language": "vi,en-US;q=0.7,en;q=0.3",
    "Accept-Encoding": "gzip, deflate, br, zstd",
    "Content-Type": "application/x-www-form-urlencoded",
    "Authorization": "Bearer acKKZDmXqpUmn7D3vuzwk88RN909S7Soc5YlXQhyV6I9lU6UNYaTHtnVEhzxoo6Pul1K2NroQeS6584FZDucIYETRjjn70alDIbOUQ4V3zt8JarwIvOhqZ+z96uFvFuZjsu03k3l5Ou0DhYlEmlQ/15duefP7Ozfa3bfIo3bASLX3cOwnkpUlwVlymNBcC40fQ8vxX9itYwn/NR0nW7uuYcPT7ic/DdhkiE08Us6bbSjqfTbHGdR8fC8Nza/I52dVn4RLZDAGfVFhFgZS6qw2j2VTpQ1hpMelekgyrg23fFO2qhb81RllfgYPTifzG9j",
    "Origin": "https://iqlvb.vinhphuc.gov.vn",
    "Connection": "keep-alive",
    "Referer": "https://iqlvb.vinhphuc.gov.vn/",
    "Sec-Fetch-Dest": "empty",
    "Sec-Fetch-Mode": "cors",
    "Sec-Fetch-Site": "same-site",
}

response = requests.request("POST", url, headers=headers, data=payload)

print(response.text)


# TODO get van ban detail
url = "https://qlvb-api.vinhphuc.gov.vn/api/van-ban-di/qua-trinh-xu-ly-van-ban-di?ma_van_ban_di=3655656&ma_don_vi_quan_tri=1187"

payload = {}
headers = {
  'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64; rv:128.0) Gecko/20100101 Firefox/128.0',
  'Accept': 'application/json, text/plain, */*',
  'Accept-Language': 'vi,en-US;q=0.7,en;q=0.3',
  'Accept-Encoding': 'gzip, deflate, br, zstd',
  'Content-Type': 'application/x-www-form-urlencoded',
  'Authorization': 'Bearer acKKZDmXqpUmn7D3vuzwk88RN909S7Soc5YlXQhyV6I9lU6UNYaTHtnVEhzxoo6Pul1K2NroQeS6584FZDucIYETRjjn70alDIbOUQ4V3zt8JarwIvOhqZ+z96uFvFuZjsu03k3l5Ou0DhYlEmlQ/15duefP7Ozfa3bfIo3bASLX3cOwnkpUlwVlymNBcC40fQ8vxX9itYwn/NR0nW7uuYcPT7ic/DdhkiE08Us6bbSjqfTbHGdR8fC8Nza/I52dVn4RLZDAGfVFhFgZS6qw2j2VTpQ1hpMelekgyrg23fFO2qhb81RllfgYPTifzG9j',
  'Origin': 'https://iqlvb.vinhphuc.gov.vn',
  'Connection': 'keep-alive',
  'Referer': 'https://iqlvb.vinhphuc.gov.vn/',
  'Sec-Fetch-Dest': 'empty',
  'Sec-Fetch-Mode': 'cors',
  'Sec-Fetch-Site': 'same-site'
}

response = requests.request("GET", url, headers=headers, data=payload)

print(response.text)
