import subprocess
import pandas as pd


from all_pickle import load_pickle
from god_class import (
    send_notification,
    yes_no,
)
from playwright_class import CapTaiKhoanKetNoi


df_dkkd = pd.read_csv("/home/<USER>/Dropbox/hnd/csv_source/dkkd.csv", dtype="str")


so_qd = load_pickle(
    "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pickle_file/soqd_gpp.pk"
)
ngayqd = df_dkkd[df_dkkd["so qd"] == so_qd]["ngay qd"].unique()[0]

if yes_no("Thông báo", "<PERSON><PERSON> cấp tài khoản kết nối không"):
    cap = CapTaiKhoanKetNoi(ngayqd)
    cap.process_all_gpp()
    send_notification("ĐÃ XONG")
subprocess.run(
    [
        "kitty",
        "-e",
        "yazi",
        "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pdf_documents/kq",
    ]
)
