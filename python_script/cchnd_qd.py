import os

import pandas as pd

from god_class import (
    TextProcess,
    captalize_first_char,
    get_current_date,
    get_day_before,
    insert_stt,
    update_multi_with_vlookup,
    drop_last_50_rows,
    add_tail_to_df,
    update_common_add_diff,
    get_max_socchnd,
)

from god_class import send_notification

os.chdir("/home/<USER>/Dropbox/hnd/csv_source")


def run_main():
    qd_cap_cchnd()


def them_vao_ds_thuhoi(df_thuhoi, len_capdc, len_caplai):
    os.chdir("/home/<USER>/Dropbox/hnd/csv_source")
    if len_capdc == "00" and len_caplai == "00":
        send_notification("XONG")
        return
    dsdc = df_thuhoi[is_cap_lai_cap_dc(df_thuhoi)]
    insert_stt(dsdc)
    # dsthuhoi = convert_dsdc(dsdc)
    # append_dsth(dsthuhoi)
    # send_notification("ĐÃ THÊM VÀO DS CCHND BỊ THU HỒI")


def append_dsth(dsthuhoi):
    ds_thuhoi = pd.read_csv("dsthuhoicchnd.csv", dtype="str")
    dsthuhoi.columns = ds_thuhoi.columns
    df_combined = pd.concat([ds_thuhoi, dsthuhoi], ignore_index=True)
    df_combined.to_csv("dsthuhoicchnd.csv", index=False)


def convert_dsdc(dsdc):
    dsdc["ten nguoi ptcm"] = dsdc["ten nguoi ptcm"].str.title()
    dsdc["NƠI CẤP"] = "Sở Y tế tỉnh Phú Thọ"
    event_dictionary = {
        "(Cấp điều chỉnh lần 1: Thay đổi trình độ chuyên môn)": "Người hành nghề đề nghị cấp điều chỉnh trình độ chuyên môn trên CCHND",
        "(Cấp điều chỉnh lần 2: Thay đổi trình độ chuyên môn)": "Người hành nghề đề nghị cấp điều chỉnh trình độ chuyên môn trên CCHND",
        "(Cấp điều chỉnh lần 1: Thay đổi phạm vi hoạt động chuyên môn)": "Người hành nghề đề nghị cấp điều chỉnh phạm vi hoạt động chuyên môn trên CCHND",
        "(Cấp điều chỉnh lần 2: Thay đổi phạm vi hoạt động chuyên môn)": "Người hành nghề đề nghị cấp điều chỉnh phạm vi hoạt động chuyên môn trên CCHND",
        "(Cấp điều chỉnh lần 1: Thay đổi thông tin cá nhân)": "Người hành nghề đề nghị cấp điều chỉnh thông tin cá nhân trên CCHND",
        "(Cấp lại lần 1)": "Người hành nghề đề nghị cấp lại CCHND do lỗi ghi sai của cơ quan cấp CCHND",
        "(Cấp lại lần 1 sau khi thu hồi do thiếu cập nhật kiến thức chuyên môn dược)": "Thu hồi do quá hạn cập nhật",
    }
    dictionary = {
        "CAP LAI CCHND DO LOI CQ CAP CCHND": "Người hành nghề đề nghị cấp lại CCHND do CCHND bị ghi sai do lỗi của cơ quan cấp CCHND",
        "CAP LAI CCHND DO LAM MAT": "Người hành nghề đề nghị cấp lại CCHND do làm mất",
    }
    dsdc["Lý do thu hồi"] = dsdc["loai cap"].replace(event_dictionary)
    for key, value in dictionary.items():
        dsdc.loc[dsdc["thu tuc"] == key, "Lý do thu hồi"] = value
    dsdc["ly do CẤP LẠI"].fillna("", inplace=True)
    dsdc.loc[dsdc["ly do CẤP LẠI"].str.contains("LỖI"), "Lý do thu hồi"] = (
        "Người hành nghề đề nghị cấp lại CCHND do CCHND bị ghi sai do lỗi của cơ quan cấp CCHND"
    )
    dsthuhoi = dsdc[
        [
            "so cchnd cu",
            "ngay cchnd cu",
            "ten nguoi ptcm",
            "trinh do cm",
            "pham vi hanh nghe cu",
            "Lý do thu hồi",
        ]
    ]
    dsdc = dsdc[
        [
            "stt",
            "so cchnd cu",
            "ngay cchnd cu",
            "NƠI CẤP",
            "ten nguoi ptcm",
            "pham vi hanh nghe cu",
            "Lý do thu hồi",
        ]
    ]
    dsthuhoi["ngay qd"] = ""
    return dsthuhoi


def update_so_cchnd(df, so_dh, so_tc, container):
    mask1 = (df["so cchnd"] == "") & (df["trinh do cm"].str.contains(container))
    df.loc[mask1, "so cchnd"] = range(int(so_dh), int(so_dh + mask1.sum()))
    mask2 = (df["so cchnd"] == "") & (~df["trinh do cm"].str.contains(container))
    df.loc[mask2, "so cchnd"] = range(int(so_tc), int(so_tc + mask2.sum()))
    return df


def mhs_valid_ngay_tiep_nhan(df_name):
    # Lấy ngày hôm qua
    comparison_date = pd.to_datetime(get_day_before(1)[-1], format="%d/%m/%Y")
    # Tạo mốc thời gian 12h trưa của ngày hôm qua
    noon_yesterday = pd.Timestamp(
        year=comparison_date.year,
        month=comparison_date.month,
        day=comparison_date.day,
        hour=12,
        minute=0,
        second=0,
    )
    # Lọc các hồ sơ có ngày tiếp nhận sau 12h trưa của ngày hôm qua
    df = df_name.loc[df_name["ngay tiep nhan"] < noon_yesterday]
    return df.index.tolist()


def is_valid_ngaytiepnhan(df_cchnd, df_name):
    return df_cchnd.index.isin(mhs_valid_ngay_tiep_nhan(df_name))


def sort_dkkd_df(df):
    df["ngay qd"] = pd.to_datetime(df["ngay qd"], format="%d/%m/%Y", errors="coerce")

    # Bước 2: Tạo cột tạm thời để đánh dấu các giá trị rỗng
    df["is_na"] = df["ngay qd"].isna()

    # Bước 3: Sắp xếp DataFrame, đầu tiên theo cột 'is_na' (để các giá trị rỗng lên đầu), sau đó theo cột 'ngay qd'
    df.sort_values(by=["is_na", "ngay qd"], ascending=[False, True], inplace=True)

    df["ngay qd"] = df["ngay qd"].dt.strftime("%d/%m/%Y")
    # Bước 4: Xóa cột tạm thời nếu không cần thiết
    df.drop(columns=["is_na"], inplace=True)


def qd_cap_cchnd():
    values = {}
    values["ngay_hop"] = get_day_before(1)[-1]
    df_name = pd.read_csv("name.csv", dtype="str", index_col="ma ho so")
    df_name["ngay tiep nhan"] = pd.to_datetime(
        df_name["ngay tiep nhan"], format="%d/%m/%Y %H:%M:%S"
    )

    df_cchnd = pd.read_csv("cchnd.csv", dtype="str", index_col="ma ho so")
    df_dshn = pd.read_csv("dshn_duoc.csv", dtype="str", index_col="so cchnd")
    df_gpp = pd.read_csv("du_lieu_gpp_all.csv", dtype="str", index_col="so cchnd")
    values["ngay"], values["thang"], values["nam"], values["ngay qd"] = (
        get_current_date()
    )

    mask1 = is_sqd_null(df_cchnd)
    mask2 = is_vande_null(df_cchnd)
    mask3 = notnull_mhs(df_cchnd)
    mask4 = is_valid_ngaytiepnhan(df_cchnd, df_name)
    valid_mhs = mask1 & mask2 & mask3 & mask4
    ghi_ngayqd(
        df_cchnd,
        valid_mhs,
        values["nam"],
        values["ngay"],
        values["ngay qd"],
        values["thang"],
    )

    df_valid_to_thu_hoi = df_cchnd[valid_mhs]
    df_valid_to_thu_hoi = df_valid_to_thu_hoi.fillna("")
    df_dsqd = df_valid_to_thu_hoi
    maxdh, maxtc = get_max_socchnd(df_cchnd)
    update_so_cchnd(df_dsqd, maxdh + 1, maxtc + 1, "Đại học")
    df_dsqd["so cchnd"] = df_dsqd["so cchnd"].astype(int)
    df_dsqd = df_dsqd.sort_values("so cchnd")

    try:
        df_dsqd.loc[df_dsqd["trinh do cm"].str.contains("Đại học"), "so cchnd"] = (
            "0" + df_dsqd["so cchnd"].astype(str)
        )
        df_dsqd.loc[~df_dsqd["trinh do cm"].str.contains("Đại học"), "so cchnd"] = (
            "00" + df_dsqd["so cchnd"].astype(str)
        )
    except ValueError as e:
        if "duplicate labels" in str(e):
            # Kiểm tra index trùng lặp
            duplicated_indices = df_dsqd.index[df_dsqd.index.duplicated()].tolist()
            send_notification(
                f"Lỗi: Có index trùng lặp trong DataFrame: {duplicated_indices}"
            )
            send_notification(
                f"Lỗi: Có index trùng lặp trong DataFrame: {duplicated_indices}", True
            )
            # Có thể reset index để tiếp tục xử lý
            df_dsqd = df_dsqd.reset_index(drop=True)
            df_dsqd.loc[df_dsqd["trinh do cm"].str.contains("Đại học"), "so cchnd"] = (
                "0" + df_dsqd["so cchnd"].astype(str)
            )
            df_dsqd.loc[~df_dsqd["trinh do cm"].str.contains("Đại học"), "so cchnd"] = (
                "00" + df_dsqd["so cchnd"].astype(str)
            )
        else:
            # Nếu là lỗi khác, ném lại ngoại lệ
            raise

    def update_df_cchnd():
        df_cchnd.update(df_dsqd)
        df_cchnd.astype(str)
        sort_dkkd_df(df_cchnd)
        df_cchnd.to_csv("cchnd.csv", index_label="ma ho so")
        return df_cchnd

    update_df_cchnd()
    condition2 = is_lan_dau(df_dsqd)
    list_cchnd_cu_to_update = df_dsqd["so cchnd cu"].tolist()
    list_cchnd_cu_to_update = [value for value in list_cchnd_cu_to_update if value]
    list_col_update = [
        "ten co so dang ky hn",
        "ten qt-nt",
        "dia chi co so",
        "so gpp",
        "so dkkd",
        "so dkkd cu",
        "ngay dkkd cu",
        "so gpp CŨ",
        "ngay gpp cu",
        "pham vi kd",
        "co nhan vien khong",
        "ngay het han gpp",
        "co quan chu quan",
        "dia chi co quan chu quan",
        "NGHIỆN, HƯỚNG THẦN, TIỀN CHẤT",
        "ten cong ty",
    ]
    df_from_dshn = df_dshn.loc[
        df_dshn.index.isin(list_cchnd_cu_to_update), list_col_update
    ]
    update_gpp_all(df_dsqd, df_gpp)
    df_from_dshn["so cchnd cu"] = df_from_dshn.index
    df_from_dsqd = df_dsqd.copy()
    df_from_dsqd = df_from_dsqd.set_index("so cchnd")
    update_multi_with_vlookup(
        df_from_dsqd, df_from_dshn, "so cchnd cu", list_col_update
    )
    df_dshn = add_row_from_dsqd(df_dshn, df_from_dsqd)
    # df_dshn = update_from_dshn(df_dshn, df_from_dsqd)
    df_dshn.to_csv("dshn_duoc.csv", index_label="so cchnd")
    df_dsqd = convert_dsqd(condition2, df_dsqd)
    # check(df_dsqd)
    values["len_capdc"] = str(
        len(df_dsqd[df_dsqd["loai cap"].str.contains("điều chỉnh")])
    ).zfill(2)
    values["len_tongso"] = str(df_dsqd.shape[0]).zfill(2)
    values["len_caplai"] = str(
        len(df_dsqd[df_dsqd["loai cap"].str.contains("lại lần")])
    ).zfill(2)

    values["len_cap_sau_th"] = str(
        len(df_dsqd[df_dsqd["loai cap"].str.contains("sau khi")])
    ).zfill(2)

    df_dsqd = df_dsqd[
        [
            "stt",
            "ten nguoi ptcm",
            "ngay sinh",
            "gioi tinh",
            "cmnd",
            "ngay cap cmnd",
            "dia chi thuong tru",
            "trinh do cm",
            "so cchnd",
            "ngay qd",
            "dang ky pham vi",
            "loai cap",
        ]
    ]
    df_dsqd["dia chi thuong tru"] = (
        df_dsqd["dia chi thuong tru"]
        .str.replace("tỉnh", "")
        .str.replace("huyện", "")
        .str.replace("thành phố", "")
    )
    df_dsqd["dia chi thuong tru"] = df_dsqd["dia chi thuong tru"].str.strip(" ")
    df_dsqd["dia chi thuong tru"] = df_dsqd["dia chi thuong tru"].apply(
        captalize_first_char
    )
    df_dsqd["so cchnd"] = "{" + df_dsqd["so cchnd"].astype(str) + r"\\/CCHND-\\SYT-VP}"

    values[
        "y"
    ] = r"""\begin{longtblr}{width=1\linewidth,rowhead=1,rowhead=2,rowhead=3,hlines,vlines, colspec={X[1,c] X[2,c] X[2.4,c]X[1,c] X[3.1,c] X[2.4,c]X[3,c] X[2,c] X[2.2,c]X[2.4,c]X[3.6,c]X[3.6,c]},
        colsep=1pt, rowsep=1pt,
        rows={font=\footnotesize,m,c}, row{1,2,3}={font=\footnotesize\bfseries}}
        \SetCell[r=2]{c} STT & \SetCell[r=2]{c} Họ và tên & \SetCell[r=2]{c} Ngày sinh & \SetCell[r=2]{c} Giới tính & \SetCell[c=3]{c} {Thẻ căn cước} & & & \SetCell[r=2]{c} Trình độ chuyên môn  &  \SetCell[c= 3]{c} Chứng chỉ hành nghề dược được cấp && &  \SetCell[r=2]{c} Ghi chú\\
        & & & & Số & Ngày cấp & Hộ khẩu thường trú & cấp & Số & Ngày cấp & Phạm vi hoạt động chuyên môn& \\
                1 & 2 & 3 & 4 & 5 & 6 & 7 & 8 & 9 & 10 & 11 &12 \\"""
    if (
        values["len_capdc"] == "00"
        and values["len_caplai"] == "00"
        and values["len_cap_sau_th"] == "00"
    ):
        df_dsqd = df_dsqd[
            [
                "stt",
                "ten nguoi ptcm",
                "ngay sinh",
                "gioi tinh",
                "cmnd",
                "ngay cap cmnd",
                "dia chi thuong tru",
                "trinh do cm",
                "so cchnd",
                "ngay qd",
                "dang ky pham vi",
            ]
        ]
        values[
            "y"
        ] = r"""\begin{longtblr}{width=1\linewidth,rowhead=1,rowhead=2,rowhead=3,hlines,vlines, colspec={X[0.4,c] X[1,c] X[0.9,c]X[0.4,c] X[1.2,c] X[0.9,c]X[1.7,c] X[0.7,c] X[1,c]X[0.9,c] X[1.2,c] X[1,c]},
        colsep=2pt, rowsep=3pt,
        rows={font=\footnotesize,m,c}, row{1,2,3}={font=\footnotesize\bfseries}}
        \SetCell[r=2]{c} STT & \SetCell[r=2]{c} Họ và tên & \SetCell[r=2]{c} Ngày sinh  & \SetCell[r=2]{c} Giới tính & \SetCell[c=3]{c} {Thẻ căn cước} & & & \SetCell[r=2]{c} Trình độ chuyên môn  &  \SetCell[c= 3]{c} Chứng chỉ hành nghề dược được cấp && &  \SetCell[r=2]{c} Ghi chú\\

        & & & & Số & Ngày cấp & Hộ khẩu thường trú & cấp & Số & Ngày cấp & Phạm vi hoạt động chuyên môn& \\
                1 & 2 & 3 & 4 & 5 & 6 & 7 & 8 & 9 & 10 & 11 &12 \\"""

    ds = df_dsqd.astype(str).apply("&".join, axis=1)
    values["s"] = ds.str.cat(sep=r"\\" + "\n") + r"\\"
    values["soqd"] = r"\hspace{1.5cm}"
    # them_vao_ds_thuhoi(df_valid_to_thu_hoi, values["len_capdc"], values["len_caplai"])
    tieu_de = (
        f"QUYẾT ĐỊNH Về việc cấp Chứng chỉ hành nghề dược ngày {values['ngay qd']}"
    )
    qd = TextProcess("cchnd_qd")
    qd.format_text(values)
    qd.auto_day_van_ban(tieu_de, "QĐ", 0)


def update_gpp_all(df_dsqd, df_gpp):
    for _, values in df_dsqd.iterrows():
        if (
            values["so cchnd cu"] in df_gpp.index
            and values["so cchnd"] not in df_gpp
            and values["so cchnd cu"] != values["so cchnd"]
        ):
            df_temp = df_gpp.loc[values["so cchnd cu"]]
            if isinstance(df_temp, pd.DataFrame):
                df_gpp.loc[values["so cchnd"]] = df_temp.iloc[0]
            else:
                df_gpp.loc[values["so cchnd"]] = df_temp
            df_gpp.drop(values["so cchnd cu"], axis=0, inplace=True)
    df_gpp.to_csv("du_lieu_gpp_all.csv", index_label="so cchnd")


def add_row_from_dsqd(df_dshn, df_from_dsqd):
    df_tail = df_dshn.tail(50)
    df_tail = update_common_add_diff(df_tail, df_from_dsqd)
    df_dshn = drop_last_50_rows(df_dshn)
    df_dshn = add_tail_to_df(df_dshn, df_tail)
    return df_dshn


def convert_dsqd(con_loai_cap_null, df_dsqd):
    df_dsqd["ngay cap cchnd"] = df_dsqd["ngay qd"]
    df_dsqd["ten nguoi ptcm"] = df_dsqd["ten nguoi ptcm"].str.title()
    df_dsqd["loai cap"] = df_dsqd["loai cap"].str.replace("(", "").str.replace(")", "")
    df_dsqd["dia chi thuong tru"] = df_dsqd["dia chi thuong tru"].apply(
        captalize_first_char
    )
    df_dsqd["gioi tinh"] = (
        df_dsqd["gioi tinh"].str.replace("1", "Nam").str.replace("0", "Nữ")
    )
    df_dsqd["ly do CẤP LẠI"] = df_dsqd["ly do CẤP LẠI"].fillna("")
    df_dsqd.loc[con_loai_cap_null, "loai cap"] = ""
    df_dsqd.loc[is_sau_khi_thu_hoi(df_dsqd), "loai cap"] = (
        "Cấp CCHND sau khi CCHND bị thu hồi"
    )
    insert_stt(df_dsqd)
    df_dsqd = df_dsqd[
        [
            "stt",
            "ten nguoi ptcm",
            "ngay sinh",
            "gioi tinh",
            "cmnd",
            "ngay cap cmnd",
            "noi cap cmnd",
            "dia chi thuong tru",
            "trinh do cm",
            "ngay tot nghiep",
            "noi tot nghiep",
            "so cchnd",
            "ngay qd",
            "dang ky pham vi",
            "loai cap",
        ]
    ]
    return df_dsqd


def notnull_mhs(df_cchnd):
    return ~df_cchnd.index.isnull()


def is_vande_null(df_cchnd):
    return df_cchnd["van de hs"].isnull()


def is_sqd_null(df_cchnd):
    return df_cchnd["so qd"].isnull()


def is_lan_dau(df_dsqd):
    return df_dsqd["loai cap"].str.contains("lần đầu")


def is_sau_khi_thu_hoi(df_dsqd):
    return df_dsqd["loai cap"].str.contains("sau khi thu hồi")


def update_from_dshn(df, df_update):
    df.update(df_update)
    return df


def check(df_dsqd):
    assert (
        df_dsqd["cmnd"]
        .apply(lambda x: (len(str(x)) == 12) and (str(x).startswith("0")))
        .all()
    ), (
        "Có giá trị không đủ 12 ký tự trong cột 'cccd' hoặc có giá trị không bắt đầu bằng 0!"
    )
    assert df_dsqd["ngay cap cmnd"].apply(lambda x: int(x[-4:]) > 2020).all(), (
        "Có ngay cap cmnd trước năm 2021!"
    )


def is_cap_lai_cap_dc(df_cchnd):
    return df_cchnd["loai cap"].str.contains("chỉnh") | df_cchnd[
        "loai cap"
    ].str.contains("lại lần 1)")


def ghi_ngayqd(df_cchnd, condition, nam, ngay, ngay_qd, thang):
    df_cchnd.loc[condition, "ngay qd"] = ngay_qd
    df_cchnd.loc[condition, "ngay"] = ngay
    df_cchnd.loc[condition, "thang"] = thang
    df_cchnd.loc[condition, "nam"] = nam


if __name__ == "__main__":
    run_main()
