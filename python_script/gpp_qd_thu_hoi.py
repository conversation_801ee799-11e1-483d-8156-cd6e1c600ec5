import pandas as pd
from loguru import logger
from final import all_or_nothing, replace_dkkd
from god_class import (
    TextProcess,
    convert_ngay,
    get_current_date,
    insert_stt,
    thay_the_xa,
    show_message,
)
from abc import ABC, abstractmethod


def replace_huyen_tinh(x):
    return x.replace("huyện", "").replace("tỉnh", "").replace("thành phố", "")


class DataFrameLoader:
    @staticmethod
    def load_csv(filename: str) -> pd.DataFrame:
        return pd.read_csv(filename, dtype=str)


class DataFrameSaver:
    @staticmethod
    def save_csv(df: pd.DataFrame, filename: str) -> None:
        df.to_csv(filename, index=False)


class DataFrameProcessor:
    @staticmethod
    def process_dates(df: pd.DataFrame, date_column: str) -> pd.DataFrame:
        df[date_column] = df[date_column].apply(lambda x: convert_ngay(x))
        return df

    @staticmethod
    def process_names(df: pd.DataFrame, name_column: str) -> pd.DataFrame:
        df[name_column] = (
            df[name_column]
            .str.title()
            .str.replace("Thuốc", "thuốc")
            .str.replace("Ii", "II")
        )
        return df


class DuplicateChecker:
    @staticmethod
    def find_duplicates(
        source_df: pd.DataFrame,
        target_df: pd.DataFrame,
        source_col: str,
        target_col: str,
        display_col: str = None,
    ) -> tuple[bool, set]:
        source_ids = set(source_df[source_col])
        target_ids = set(target_df[target_col])
        intersection = source_ids & target_ids

        return bool(intersection), intersection


class DuplicateNotifier:
    @staticmethod
    def show_duplicate_message(
        df: pd.DataFrame, duplicates: set, id_col: str, display_col: str
    ) -> None:
        if duplicates:
            duplicate_details = df[df[id_col].isin(duplicates)]
            display_text = duplicate_details[display_col].str.cat(sep=", ")
            show_message("THÔNG BÁO", f"ĐÃ CÓ CÁI cham dut RỒI\n{display_text}")


class DataProcessor(ABC):
    @abstractmethod
    def process(self, data: pd.DataFrame) -> pd.DataFrame:
        pass


class DataFrameFormatter:
    def __init__(self, values: dict):
        self.values = values

    def format_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        df["TÊN CSKD"] = df["ten qt-nt"]
        df["so qd cham dut"] = ""
        df["ngay qd cham dut"] = self.values["ngayqd"]
        df["so dkkd cd"] = df["so dkkd"]
        return df


class LatexConverter:
    @staticmethod
    def convert_socchnd(df: pd.DataFrame) -> None:
        df["so cchnd"] = df["so cchnd"].apply(
            lambda x: x + "/CCHND-SYT-VP" if "-" not in x else x
        )
        df["so cchnd"] = "{" + df["so cchnd"].str.replace("/CCHN", r"\\/CCHN") + "}"


class UpdateDsThuhoi:
    def __init__(
        self,
        loader: DataFrameLoader,
        processor: DataFrameProcessor,
        formatter: DataFrameFormatter,
        values: dict,
    ):
        self.loader = loader
        self.processor = processor
        self.formatter = formatter
        self.values = values
        self.df_dsthuhoi = self.loader.load_csv("dsthuhoigpp.csv")
        insert_stt(self.df_dsthuhoi)
        self.update_df()

    def update_df(self):
        self.df_dsthuhoi = self.processor.process_dates(self.df_dsthuhoi, "ngay qd")
        self.df_dsthuhoi = self.processor.process_names(self.df_dsthuhoi, "ten qt-nt")
        self.df_dsthuhoi = self.processor.process_names(
            self.df_dsthuhoi, "ten nguoi ptcm"
        )
        self.df_dsthuhoi = self.formatter.format_dataframe(self.df_dsthuhoi)
        return self.df_dsthuhoi

    def get_list_dkkd_cd(self):
        return self.df_dsthuhoi[self.df_dsthuhoi["ly do"].str.contains("chấm dứt")][
            "so dkkd"
        ].tolist()


class RemoveFromGppAll:
    def __init__(self, list_dkkd_cd):
        self.list_dkkd_cd = list_dkkd_cd
        self.df_gpp_all = pd.read_csv("du_lieu_gpp_all.csv", dtype=str)

    def remove_records(self):
        self.df_gpp_all = self.df_gpp_all.drop(
            self.df_gpp_all[self.df_gpp_all["so dkkd"].isin(self.list_dkkd_cd)].index
        )
        self.df_gpp_all.to_csv("du_lieu_gpp_all.csv", index=False)


class AppendToDfChamdut:
    def __init__(
        self,
        df_dsthuhoi: pd.DataFrame,
        list_dkkd_cd: set,
        loader: DataFrameLoader,
        saver: DataFrameSaver,
    ):
        self.df_dsthuhoi = df_dsthuhoi
        self.list_dkkd_cd = list_dkkd_cd
        self.file_path = "ds_da_cham_dut.csv"
        self.loader = loader
        self.saver = saver
        self.df_chamdut = self.loader.load_csv(self.file_path)
        self.duplicate_checker = DuplicateChecker()
        self.duplicate_notifier = DuplicateNotifier()

    def check_duplicates(self) -> bool:
        recent_records = self.df_chamdut.tail(30)
        has_duplicates, duplicates = self.duplicate_checker.find_duplicates(
            self.df_dsthuhoi, recent_records, "so dkkd", "so dkkd cd"
        )

        if has_duplicates:
            self.duplicate_notifier.show_duplicate_message(
                self.df_dsthuhoi, duplicates, "so dkkd", "ten qt-nt"
            )

        return has_duplicates

    def append_records(self) -> bool:
        if not self.check_duplicates():
            self.df_chamdut = pd.concat([self.df_chamdut, self.df_dsthuhoi])
            self.saver.save_csv(self.df_chamdut, self.file_path)
            return True
        return False


class UpdateChitietThaobienCancu:
    def __init__(self, so_cap_dc, so_cap_lai, so_cham_dut):
        self.so_cap_dc = so_cap_dc
        self.so_cap_lai = so_cap_lai
        self.so_cham_dut = so_cham_dut
        self.values = {}

    def update_values(self):
        if self.so_cap_dc != "00" or self.so_cap_lai != "00":
            self._update_for_cap_dc_or_lai()
        else:
            self._update_for_cham_dut()
        return self.values

    def _update_for_cap_dc_or_lai(self):
        self.values["chitiet"] = (
            ". Trong đó"
            + all_or_nothing(
                ", {} cơ sở xin chấm dứt hoạt động kinh doanh dược", self.so_cham_dut
            )
            + all_or_nothing(
                ", {} cơ sở đề nghị thu hồi để cấp điều chỉnh Giấy chứng nhận đủ điều kiện kinh doanh dược",
                self.so_cap_dc,
            )
            + all_or_nothing(
                ", {} cơ sở đề nghị thu hồi để cấp lại Giấy chứng nhận đủ điều kiện kinh doanh dược",
                self.so_cap_lai,
            )
            + r".\par "
            + "Chi tiết theo Phụ lục đính kèm Quyết định"
        )
        self.values["thaobien"] = "xin chấm dứt hoạt động kinh doanh dược "
        self._update_cancu()

    def _update_cancu(self):
        if self.so_cap_dc == "00":
            self.values[
                "cancu"
            ] = r"""\textit{Căn cứ hồ sơ đề nghị cấp lại Giấy chứng nhận đủ điều kiện kinh doanh dược của người chịu trách nhiệm chuyên môn về dược của cơ sở;}

            \textit{Căn cứ đơn đề nghị chấm dứt hoạt động kinh doanh của người chịu trách nhiệm chuyên môn về dược của cơ sở;}"""
        elif self.so_cap_lai == "00":
            self.values[
                "cancu"
            ] = r"""\textit{Căn cứ hồ sơ đề nghị cấp điều chỉnh Giấy chứng nhận đủ điều kiện kinh doanh dược của người chịu trách nhiệm chuyên môn về dược của cơ sở;}

                            \textit{Xét đơn đề nghị chấm dứt hoạt động kinh doanh của cơ sở kinh doanh dược trên địa bàn tỉnh Phú Thọ;}"""
        else:
            self.values[
                "cancu"
            ] = r"""\textit{Căn cứ hồ sơ đề nghị cấp lại, cấp điều chỉnh Giấy chứng nhận đủ điều kiện kinh doanh dược của người chịu trách nhiệm chuyên môn về dược của cơ sở;}

                                            \textit{Xét đơn đề nghị chấm dứt hoạt động kinh doanh của cơ sở kinh doanh dược trên địa bàn tỉnh Phú Thọ;}"""

    def _update_for_cham_dut(self):
        self.values["chitiet"] = ", chi tiết theo Phụ lục đính kèm Quyết định"
        self.values["thaobien"] = "có tên"
        self.values["cancu"] = (
            r"""\textit{Căn cứ đơn đề nghị chấm dứt hoạt động kinh doanh của người chịu trách nhiệm chuyên môn về dược của cơ sở;}"""
        )


class EmptyFormDsthGpp:
    def __init__(self, df_dsthuhoi):
        self.df = df_dsthuhoi

    def process_df(self):
        self.df["so dkkd"] = self.df["so dkkd"].apply(replace_dkkd)
        self.df.drop(
            labels=["TÊN CSKD", "so qd cham dut", "ngay qd cham dut", "so dkkd cd"],
            axis="columns",
            inplace=True,
        )

    def create_empty_form(self):
        dk = self.df[0:0]
        dk.drop("stt", axis=1, inplace=True)
        dk.to_csv("dsthuhoigpp.csv", index=False)


class DataframeToLatex:
    def __init__(self, df):
        self.df = df

    def convert_to_latex(self):
        s = self.df.astype(str).apply("&".join, axis=1)
        clip = s.str.cat(sep="\\\\" + "\n") + "\\\\"
        return thay_the_xa(replace_huyen_tinh(clip))


@logger.catch
def qd_thuhoigpp():
    values = {}
    values["ngay"], values["thang"], values["nam"], values["ngayqd"] = (
        get_current_date()
    )

    # Khởi tạo các dependencies
    loader = DataFrameLoader()
    saver = DataFrameSaver()
    processor = DataFrameProcessor()
    formatter = DataFrameFormatter(values)

    # Sử dụng dependency injection với đầy đủ tham số
    thu_hoi = UpdateDsThuhoi(loader, processor, formatter, values)
    df_dsthuhoi = thu_hoi.df_dsthuhoi
    list_dkkd_cd = thu_hoi.get_list_dkkd_cd()

    # Tạo instance RemoveFromGppAll và gọi method
    remove_gpp = RemoveFromGppAll(list_dkkd_cd)
    remove_gpp.remove_records()

    append_to_df_chamdut = AppendToDfChamdut(df_dsthuhoi, list_dkkd_cd, loader, saver)
    append_to_df_chamdut.append_records()

    LatexConverter.convert_socchnd(df_dsthuhoi)

    so_cap_dc = str(sum(df_dsthuhoi["ly do"].str.contains("điều chỉnh"))).zfill(2)
    so_cap_lai = str(sum(df_dsthuhoi["ly do"].str.contains("lại"))).zfill(2)
    so_cham_dut = str(sum(df_dsthuhoi["ly do"].str.contains("chấm dứt"))).zfill(2)

    update_chitiet_thaobien_cancu = UpdateChitietThaobienCancu(
        so_cap_dc, so_cap_lai, so_cham_dut
    )
    values_update = update_chitiet_thaobien_cancu.update_values()
    values.update(values_update)
    # Áp dụng hàm cho mỗi dòng trong DataFrame

    empty_form = EmptyFormDsthGpp(df_dsthuhoi)
    empty_form.process_df()
    empty_form.create_empty_form()
    df_dsthuhoi = df_dsthuhoi[
        [
            "stt",
            "ten qt-nt",
            "dia chi co so",
            "ten nguoi ptcm",
            "so cchnd",
            "so dkkd",
            "ngay qd",
            "ly do",
        ]
    ]
    latex_converter = DataframeToLatex(df_dsthuhoi)
    values["clip"] = latex_converter.convert_to_latex()

    values["soqd"] = r"\hspace{1.5cm}"
    values["pp1"] = (
        r"\textbf{QUYẾT ĐỊNH\\Về việc thu hồi, hủy bỏ Giấy chứng nhận đủ điều kiện kinh doanh dược}"
    )
    values["pp2"] = ""
    values["giaypph"] = ""
    values["tongso"] = str(len(df_dsthuhoi)).zfill(2)

    title = f"QUYẾT ĐỊNH Về việc thu hồi, hủy bỏ Giấy chứng nhận đủ điều kiện kinh doanh dược ngày {values['ngayqd']}"
    text = TextProcess("gpp_qd_thu_hoi")
    text.format_text(values)
    text.auto_day_van_ban(title, "QĐ", "0")


if __name__ == "__main__":
    qd_thuhoigpp()
