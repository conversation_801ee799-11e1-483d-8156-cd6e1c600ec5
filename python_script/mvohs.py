from god_class import fzf_get_dict_from_list
from csv_load_and_export import CsvLoaderFactory
import os
import shutil


df = CsvLoaderFactory.create_basic_loader().load_df("name", "ma ho so")

os.chdir("/home/<USER>/Dropbox/hnd/csv_source")
backup_dir = os.path.expanduser(
    "~/Pcloud_ssd/Pcloud/HND_Document/pdf_documents/hs_saved"
)
subdirs = [
    os.path.join(backup_dir, d)
    for d in os.listdir(backup_dir)
    if os.path.isdir(os.path.join(backup_dir, d)) and not d.startswith(".")
]

item = fzf_get_dict_from_list(subdirs)
# Kiểm tra nếu item là thư mục thì dùng copytree, nếu là file thì dùng copy


def copy_item_to_dest(source_path: str, destination_parent_dir: str) -> str | None:
    """
    Sao chép một mục (tệp hoặc thư mục) v<PERSON><PERSON> thư mục cha đích.
    <PERSON><PERSON><PERSON> mục là một thư mụ<PERSON>, nó sẽ được sao chép đệ quy.
    Nếu đích đã tồn tại, thư mục đích sẽ bị xóa và tạo lại.

    Args:
        source_path: Đường dẫn đến tệp hoặc thư mục nguồn.
        destination_parent_dir: Đường dẫn đến thư mục cha nơi mục sẽ được sao chép.

    Returns:
        Đường dẫn đến mục đã sao chép trong đích, hoặc None nếu có lỗi xảy ra.
    """
    if not os.path.exists(source_path):
        print(f"Lỗi: Đường dẫn nguồn không tồn tại: {source_path}")
        return None

    item_name = os.path.basename(source_path)
    destination_path = os.path.join(destination_parent_dir, item_name)

    try:
        if os.path.isdir(source_path):
            # Đảm bảo thư mục cha đích tồn tại
            if not os.path.exists(destination_parent_dir):
                os.makedirs(destination_parent_dir, exist_ok=True)

            # Nếu thư mục đích cụ thể đã tồn tại, xóa nó đi
            if os.path.exists(destination_path):
                shutil.rmtree(destination_path)
                print(f"Thông báo: Đã xóa thư mục đích đã tồn tại: {destination_path}")

            shutil.copytree(
                source_path, destination_path
            )  # Không cần dirs_exist_ok=True nữa vì đã xóa ở trên
            print(
                f"Thông báo: Đã sao chép thư mục {source_path} đến {destination_path}"
            )
        elif os.path.isfile(source_path):
            # Đảm bảo thư mục cha đích tồn tại
            if not os.path.exists(destination_parent_dir):
                os.makedirs(destination_parent_dir, exist_ok=True)
            shutil.copy2(
                source_path, destination_path
            )  # copy2 bảo toàn metadata và sẽ ghi đè nếu tệp đích tồn tại
            print(f"Thông báo: Đã sao chép tệp {source_path} đến {destination_path}")
        else:
            print(f"Lỗi: {source_path} không phải là tệp hay thư mục hợp lệ.")
            return None
        return destination_path
    except Exception as e:
        print(f"Lỗi khi sao chép {source_path} đến {destination_path}: {e}")
        return None


copy_item_to_dest(
    item, "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pdf_documents/filehs"
)
mhs = item.split("/")[-1].split("--")[0]
df.loc[mhs, "da nhan"] = "0"
df.to_csv("name.csv", index_label="ma ho so")
