from telegram_append_img2pdf import click_ui, save_clipboard_to_pdf, copy_file_to_clip
from god_class import send_notification
import os


def main():
    path = "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pdf_documents/file_hs_online/file.pdf"
    if os.path.exists(path):
        os.remove(path)
    send_notification("start")
    click_ui(899, 1021, "chat.zalo.me.Google-chrome")
    save_clipboard_to_pdf("/home/<USER>/Pcloud_ssd/Pcloud/Zalo_Download")
    copy_file_to_clip("/home/<USER>/Pcloud_ssd/Pcloud/anh_hs/file_hs.pdf")
    send_notification("Đã copy file_hs.pdf vào clipboard")


if __name__ == "__main__":
    main()
