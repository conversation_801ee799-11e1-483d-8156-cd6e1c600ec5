import os
import shutil
import subprocess
import sys
from typing import List

import pandas as pd
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QKeyEvent
from PyQt5.QtWidgets import (
    QApplication,
    QWidget,
    QVBoxLayout,
    QPushButton,
    QLabel,
    QHBoxLayout,
)
from Xlib import display

from all_pickle import save_last_dict


def switch_to_window_by_class(class_name):
    output = subprocess.check_output(["wmctrl", "-lx"]).decode("utf-8")

    # Kiểm tra từng dòng để tìm cửa sổ với class name đã cho
    for line in output.splitlines():
        if class_name in line:
            # Lấy ID của cửa sổ
            window_id = line.split()[0]
            # Chuyển đến cửa sổ đó
            subprocess.run(["wmctrl", "-i", "-a", window_id])
            return True


# Sử dụng hàm để kiểm tra và chuyển qua cửa sổ với class "file_hs.file_hs"
if switch_to_window_by_class("file_hs.file_hs"):
    sys.exit()


class FileHsViewer(QWidget):
    BASE_DIR = "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pdf_documents/filehs"

    def __init__(self):
        super().__init__()
        from god_class import get_list_subfolder

        self.current_index: int = 0
        self.folder_buttons: List[QPushButton] = []  # Danh sách các button để duyệt
        self.folder_paths: List[str] = []  # Danh sách đường dẫn thư mục
        self.folder_labels: List[QLabel] = []  # Danh sách các label
        self.opened_items: set[str] = set()  # Lưu tên các thư mục đã mở

        df_hs = pd.read_csv("/home/<USER>/Dropbox/hnd/csv_source/hs.csv", dtype=str)
        list_subfolder = get_list_subfolder(FileHsViewer.BASE_DIR)

        dict_active_hs = {
            folder.split("--")[0].split("/")[-1]: folder for folder in list_subfolder
        }
        # list_done_hs = df_hs.loc[df_hs["da nhan"] == "1", "ma ho so"].tolist()
        """
        xoa bo cac thu muc da xong
        """
        # for subfolder in list_subfolder:
        #     for done_hs in list_done_hs:
        #         if done_hs in subfolder:
        #             # shutil.rmtree(subfolder)
        #
        #             break
        # list_all_hs = df_hs["ma ho so"].tolist()
        # keys_to_remove = [mhs for mhs in dict_active_hs if mhs in list_all_hs]
        # for mhs in keys_to_remove:
        #     dict_active_hs.pop(mhs)
        # for subfolder in dict_active_hs.values():
        #     shutil.rmtree(subfolder)

        self.title = "DANH SACH HO SO"
        self.left = 10
        self.top = 10
        self.width = 600
        self.height = 400
        self.initUI()

    def set_class_hint(self, res_name, res_class):
        # Lấy display và window ID
        d = display.Display()
        w = self.winId()

        # Chuyển window ID thành kiểu đúng của Xlib
        window = d.create_resource_object("window", int(w))

        # Set class hint
        window.set_wm_class(res_name, res_class)

        # Flush yêu cầu tới server
        d.sync()

    def keyPressEvent(self, event: QKeyEvent) -> None:
        """Xử lý sự kiện nhấn phím.

        Args:
            event: Sự kiện phím được nhấn
        """
        if event.key() == Qt.Key_J:
            self.move_selection(1)
        elif event.key() == Qt.Key_K:
            self.move_selection(-1)
        elif event.key() in (Qt.Key_Return, Qt.Key_Space):
            if 0 <= self.current_index < len(self.folder_buttons):
                # Mở trực tiếp thư mục tại chỉ số hiện tại
                print(f"Mở thư mục tại index {self.current_index}")
                print(f"Đường dẫn: {self.folder_paths[self.current_index]}")
                self.open_folder_at_index(self.current_index)
        else:
            super().keyPressEvent(event)

    def move_selection(self, direction: int) -> None:
        """Di chuyển lựa chọn lên hoặc xuống.

        Args:
            direction: Hướng di chuyển (1: xuống, -1: lên)
        """
        if not self.folder_buttons:
            return

        # Cập nhật index mới
        self.current_index = (self.current_index + direction) % len(self.folder_buttons)

    def initUI(self):
        self.show()

        # Set class name cho ứng dụng
        self.set_class_hint("file_hs", "file_hs")
        self.setWindowTitle(self.title)
        self.setGeometry(self.left, self.top, self.width, self.height)

        # Căn giữa cửa sổ
        screenGeometry = QApplication.desktop().screenGeometry()
        x = (screenGeometry.width() - self.width) / 2
        y = (screenGeometry.height() - self.height) / 2
        self.move(int(x), int(y))

        layout = QVBoxLayout()
        self.setLayout(layout)

        # Thiết lập font chữ
        font = QFont("DejaVu", 15)

        # Đường dẫn tới thư mục chứa các thư mục PDF
        folder_names = os.listdir(FileHsViewer.BASE_DIR)

        # Duyệt qua mỗi thư mục con trong base_dir
        sorted_folders = sorted(
            folder_names,
            key=lambda name: (
                len(name.split("--")) < 2,
                name.split("--")[1] if len(name.split("--")) >= 2 else "",
            ),
        )

        for folder_name in sorted_folders:
            folder_path = os.path.join(FileHsViewer.BASE_DIR, folder_name)
            if os.path.isdir(folder_path):
                # Tạo một QHBoxLayout cho mỗi thư mục
                h_layout = QHBoxLayout()

                # Tạo và thêm label vào QHBoxLayout
                label = QLabel(folder_name)
                label.setFont(font)  # Áp dụng font chữ
                h_layout.addWidget(label)
                self.folder_labels.append(label)  # Thêm vào danh sách labels

                # Tạo và thêm QPushButton vào QHBoxLayout
                button = QPushButton("THAM DINH")
                button.setFont(font)  # Áp dụng font chữ

                # Lưu button vào danh sách
                self.folder_buttons.append(button)
                # Lưu đường dẫn thư mục vào danh sách
                self.folder_paths.append(folder_path)

                # Kết nối button với phương thức click handler
                button.clicked.connect(self.button_clicked)
                h_layout.addWidget(button)

                # Thêm QHBoxLayout vào QVBoxLayout chính
                layout.addLayout(h_layout)

        quitButton = QPushButton("Quit")
        quitButton.setFont(font)
        quitButton.clicked.connect(self.close)
        quitLayout = QHBoxLayout()
        quitLayout.addStretch(1)
        quitLayout.addWidget(quitButton)
        quitLayout.addStretch(1)
        layout.addLayout(quitLayout)

    def open_folder_at_index(self, index: int) -> None:
        """Mở thư mục tại chỉ số cụ thể.

        Args:
            index: Chỉ số của thư mục cần mở
        """
        if 0 <= index < len(self.folder_paths):
            label = self.folder_labels[index]
            path = self.folder_paths[index]
            print(f"Mở thư mục tại index {index}: {path}")
            self.openPDFs(label, path)

    def button_clicked(self) -> None:
        """Xử lý sự kiện button được click."""
        # Lấy button đang gửi tín hiệu
        button = self.sender()
        if button in self.folder_buttons:
            # Lấy index của button
            index = self.folder_buttons.index(button)
            print(f"Button clicked at index: {index}")
            self.open_folder_at_index(index)

    def openPDFs(self, label, folder_path):
        """Mở thư mục được chọn.

        Args:
            label: Label chứa tên thư mục
            folder_path: Đường dẫn đầy đủ đến thư mục
        """
        if not os.path.exists(folder_path):
            print(f"Folder not found: {folder_path}")
            return

        # Lưu tên thư mục đã mở trước khi minimize
        folder_name = label.text()
        print(f"Opening folder: {folder_name}")  # Debug log

        subprocess.run("xdotool windowminimize $(xdotool getactivewindow)", shell=True)

        # Đánh dấu đỏ item đã mở
        label.setStyleSheet("color: red;")
        self.opened_items.add(folder_name)

        save_last_dict(
            folder_path,
            "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pickle_file/hs_path.pk",
        )
        subprocess.Popen(["kitty", "-e", "yazi", folder_path])


if __name__ == "__main__":
    app = QApplication([])
    ex = FileHsViewer()
    ex.show()
    app.exec_()
