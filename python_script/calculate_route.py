#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Chương trình tính toán lộ trình tối ưu cho việc đi thẩm định.

Chương trình đọc dữ liệu từ hai file csv:
- toa_do.csv: <PERSON><PERSON><PERSON> tọa độ địa lý của các địa điểm
- lichthamdinh.csv: <PERSON><PERSON><PERSON> danh sách các địa điểm cần đi thẩm định

Kết quả là một lộ trình tối ưu đi qua tất cả các điểm, tạo thành một vòng tròn
và không có đường đi nào giao nhau.
"""

import csv
import math
import re
from typing import Dict, List, Tuple, Optional, Set
import matplotlib.pyplot as plt
from reportlab.lib.pagesizes import A4
from reportlab.lib.units import cm
from reportlab.lib import colors
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from god_class import send_notification, TelegramSend
import os

tele = TelegramSend("hnd")

# Đăng ký font tiếng Việt (nếu có)
try:
    pdfmetrics.registerFont(TTFont("DejaVuSans", "/usr/share/fonts/TTF/DejaVuSans.ttf"))
    FONT_NAME = "DejaVuSans"
except:
    FONT_NAME = "Helvetica"


class DiaDiem:
    """
    Lớp biểu diễn một địa điểm với tên và tọa độ.
    """

    def __init__(self, ten: str, kinh_do: float, vi_do: float, mo_ta: str = "") -> None:
        """
        Khởi tạo đối tượng DiaDiem.

        Args:
            ên địa điểm
            kinh_do: Tọa độ kinh độ
            vi_do: Tọa độ vĩ độ
            mo_ta: Mô tả bổ sung cho địa điểm (ví dụ: tên cơ sở)
        """
        self.ten = ten.lower().strip()
        self.kinh_do = kinh_do
        self.vi_do = vi_do
        self.mo_ta = mo_ta
        self.ten_co_so: List[str] = []  # Danh sách các cơ sở tại địa điểm này

    def __str__(self) -> str:
        """
        Trả về chuỗi biểu diễn địa điểm.

        Returns:
            Chuỗi biểu diễn địa điểm
        """
        return f"{self.ten} ({self.kinh_do}, {self.vi_do})"

    def __eq__(self, other: object) -> bool:
        """
        So sánh hai địa điểm dựa trên tọa độ.

        Args:
            other: Đối tượng cần so sánh

        Returns:
            True nếu hai địa điểm có cùng tọa độ, False nếu khác
        """
        if not isinstance(other, DiaDiem):
            return False
        return self.kinh_do == other.kinh_do and self.vi_do == other.vi_do

    def __hash__(self) -> int:
        """
        Hàm băm cho địa điểm, dùng để lưu trong set hoặc làm khóa cho dict.

        Returns:
            Giá trị băm của địa điểm
        """
        return hash((self.kinh_do, self.vi_do))


def load_toa_do(filename: str) -> Dict[str, DiaDiem]:
    """
    Đọc file tọa độ và trả về từ điển các địa điểm.

    Args:
        filename: Đường dẫn đến file tọa độ

    Returns:
        Từ điển với khóa là tên địa điểm và giá trị là đối tượng DiaDiem
    """
    dia_diem_dict = {}
    with open(filename, "r", encoding="utf-8") as f:
        reader = csv.DictReader(f)
        for row in reader:
            ten = row["ten dia diem"].lower().strip()
            kinh_do = float(row["kinh_do"])
            vi_do = float(row["vi_do"])
            dia_diem_dict[ten] = DiaDiem(ten, kinh_do, vi_do)
    return dia_diem_dict


def extract_xa_from_address(address: str) -> str:
    """
    Trích xuất tên xã từ địa chỉ.

    Args:
        address: Chuỗi địa chỉ đầy đủ

    Returns:
        Tên xã đã được chuẩn hóa
    """
    # Tìm mẫu "xã X" trong địa chỉ
    match = re.search(r"xã\s+([^,]+)", address.lower())
    if match:
        return match.group(1).strip()

    # Nếu không tìm thấy "xã", thử tìm "thị trấn X"
    match = re.search(r"thị trấn\s+([^,]+)", address.lower())
    if match:
        return match.group(1).strip()

    # Nếu không tìm thấy "xã" và "thị trấn", thử tìm "phường X"
    match = re.search(r"phường\s+([^,]+)", address.lower())
    if match:
        return match.group(1).strip()

    # Nếu không tìm thấy cả "xã", "thị trấn" và "phường", trả về chính địa chỉ đó
    return address.lower().strip()


def load_lich_tham_dinh(filename: str) -> List[Tuple[str, str, str, str]]:
    """
    Đọc file lịch thẩm định và trả về danh sách các địa điểm cần đi.

    Args:
        filename: Đường dẫn đến file lịch thẩm định

    Returns:
        Danh sách các bộ bốn (số thứ tự, tên cơ sở, địa chỉ, ngày thẩm định)
    """
    dia_diem_list = []
    with open(filename, "r", encoding="utf-8") as f:
        reader = csv.DictReader(f)
        for row in reader:
            stt = row["stt"]
            ten_co_so = row["ten qt-nt"]
            dia_chi = row["dia chi co so"]
            ngay_tham_dinh = row.get(
                "ngay td", ""
            )  # Lấy ngày thẩm định, nếu không có thì trả về chuỗi rỗng
            dia_diem_list.append((stt, ten_co_so, dia_chi, ngay_tham_dinh))
    return dia_diem_list


def distance(diem1: DiaDiem, diem2: DiaDiem) -> float:
    """
    Tính khoảng cách giữa hai điểm dựa trên tọa độ địa lý, sử dụng công thức Haversine.

    Args:
        diem1: Điểm thứ nhất
        diem2: Điểm thứ hai

    Returns:
        Khoảng cách thực tế giữa hai điểm theo km
    """
    # Bán kính trái đất (km)
    R = 6371.0

    # Chuyển từ độ sang radian
    lon1 = math.radians(diem1.kinh_do)
    lat1 = math.radians(diem1.vi_do)
    lon2 = math.radians(diem2.kinh_do)
    lat2 = math.radians(diem2.vi_do)

    # Chênh lệch tọa độ
    dlon = lon2 - lon1
    dlat = lat2 - lat1

    # Công thức Haversine
    a = (
        math.sin(dlat / 2) ** 2
        + math.cos(lat1) * math.cos(lat2) * math.sin(dlon / 2) ** 2
    )
    c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))
    distance = R * c

    return distance


def do_segments_intersect(p1: DiaDiem, p2: DiaDiem, p3: DiaDiem, p4: DiaDiem) -> bool:
    """
    Kiểm tra xem hai đoạn thẳng có giao nhau không.

    Args:
        p1: Điểm đầu của đoạn thẳng thứ nhất
        p2: Điểm cuối của đoạn thẳng thứ nhất
        p3: Điểm đầu của đoạn thẳng thứ hai
        p4: Điểm cuối của đoạn thẳng thứ hai

    Returns:
        True nếu hai đoạn thẳng giao nhau, False nếu không
    """

    def orientation(p: DiaDiem, q: DiaDiem, r: DiaDiem) -> int:
        val = (q.vi_do - p.vi_do) * (r.kinh_do - q.kinh_do) - (
            q.kinh_do - p.kinh_do
        ) * (r.vi_do - q.vi_do)
        if val == 0:
            return 0  # Đồng tuyến
        return (
            1 if val > 0 else 2
        )  # Theo chiều kim đồng hồ hoặc ngược chiều kim đồng hồ

    def on_segment(p: DiaDiem, q: DiaDiem, r: DiaDiem) -> bool:
        return (
            q.kinh_do <= max(p.kinh_do, r.kinh_do)
            and q.kinh_do >= min(p.kinh_do, r.kinh_do)
            and q.vi_do <= max(p.vi_do, r.vi_do)
            and q.vi_do >= min(p.vi_do, r.vi_do)
        )

    o1 = orientation(p1, p2, p3)
    o2 = orientation(p1, p2, p4)
    o3 = orientation(p3, p4, p1)
    o4 = orientation(p3, p4, p2)

    # Trường hợp chung
    if o1 != o2 and o3 != o4:
        return True

    # Trường hợp đặc biệt
    if o1 == 0 and on_segment(p1, p3, p2):
        return True
    if o2 == 0 and on_segment(p1, p4, p2):
        return True
    if o3 == 0 and on_segment(p3, p1, p4):
        return True
    if o4 == 0 and on_segment(p3, p2, p4):
        return True

    return False


def has_intersections(route: List[DiaDiem]) -> bool:
    """
    Kiểm tra xem lộ trình có các đoạn giao nhau không.

    Args:
        route: Danh sách các điểm trên lộ trình

    Returns:
        True nếu có giao nhau, False nếu không
    """
    n = len(route)
    for i in range(n):
        for j in range(i + 2, n):
            # Bỏ qua trường hợp 2 đoạn liên tiếp hoặc đoạn đầu và đoạn cuối
            if j == i + 1 or (i == 0 and j == n - 1):
                continue

            if do_segments_intersect(
                route[i], route[(i + 1) % n], route[j], route[(j + 1) % n]
            ):
                return True

    return False


def find_optimal_route(points: List[DiaDiem]) -> List[DiaDiem]:
    """
    Tìm lộ trình tối ưu đi qua tất cả các điểm.

    Dùng thuật toán gần đúng để tạo một lộ trình tối ưu đi qua tất cả các điểm,
    tạo thành một vòng tròn và không có đường đi nào giao nhau.

    Args:
        points: Danh sách các điểm cần đi qua

    Returns:
        Lộ trình tối ưu
    """
    # Xử lý các điểm có cùng tọa độ
    unique_points: List[DiaDiem] = []
    seen_coords: Set[Tuple[float, float]] = set()

    for point in points:
        coords = (point.kinh_do, point.vi_do)
        if coords not in seen_coords:
            seen_coords.add(coords)
            unique_points.append(point)
        else:
            # Nếu đã có một điểm với tọa độ này, thêm tên cơ sở vào điểm đó
            for p in unique_points:
                if (p.kinh_do, p.vi_do) == coords:
                    if point.ten_co_so and point.ten_co_so not in p.ten_co_so:
                        p.ten_co_so.extend(point.ten_co_so)
                    break

    points = unique_points

    if len(points) <= 3:
        # Nếu chỉ có 3 điểm hoặc ít hơn, không thể có đường giao nhau
        return points

    # Sử dụng thuật toán convex hull để tạo lộ trình ban đầu
    def convex_hull(points: List[DiaDiem]) -> List[DiaDiem]:
        def cross(o: DiaDiem, a: DiaDiem, b: DiaDiem) -> float:
            return (a.kinh_do - o.kinh_do) * (b.vi_do - o.vi_do) - (
                a.vi_do - o.vi_do
            ) * (b.kinh_do - o.kinh_do)

        points = sorted(points, key=lambda p: (p.kinh_do, p.vi_do))
        if len(points) <= 1:
            return points

        lower = []
        for p in points:
            while len(lower) >= 2 and cross(lower[-2], lower[-1], p) <= 0:
                lower.pop()
            lower.append(p)

        upper = []
        for p in reversed(points):
            while len(upper) >= 2 and cross(upper[-2], upper[-1], p) <= 0:
                upper.pop()
            upper.append(p)

        return lower[:-1] + upper[:-1]

    # Tạo ra vỏ lồi (convex hull) như một lộ trình ban đầu
    hull = convex_hull(points)
    remaining = [p for p in points if p not in hull]

    # Nếu tất cả các điểm nằm trên vỏ lồi, trả về vỏ lồi
    if not remaining:
        return hull

    # Thêm các điểm còn lại vào lộ trình bằng cách chèn chúng vào vị trí tốt nhất
    route = hull.copy()

    for point in remaining:
        best_pos = 0
        best_increase = float("inf")

        for i in range(len(route)):
            # Tính tăng khoảng cách khi chèn điểm vào vị trí i
            prev_dist = distance(route[i], route[(i + 1) % len(route)])
            new_dist = distance(route[i], point) + distance(
                point, route[(i + 1) % len(route)]
            )
            increase = new_dist - prev_dist

            # Tạm thời chèn điểm vào vị trí i+1
            route.insert(i + 1, point)

            # Kiểm tra xem lộ trình mới có giao nhau không
            if not has_intersections(route):
                if increase < best_increase:
                    best_increase = increase
                    best_pos = i + 1

            # Xóa điểm vừa chèn
            route.pop(i + 1)

        # Chèn điểm vào vị trí tốt nhất
        route.insert(best_pos, point)

    return route


def tim_dia_diem(xa: str, danh_sach_dia_diem: Dict[str, DiaDiem]) -> Optional[DiaDiem]:
    """
    Tìm địa điểm trong danh sách dựa trên tên xã.

    Args:
        xa: Tên xã cần tìm
        danh_sach_dia_diem: Từ điển các địa điểm

    Returns:
        Đối tượng DiaDiem nếu tìm thấy, None nếu không tìm thấy
    """
    xa = xa.lower().strip()

    # Xử lý đặc biệt cho Xuân Hòa
    if "xuân hòa" in xa.lower():
        # Kiểm tra xem có chứa "phúc yên" không
        if "phúc yên" in xa.lower():
            # Tìm tọa độ theo "xuân hòa phúc yên"
            if "xuân hòa phúc yên" in danh_sach_dia_diem:
                return danh_sach_dia_diem["xuân hòa phúc yên"]
        else:
            # Tìm tọa độ theo "xuân hòa lập thạch"
            if "xuân hòa lập thạch" in danh_sach_dia_diem:
                return danh_sach_dia_diem["xuân hòa lập thạch"]

    # Tìm địa điểm khớp chính xác
    if xa in danh_sach_dia_diem:
        return danh_sach_dia_diem[xa]

    # Tìm địa điểm có tên gần giống
    for ten, dia_diem in danh_sach_dia_diem.items():
        if xa in ten or ten in xa:
            return dia_diem

    return None


def reorder_route_with_start_point(
    route: List[DiaDiem], start_point_name: str
) -> List[DiaDiem]:
    """
    Sắp xếp lại lộ trình để bắt đầu từ một điểm cụ thể.

    Args:
        route: Lộ trình ban đầu
        start_point_name: Tên điểm bắt đầu

    Returns:
        Lộ trình mới bắt đầu từ điểm chỉ định
    """
    start_point_name = start_point_name.lower().strip()

    # Tìm vị trí của điểm bắt đầu trong lộ trình
    start_index = -1
    for i, diem in enumerate(route):
        if start_point_name in diem.ten:
            start_index = i
            break

    # Nếu không tìm thấy điểm bắt đầu, trả về lộ trình ban đầu
    if start_index == -1:
        print(f"Không tìm thấy điểm '{start_point_name}' trong lộ trình.")
        return route

    # Sắp xếp lại lộ trình để bắt đầu từ điểm chỉ định
    return route[start_index:] + route[:start_index]


def visualize_route(
    route: List[DiaDiem],
    title: str = "Lộ trình tối ưu",
    output_filename_base: str = "lo_trinh",
) -> None:
    """
    Vẽ lộ trình lên đồ thị.

    Args:
        route: Danh sách các điểm trên lộ trình
        title: Tiêu đề của đồ thị
        output_filename_base: Tên file cơ sở cho file output (không bao gồm phần mở rộng)
    """
    plt.figure(figsize=(12, 10))

    # Vẽ các điểm
    kinh_do = [diem.kinh_do for diem in route]
    vi_do = [diem.vi_do for diem in route]
    plt.scatter(kinh_do, vi_do, s=100, c="blue")

    # Vẽ các đoạn đường
    for i in range(len(route)):
        plt.plot(
            [route[i].kinh_do, route[(i + 1) % len(route)].kinh_do],
            [route[i].vi_do, route[(i + 1) % len(route)].vi_do],
            "k-",
        )

    # Đánh số các điểm
    for i, diem in enumerate(route):
        if i > 0:  # Chỉ đánh số cho các điểm sau điểm bắt đầu
            plt.text(
                diem.kinh_do, diem.vi_do, str(i), fontsize=12, ha="right", va="bottom"
            )

    # Đánh dấu điểm bắt đầu
    plt.scatter(route[0].kinh_do, route[0].vi_do, s=200, c="red", marker="*")
    plt.text(
        route[0].kinh_do,
        route[0].vi_do,
        "Bắt đầu",
        fontsize=14,
        ha="right",
        va="top",
        color="red",
    )

    plt.title(title)
    plt.xlabel("Kinh độ")
    plt.ylabel("Vĩ độ")
    plt.grid(True)

    # Lưu biểu đồ dưới dạng PNG (cho các hàm khác sử dụng)
    temp_png_path = f"{output_filename_base}.png"
    plt.savefig(temp_png_path, dpi=300, bbox_inches="tight")

    # Lưu biểu đồ dưới dạng PDF với đường dẫn mới
    pdf_path = f"{output_filename_base}_bieu_do.pdf"
    plt.savefig(pdf_path, format="pdf", bbox_inches="tight")
    # plt.show() # Bỏ comment nếu muốn hiển thị biểu đồ ngay lập tức


def export_to_pdf(
    route: List[DiaDiem],
    ten_co_so_map: Dict[str, List[str]],
    output_file: str = "lo_trinh.pdf",
    ngay_tham_dinh_str: str = "",
    image_filename_base: str = "lo_trinh_toi_uu",
) -> None:
    """
    Xuất danh sách lộ trình ra file PDF.

    Args:
        route: Danh sách các điểm trên lộ trình
        ten_co_so_map: Bảng ánh xạ từ tên xã đến danh sách các cơ sở
        output_file: Tên file PDF đầu ra
        ngay_tham_dinh_str: Chuỗi ngày thẩm định (ví dụ: "28/9/2023")
        image_filename_base: Tên file cơ sở của ảnh biểu đồ (không có phần mở rộng)
    """
    # Tạo document
    doc = SimpleDocTemplate(
        output_file,
        pagesize=A4,
        rightMargin=1 * cm,
        leftMargin=1 * cm,
        topMargin=1 * cm,
        bottomMargin=1 * cm,
    )

    # Thiết lập các styles
    styles = getSampleStyleSheet()
    title_style = ParagraphStyle(
        "TitleStyle",
        parent=styles["Heading1"],
        fontName=FONT_NAME,
        alignment=1,  # 0=Trái, 1=Giữa, 2=Phải
        fontSize=16,
        spaceAfter=12,
    )

    normal_style = ParagraphStyle(
        "NormalStyle",
        parent=styles["Normal"],
        fontName=FONT_NAME,
        fontSize=10,
        leading=12,
    )

    heading_style = ParagraphStyle(
        "HeadingStyle",
        parent=styles["Heading2"],
        fontName=FONT_NAME,
        fontSize=12,
        spaceAfter=6,
    )

    # Tạo nội dung PDF
    elements = []

    # Tiêu đề
    tieu_de_chinh = "LỘ TRÌNH THẨM ĐỊNH"
    if ngay_tham_dinh_str:
        tieu_de_chinh += f" - NGÀY {ngay_tham_dinh_str}"
    elements.append(Paragraph(tieu_de_chinh, title_style))
    elements.append(Spacer(1, 0.5 * cm))

    # Tính tổng số cơ sở
    tong_so_co_so = sum(len(co_so_list) for co_so_list in ten_co_so_map.values()) - 1

    # Thông tin tổng quát
    elements.append(Paragraph(f"Tổng số điểm đến: {len(route) - 1}", heading_style))
    elements.append(Paragraph(f"Tổng số cơ sở: {tong_so_co_so}", heading_style))

    # Hiển thị điểm xuất phát
    if len(route) > 0:
        diem_xuat_phat = route[0]
        elements.append(
            Paragraph(f"Điểm xuất phát: {diem_xuat_phat.ten.title()}", heading_style)
        )

        # Thêm khoảng cách từ điểm xuất phát đến điểm đầu tiên trong danh sách
        if len(route) > 1:
            khoang_cach_dau = distance(route[0], route[1])
            elements.append(
                Paragraph(
                    f"Khoảng cách đến điểm đầu tiên ({route[1].ten.title()}): {khoang_cach_dau:.2f} km",
                    normal_style,
                )
            )

    # Tính tổng khoảng cách
    tong_khoang_cach = 0
    for i in range(len(route)):
        diem_hien_tai = route[i]
        diem_ke_tiep = route[(i + 1) % len(route)]
        khoang_cach = distance(diem_hien_tai, diem_ke_tiep)
        tong_khoang_cach += khoang_cach

    elements.append(
        Paragraph(f"Tổng khoảng cách: {tong_khoang_cach:.2f} km", heading_style)
    )
    elements.append(Spacer(1, 0.5 * cm))

    # Bảng lộ trình
    data = [
        [
            "STT",
            "Địa điểm",
            "Cơ sở cần thẩm định",
            "Khoảng cách đến\nđiểm tiếp theo (km)",
        ]
    ]

    # Bắt đầu từ điểm thứ 2 (bỏ qua điểm đầu tiên là Sở Y tế)
    for i in range(1, len(route)):
        diem_hien_tai = route[i]
        diem_ke_tiep = route[(i + 1) % len(route)]
        khoang_cach = distance(diem_hien_tai, diem_ke_tiep)

        # Lấy danh sách cơ sở
        co_so_list = ten_co_so_map.get(diem_hien_tai.ten, [])

        if not co_so_list and hasattr(diem_hien_tai, "ten_co_so"):
            co_so_list = diem_hien_tai.ten_co_so

        # Nếu không có cơ sở nào, đặt thành một chuỗi rỗng
        if not co_so_list:
            co_so_str = "-"
        else:
            co_so_str = "\n".join(co_so_list)

        # Số thứ tự hiển thị là i thay vì i+1 vì đã bỏ qua điểm đầu tiên
        data.append(
            [str(i), diem_hien_tai.ten.title(), co_so_str, f"{khoang_cach:.2f}"]
        )

    # Tạo bảng với chiều rộng cột được điều chỉnh
    table = Table(data, colWidths=[1 * cm, 4 * cm, 9 * cm, 5 * cm])

    # Thiết lập style cho bảng
    table_style = TableStyle(
        [
            ("BACKGROUND", (0, 0), (-1, 0), colors.grey),
            ("TEXTCOLOR", (0, 0), (-1, 0), colors.whitesmoke),
            ("ALIGN", (0, 0), (-1, 0), "CENTER"),
            ("FONTNAME", (0, 0), (-1, 0), FONT_NAME),
            ("FONTSIZE", (0, 0), (-1, 0), 12),
            ("BOTTOMPADDING", (0, 0), (-1, 0), 12),
            ("BACKGROUND", (0, 1), (-1, -1), colors.beige),
            ("TEXTCOLOR", (0, 1), (-1, -1), colors.black),
            ("ALIGN", (0, 0), (0, -1), "CENTER"),
            ("ALIGN", (3, 0), (3, -1), "RIGHT"),
            ("FONTNAME", (0, 1), (-1, -1), FONT_NAME),
            ("FONTSIZE", (0, 1), (-1, -1), 10),
            ("VALIGN", (0, 0), (-1, -1), "MIDDLE"),
            ("GRID", (0, 0), (-1, -1), 1, colors.black),
        ]
    )

    table.setStyle(table_style)
    elements.append(table)

    # Thêm hình ảnh lộ trình nếu có
    image_path = f"{image_filename_base}.png"
    if os.path.exists(image_path):
        elements.append(Spacer(1, 1 * cm))
        elements.append(Paragraph("Bản đồ lộ trình:", heading_style))

        # Thêm hình ảnh vào PDF
        from reportlab.platypus import Image

        img = Image(image_path, width=18 * cm, height=15 * cm)
        elements.append(img)

    # Tạo PDF
    doc.build(elements)

    print(f"Đã xuất lộ trình ra file PDF: {output_file}")
    tele.send_file_normal(output_file)


def main() -> None:
    """
    Hàm chính của chương trình.
    """
    # Đường dẫn đến các file dữ liệu
    toa_do_file = "/home/<USER>/Dropbox/hnd/csv_source/toa_do.csv"
    lichthamdinh_file = "/home/<USER>/Dropbox/hnd/csv_source/lichthamdinh.csv"

    # Điểm bắt đầu lộ trình
    start_point_name = "sở y tế tỉnh vĩnh phúc"

    # Đọc dữ liệu từ các file
    danh_sach_dia_diem = load_toa_do(toa_do_file)
    danh_sach_tham_dinh_full = load_lich_tham_dinh(lichthamdinh_file)

    # Nhóm các địa điểm thẩm định theo ngày
    lich_theo_ngay: Dict[str, List[Tuple[str, str, str]]] = {}
    for stt, ten_co_so, dia_chi, ngay_td in danh_sach_tham_dinh_full:
        if ngay_td not in lich_theo_ngay:
            lich_theo_ngay[ngay_td] = []
        lich_theo_ngay[ngay_td].append((stt, ten_co_so, dia_chi))

    if not lich_theo_ngay:
        print("Không có lịch thẩm định nào được tìm thấy.")
        return

    # Xử lý cho từng ngày thẩm định
    for ngay_tham_dinh_str, danh_sach_tham_dinh_ngay in lich_theo_ngay.items():
        print(f"\n--- XỬ LÝ LỘ TRÌNH CHO NGÀY: {ngay_tham_dinh_str} ---")
        # Mảng lưu các địa điểm cần đi thẩm định cho ngày hiện tại
        dia_diem_tham_dinh_ngay = []

        # Dictionary ánh xạ từ tên xã đến danh sách tên cơ sở cho ngày hiện tại
        ten_co_so_map_ngay = {}
        dict_khong_khop_ngay = {}

        # Kết nối thông tin địa chỉ với tọa độ cho ngày hiện tại
        khop_ngay = True
        for _, ten_co_so, dia_chi in danh_sach_tham_dinh_ngay:
            xa = extract_xa_from_address(dia_chi)
            dia_diem = tim_dia_diem(xa, danh_sach_dia_diem)

            if dia_diem:
                print(f"Đã tìm thấy tọa độ cho {ten_co_so} tại {xa}: {dia_diem}")

                # Tạo bản sao của đối tượng DiaDiem để không ảnh hưởng đến đối tượng gốc
                dia_diem_copy = DiaDiem(dia_diem.ten, dia_diem.kinh_do, dia_diem.vi_do)
                dia_diem_copy.ten_co_so.append(ten_co_so)
                dia_diem_tham_dinh_ngay.append(dia_diem_copy)

                # Cập nhật map tên cơ sở cho ngày hiện tại
                if dia_diem.ten not in ten_co_so_map_ngay:
                    ten_co_so_map_ngay[dia_diem.ten] = []
                ten_co_so_map_ngay[dia_diem.ten].append(ten_co_so)
            else:
                dict_khong_khop_ngay[ten_co_so] = xa
                khop_ngay = False
                print(f"Không tìm thấy tọa độ cho {ten_co_so} tại {xa}")

        if not khop_ngay:
            message = (
                f"Không tìm thấy tọa độ cho các cơ sở vào ngày {ngay_tham_dinh_str}:\n"
            )
            for ten_co_so, xa in dict_khong_khop_ngay.items():
                message += f"- {ten_co_so} tại {xa}\n"
            send_notification(message, True)
            # Không thoát chương trình ngay, tiếp tục xử lý các ngày khác nếu có
            continue  # Bỏ qua ngày này và xử lý ngày tiếp theo

        # Thêm Sở Y Tế vào danh sách các điểm cần đi cho ngày hiện tại
        so_y_te = danh_sach_dia_diem.get(start_point_name)
        if so_y_te and not any(
            start_point_name in diem.ten for diem in dia_diem_tham_dinh_ngay
        ):
            print(f"Thêm điểm xuất phát: {so_y_te}")
            so_y_te_copy = DiaDiem(so_y_te.ten, so_y_te.kinh_do, so_y_te.vi_do)
            so_y_te_copy.ten_co_so.append("Sở Y Tế tỉnh Vĩnh Phúc")
            dia_diem_tham_dinh_ngay.append(so_y_te_copy)
            ten_co_so_map_ngay[so_y_te.ten] = ["Sở Y Tế tỉnh Vĩnh Phúc"]

        if len(dia_diem_tham_dinh_ngay) < 2:
            print(
                f"Không đủ địa điểm để tính toán lộ trình cho ngày {ngay_tham_dinh_str}."
            )
            continue  # Bỏ qua ngày này

        # Tìm lộ trình tối ưu cho ngày hiện tại
        lo_trinh_ngay = find_optimal_route(dia_diem_tham_dinh_ngay)

        # Sắp xếp lại lộ trình để bắt đầu từ Sở Y Tế
        lo_trinh_ngay = reorder_route_with_start_point(lo_trinh_ngay, start_point_name)

        # In ra lộ trình cho ngày hiện tại
        print(f"\nLộ trình tối ưu cho ngày {ngay_tham_dinh_str}:")
        tong_khoang_cach_ngay = 0
        for i in range(len(lo_trinh_ngay)):
            diem_hien_tai = lo_trinh_ngay[i]
            diem_ke_tiep = lo_trinh_ngay[(i + 1) % len(lo_trinh_ngay)]
            khoang_cach = distance(diem_hien_tai, diem_ke_tiep)
            tong_khoang_cach_ngay += khoang_cach

            print(f"{i + 1}. {diem_hien_tai} -> {diem_ke_tiep} ({khoang_cach:.2f} km)")

        print(
            f"\nTổng khoảng cách cho ngày {ngay_tham_dinh_str}: {tong_khoang_cach_ngay:.2f} km"
        )

        # Tạo tên file cơ sở dựa trên ngày (thay thế / bằng _ để an toàn cho tên file)
        ngay_file_base = ngay_tham_dinh_str.replace("/", "_")
        output_filename_base_ngay = f"lo_trinh_{ngay_file_base}"

        # Vẽ lộ trình cho ngày hiện tại
        visualize_route(
            lo_trinh_ngay,
            f"Lộ trình tối ưu ngày {ngay_tham_dinh_str} - Bắt đầu từ Sở Y Tế tỉnh Vĩnh Phúc",
            output_filename_base=output_filename_base_ngay,
        )

        # Xuất lộ trình ra file PDF cho ngày hiện tại
        pdf_output_filename_ngay = f"lo_trinh_tham_dinh_{ngay_file_base}.pdf"
        export_to_pdf(
            lo_trinh_ngay,
            ten_co_so_map_ngay,
            pdf_output_filename_ngay,
            ngay_tham_dinh_str=ngay_tham_dinh_str,
            image_filename_base=output_filename_base_ngay,
        )


if __name__ == "__main__":
    main()
