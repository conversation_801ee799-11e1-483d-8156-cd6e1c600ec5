from pykeepass import Py<PERSON>eePass
from create_snippets import creat_and_get
from typing import Dict
import os


def add_keepass_entry(
    keepass_file: str, password: str, entry_data: Dict[str, str]
) -> bool:
    """
    Thêm một mục mới vào file KeePass.

    Args:
        keepass_file: Đường dẫn đến file KeePass
        password: Mật khẩu của file KeePass
        entry_data: Dictionary chứa dữ liệu cho mục mới

    Returns:
        bool: True nếu thêm thành công, False nếu có lỗi
    """
    try:
        # Kiểm tra file tồn tại
        if not os.path.exists(keepass_file):
            print(f"Lỗi: File KeePass không tồn tại tại đường dẫn {keepass_file}")
            return False

        # Mở file KeePass
        kp = PyKeePass(keepass_file, password=password)

        # Thêm tài khoản mới
        _ = kp.add_entry(
            kp.root_group,  # Nhó<PERSON> gốc (hoặc chỉ định nhóm cụ thể)
            title=entry_data["Tên"],
            username=entry_data["username"],
            password=entry_data["password"],
            url=entry_data["url"],
            notes="",
        )

        # Lưu thay đổi vào file
        kp.save()
        return True

    except Exception as e:
        print(f"Lỗi khi thêm mục vào KeePass: {str(e)}")
        return False


# Chạy khi được gọi trực tiếp
if __name__ == "__main__":
    # Cấu hình
    keepass_file = "/home/<USER>/Dropbox/linux/kee.kdbx"
    password = "mfpw2025"

    # Tạo form nhập liệu
    dict_add = {
        "url": "https://sso.dancuquocgia.gov.vn",
        "Tên": "",
        "username": "",
        "password": "",
    }

    # Hiển thị UI và lấy giá trị người dùng nhập
    values = creat_and_get(dict_add)

    # Thêm vào KeePass
    add_keepass_entry(keepass_file, password, values)
