import os
import pandas as pd

from god_class import update_multi_with_vlookup, update_with_vlookup

os.chdir("/home/<USER>/Dropbox/hnd/csv_source")

df = pd.read_csv("thamdinh.csv", skiprows=4)
df.fillna("", inplace=True)

df["ma thu tuc"] = df["ve viec"].str.split(" - ").str[0]

df["dia chi THƯỜNG TRÚ"] = df["NGƯỜI ĐĂNG KÝ"].apply(
    lambda x: x.split(",", 1)[1].strip() if "," in x else ""
)

df["NGƯỜI ĐĂNG KÝ"] = df["NGƯỜI ĐĂNG KÝ"].str.split(",").str[0]

df["HẠN"] = df["TGQD HỒ SƠ"].str.split(" - Hạn xử lý: ").str[1].str.split(" - ").str[0]

df["ngay tiep nhan"] = (
    df["TGQD HỒ SƠ"].str.split(" - <PERSON><PERSON><PERSON> tiếp nhận: ").str[1].str.split(" - ").str[0]
)

df.rename(columns={"SỐ HỒ SƠ": "MÃ HỒ SƠ"}, inplace=True)

df = df[
    [
        "MÃ HỒ SƠ",
        "ma thu tuc",
        "NGƯỜI ĐĂNG KÝ",
        "dia chi THƯỜNG TRÚ",
        "ngay tiep nhan",
        "HẠN",
    ]
]

df["MÃ HỒ SƠ"] = df["MÃ HỒ SƠ"].str.strip()

df_thutuc = pd.read_csv("thu_tuc_duoc.csv")

update_with_vlookup(df, df_thutuc, "ma thu tuc", "TÊN TẮT")


df_name = pd.read_csv("name.csv")

update_with_vlookup(df_name, df, "MÃ HỒ SƠ", "HẠN")

col_to_update = [col for col in df if col != "MÃ HỒ SƠ"]

update_multi_with_vlookup(df_name, df, "MÃ HỒ SƠ", col_to_update)

df_to_add = df[~df["MÃ HỒ SƠ"].isin(df_name["MÃ HỒ SƠ"])]

# Thêm các hàng đã lọc vào df1
df_name = pd.concat([df_name, df_to_add], ignore_index=True)
from god_class import convert_and_sort_dates

df_name = convert_and_sort_dates(df_name, "HẠN")
df_name.to_csv("name.csv", index=False)
