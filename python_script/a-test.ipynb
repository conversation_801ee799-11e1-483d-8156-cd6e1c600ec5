{"cells": [{"cell_type": "code", "execution_count": 18, "id": "bcd63744", "metadata": {}, "outputs": [], "source": ["text='<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Tỉnh <PERSON><PERSON><PERSON>'"]}, {"cell_type": "code", "execution_count": 19, "id": "39fef895", "metadata": {}, "outputs": [{"data": {"text/plain": ["' <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Tỉnh <PERSON><PERSON><PERSON>'"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["text=text[::-1].split(',')\n", "text_reverse=text[-3][::-1] + ',' + text[-4][::-1] + ',' + text[-5][::-1]\n"]}, {"cell_type": "code", "execution_count": 21, "id": "2e1262aa", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Tỉnh <PERSON><PERSON><PERSON>\n", " <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Tỉnh <PERSON><PERSON><PERSON>\n", "<PERSON><PERSON><PERSON><PERSON>, Tỉnh <PERSON><PERSON><PERSON>\n"]}], "source": ["def get_last_three_parts(text):\n", "    # <PERSON><PERSON><PERSON> ng<PERSON> chuỗi và tách theo dấu phẩy\n", "    parts = text[::-1].split(',')\n", "    \n", "    # Lấy 3 phần tử cuối (hoặc ít hơn nếu không đủ 3)\n", "    last_three = parts[:3]\n", "    \n", "    # <PERSON><PERSON><PERSON> ng<PERSON><PERSON><PERSON> lại từng phần tử và nối chúng lại với dấu phẩy\n", "    result = ','.join(part[::-1] for part in last_three[::-1])\n", "    \n", "    return result\n", "\n", "# <PERSON><PERSON><PERSON> nghiệm với các chuỗi khác nhau\n", "text1 = '<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Tỉnh <PERSON><PERSON><PERSON>'\n", "print(get_last_three_parts(text1))  # Output: <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Tỉnh <PERSON><PERSON><PERSON>\n", "\n", "text2 = 'sdf<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Tỉnh <PERSON><PERSON><PERSON>'\n", "print(get_last_three_parts(text2))  # Output: <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Tỉnh <PERSON><PERSON><PERSON>\n", "\n", "text3 = '<PERSON><PERSON><PERSON><PERSON>, Tỉnh <PERSON><PERSON><PERSON>'\n", "print(get_last_three_parts(text3))  # Output: <PERSON><PERSON><PERSON><PERSON>, Tỉnh <PERSON><PERSON><PERSON>"]}], "metadata": {"kernelspec": {"display_name": "python313", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.1"}}, "nbformat": 4, "nbformat_minor": 5}