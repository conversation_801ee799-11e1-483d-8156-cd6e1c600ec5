import os


def cleanup_latex_files(directory):
    # Đ<PERSON><PERSON> nghĩa các định dạng file thường hay được sinh ra bởi LaTeX mà bạn muốn xoá
    latex_file_extensions = [
        ".aux",
        ".log~",
        ".log",
        ".out",
        ".gz",
        ".bbl",
        ".blg",
        ".fls",
        ".fdb_latexmk",
        ".tex#",
        ".xdv",
        ".tex~",
        ".pdf",
        ".synctex(busy)",
    ]

    # <PERSON><PERSON><PERSON>t qua tất cả các thư mục và file trong thư mục gốc
    for root, dirs, files in os.walk(directory):
        for file in files:
            # Kiểm tra nếu file có định dạng nằm trong danh sách cần xoá
            if any(file.endswith(ext) for ext in latex_file_extensions):
                # Tạo đường dẫn tuyệt đối để tránh sai sót
                file_path = os.path.join(root, file)
                # <PERSON>o<PERSON> file
                os.remove(file_path)


cleanup_latex_files("/home/<USER>/Dropbox/hnd/latexall")
cleanup_latex_files(
    "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/Latex/backup_vb_da_phat_hanh"
)
cleanup_latex_files("/home/<USER>/Dropbox/hnd/latexall/source_latex")
cleanup_latex_files("/home/<USER>/Dropbox/linux/backup/luat_txt")
