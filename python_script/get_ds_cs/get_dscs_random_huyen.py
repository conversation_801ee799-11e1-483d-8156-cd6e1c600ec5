import pandas as pd
import os
import numpy as np
from typing import Dict, List

os.chdir("/home/<USER>/Dropbox/hnd/csv_source")
df = pd.read_csv("dkkd.csv", dtype=str)
df.fillna("", inplace=True)
mask = df["ngay het han gpp"].str.contains("2027")
df = df[mask]
df["ngay het han gpp"] = pd.to_datetime(
    df["ngay het han gpp"], format="%d/%m/%Y", errors="coerce"
)
print(df.shape)

df["huyen"] = df["dia chi co so"].str[::-1].str.split(",").str[1].str[::-1]

# L<PERSON>y danh sách các huyện duy nhất
unique_huyen = df["huyen"].unique()
print(f"Số lượng huyện: {len(unique_huyen)}")
print(f"<PERSON>h sách các huyện: {unique_huyen}")

# Tạo DataFrame để lưu kết quả
result_df = pd.DataFrame()

# Với mỗi huyện, lấy ngẫu nhiên 5 cơ sở
for huyen in unique_huyen:
    huyen_df = df[df["huyen"] == huyen]
    if len(huyen_df) >= 5:
        random_samples = huyen_df.sample(n=5, random_state=42)
    else:
        random_samples = huyen_df.sample(n=len(huyen_df), random_state=42)
        print(f"Huyện {huyen} chỉ có {len(huyen_df)} cơ sở")

    result_df = pd.concat([result_df, random_samples])

# In ra số lượng cơ sở trong kết quả
print(f"Tổng số cơ sở được chọn: {len(result_df)}")

result_df = result_df[["ten qt-nt", "dia chi co so", "ten nguoi ptcm"]]
from god_class import insert_stt, dataframe_to_latex
insert_stt(result_df)
text = dataframe_to_latex(result_df)

with open("result.txt", "w") as f:
    f.write(text)


