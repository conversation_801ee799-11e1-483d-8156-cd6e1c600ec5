from abc import ABC, abstractmethod
import pandas as pd
import sys

sys.path.append("../python_script")
import csv_load_and_export as dl
from god_class import TextProcess, dataframe_to_latex


class ICosobanleProcess(ABC):
    @abstractmethod
    def process(self, df: pd.DataFrame) -> pd.DataFrame:
        pass


class CosobanleProcessor(ICosobanleProcess):
    def process(self, df: pd.DataFrame) -> pd.DataFrame:
        df = self._uppercase_business_name(df)
        df = self._titlecase_manager_name(df)
        df = self._extract_xa(df)
        df = self._process_decision_date(df)
        df = self._filter_by_time_range(df, "2025-01-01")
        df = self._sort_by_name(df)
        df = self._sort_by_date(df)
        df = self._select_columns(df)
        df = self._insert_stt(df)
        df = self._rename_columns(df)
        df = self._format_time(df)
        return df

    def _uppercase_business_name(self, df: pd.DataFrame) -> pd.DataFrame:
        df["TÊN CSKD"] = df["TÊN CSKD"].str.upper()
        return df

    def _filter_by_time_range(self, df: pd.DataFrame, start_date: str) -> pd.DataFrame:
        df = df[df["ngay qd cham dut"] >= pd.Timestamp(start_date)]
        return df

    def _format_time(self, df: pd.DataFrame) -> pd.DataFrame:
        df["ngay qd cham dut"] = df["ngay qd cham dut"].dt.strftime("%d/%m/%Y")
        return df

    def _sort_by_date(self, df: pd.DataFrame) -> pd.DataFrame:
        return df.sort_values("ngay qd cham dut")

    def _sort_by_name(self, df: pd.DataFrame) -> pd.DataFrame:
        return df.sort_values("TÊN CSKD", key=lambda col: col.str.lower())

    def _titlecase_manager_name(self, df: pd.DataFrame) -> pd.DataFrame:
        df["ten nguoi ptcm"] = df["ten nguoi ptcm"].str.title()
        return df

    def _extract_xa(self, df: pd.DataFrame) -> pd.DataFrame:
        df["xa"] = df["dia chi co so"].str[::-1].str.split(",").str[2].str[::-1]
        return df

    def _process_decision_date(self, df: pd.DataFrame) -> pd.DataFrame:
        df["ngay qd cham dut"] = pd.to_datetime(
            df["ngay qd cham dut"], format="%d/%m/%Y", errors="coerce"
        )
        df["ngay qd cham dut"] = df["ngay qd cham dut"].fillna(
            pd.Timestamp("1900-01-01")
        )
        return df

    def _remove_duplicates(self, df: pd.DataFrame) -> pd.DataFrame:
        return df.sort_values("ngay qd cham dut").drop_duplicates(
            subset=["TÊN CSKD", "xa"], keep="last"
        )

    def _insert_stt(self, df: pd.DataFrame) -> pd.DataFrame:
        df.insert(0, "stt", range(1, len(df) + 1))
        return df

    def _select_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        return df[
            [
                "TÊN CSKD",
                "dia chi co so",
                "ten nguoi ptcm",
                "ngay qd cham dut",
            ]
        ]

    def _rename_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        df.rename(
            columns={
                "TÊN CSKD": "Tên cơ sở bán lẻ thuốc",
                "dia chi co so": "Địa chỉ cơ sở",
                "ten nguoi ptcm": "Người phụ trách chuyên môn",
                "so dt chu hs": "Số điện thoại",
            },
            inplace=True,
        )
        return df

    def _to_latex(self, df: pd.DataFrame) -> str:
        return dataframe_to_latex(df)


class DataExporter(ABC):
    @abstractmethod
    def export(self, df: pd.DataFrame, path: str) -> None:
        pass


class ExcelExporter(DataExporter):
    def export(self, df: pd.DataFrame, path: str) -> None:
        df.to_csv(path, index=False)


class DataPipeline:
    def __init__(
        self,
        data_loader: dl.CsvLoader,
        data_processor: CosobanleProcessor,
        data_exporter: DataExporter,
    ):
        self.data_loader = data_loader
        self.data_processor = data_processor
        self.data_exporter = data_exporter

    def format_latex(self, df: pd.DataFrame) -> str:
        values = {}
        values["ds"] = dataframe_to_latex(df)
        f = TextProcess("kiem_tra_da_cham_dut")
        f.format_text(values)
        f.auto_day_van_ban("kiem_tra_da_cham_dut", "TB", "")

    def run(self, export_path: str) -> None:
        df = self.data_loader.load_df("ds_da_cham_dut")
        df = self.data_processor.process(df)
        self.data_exporter.export(df, export_path)
        self.format_latex(df)


# get dscs ban le khong trung ten trong pham vi xa/phuong/thi tran
if __name__ == "__main__":
    data_loader = dl.CsvLoaderFactory.create_basic_loader()
    data_processor = CosobanleProcessor()
    data_exporter = ExcelExporter()

    pipeline = DataPipeline(
        data_loader=data_loader,
        data_processor=data_processor,
        data_exporter=data_exporter,
    )

    pipeline.run("~/Dropbox/co_so_ban_le_da_cd_hd.csv")
