from abc import ABC, abstractmethod
import pandas as pd
import sys

sys.path.append("../python_script")
import csv_load_and_export as dl


class ICosobanleProcess(ABC):
    @abstractmethod
    def process(self, df: pd.DataFrame) -> pd.DataFrame:
        pass


class CongtympPrecessor(ICosobanleProcess):
    def process(self, df: pd.DataFrame) -> pd.DataFrame:
        df = self._uppercase_business_name(df)
        df = self._insert_stt(df)
        df = self._select_columns(df)
        df = self._rename_columns(df)
        return df

    def _uppercase_business_name(self, df: pd.DataFrame) -> pd.DataFrame:
        df["TEN_CONG_TY_MP"] = df["TEN_CONG_TY_MP"].str.upper()
        return df

    def _insert_stt(self, df: pd.DataFrame) -> pd.DataFrame:
        df.insert(0, "stt", range(1, len(df) + 1))
        return df

    def _select_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        return df[["stt", "TEN_CONG_TY_MP", "TRU_SO"]]

    def _rename_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        df.rename(
            columns={
                "TEN_CONG_TY_MP": "Tên công ty",
                "TRU_SO": "Địa chỉ cơ sở",
                "stt": "STT",
            },
            inplace=True,
        )
        return df


class DataExporter(ABC):
    @abstractmethod
    def export(self, df: pd.DataFrame, path: str) -> None:
        pass


class ExcelExporter(DataExporter):
    def export(self, df: pd.DataFrame, path: str) -> None:
        df.to_excel(path, index=False)


class DataPipeline:
    def __init__(
        self,
        data_loader: dl.CsvLoader,
        data_processor: CongtympPrecessor,
        data_exporter: DataExporter,
    ):
        self.data_loader = data_loader
        self.data_processor = data_processor
        self.data_exporter = data_exporter

    def run(self, export_path: str) -> None:
        df = self.data_loader.load_df("du_ct_mp")
        df = self.data_processor.process(df)
        self.data_exporter.export(df, export_path)


# get dscs ban le khong trung ten trong pham vi xa/phuong/thi tran
if __name__ == "__main__":
    data_loader = dl.CsvLoaderFactory.create_basic_loader()
    data_processor = CongtympPrecessor()
    data_exporter = ExcelExporter()

    pipeline = DataPipeline(
        data_loader=data_loader,
        data_processor=data_processor,
        data_exporter=data_exporter,
    )

    pipeline.run("~/Dropbox/ds_ct_mp.xlsx")
