from abc import ABC, abstractmethod
import pandas as pd
import sys

sys.path.append("../python_script")
import csv_load_and_export as dl


class ICosobanleProcess(ABC):
    @abstractmethod
    def process(self, df: pd.DataFrame) -> pd.DataFrame:
        pass


class CosobanleProcessor(ICosobanleProcess):
    def process(self, df: pd.DataFrame) -> pd.DataFrame:
        df = self._uppercase_business_name(df)
        df = self._titlecase_manager_name(df)
        df = self._extract_xa(df)
        df = self._process_decision_date(df)
        df = self._remove_duplicates(df)
        df = self.filter_truc_thuoc(df)
        df = self._sort_by_name(df)
        df = self._insert_stt(df)
        df = self._select_columns(df)
        df = self._rename_columns(df)
        return df

    def _uppercase_business_name(self, df: pd.DataFrame) -> pd.DataFrame:
        df["ten qt-nt"] = df["ten qt-nt"].str.upper()
        return df

    def _sort_by_name(self, df: pd.DataFrame) -> pd.DataFrame:
        return df.sort_values("ten qt-nt", key=lambda col: col.str.lower())

    def _titlecase_manager_name(self, df: pd.DataFrame) -> pd.DataFrame:
        df["ten nguoi ptcm"] = df["ten nguoi ptcm"].str.title()
        return df

    def _extract_xa(self, df: pd.DataFrame) -> pd.DataFrame:
        df["xa"] = df["dia chi co so"].str[::-1].str.split(",").str[2].str[::-1]
        return df

    def _process_decision_date(self, df: pd.DataFrame) -> pd.DataFrame:
        df["ngay qd"] = pd.to_datetime(
            df["ngay qd"], format="%d/%m/%Y", errors="coerce"
        )
        df["ngay qd"] = df["ngay qd"].fillna(pd.Timestamp("1900-01-01"))
        return df

    def _remove_duplicates(self, df: pd.DataFrame) -> pd.DataFrame:
        return df.sort_values("ngay qd").drop_duplicates(
            subset=["ten qt-nt", "xa"], keep="last"
        )

    def _insert_stt(self, df: pd.DataFrame) -> pd.DataFrame:
        df.insert(0, "stt", range(1, len(df) + 1))
        return df

    def filter_truc_thuoc(self, df: pd.DataFrame) -> pd.DataFrame:
        df["co quan chu quan"].fillna("0", inplace=True)
        return df[df["co quan chu quan"] != "0"]

    def _select_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        return df[
            [
                "stt",
                "ten qt-nt",
                "dia chi co so",
                "co quan chu quan",
                "dia chi co quan chu quan",
                "ten nguoi ptcm",
                "so dt chu hs",
            ]
        ]

    def _rename_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        df.rename(
            columns={
                "ten qt-nt": "Tên cơ sở bán lẻ thuốc",
                "dia chi co so": "Địa chỉ cơ sở",
                "ten nguoi ptcm": "Người phụ trách chuyên môn",
                "stt": "STT",
                "co quan chu quan": "Cơ quan chủ quản",
                "dia chi co quan chu quan": "Trụ sở",
                "so dt chu hs": "Số điện thoại",
            },
            inplace=True,
        )
        return df


class DataExporter(ABC):
    @abstractmethod
    def export(self, df: pd.DataFrame, path: str) -> None:
        pass


class ExcelExporter(DataExporter):
    def export(self, df: pd.DataFrame, path: str) -> None:
        df.to_excel(path, index=False)


class DataPipeline:
    def __init__(
        self,
        data_loader: dl.CsvLoader,
        data_processor: CosobanleProcessor,
        data_exporter: DataExporter,
    ):
        self.data_loader = data_loader
        self.data_processor = data_processor
        self.data_exporter = data_exporter

    def run(self, export_path: str) -> None:
        df = self.data_loader.load_df("du_lieu_gpp_all")
        df = self.data_processor.process(df)
        self.data_exporter.export(df, export_path)


# get dscs ban le khong trung ten trong pham vi xa/phuong/thi tran
if __name__ == "__main__":
    data_loader = dl.CsvLoaderFactory.create_basic_loader()
    data_processor = CosobanleProcessor()
    data_exporter = ExcelExporter()

    pipeline = DataPipeline(
        data_loader=data_loader,
        data_processor=data_processor,
        data_exporter=data_exporter,
    )

    pipeline.run("~/Dropbox/co_so_ban_le_truc_thuoc.xlsx")
