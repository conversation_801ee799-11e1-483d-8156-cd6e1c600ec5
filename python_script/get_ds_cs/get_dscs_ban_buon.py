from abc import ABC, abstractmethod
import pandas as pd
import sys

sys.path.append("/home/<USER>/Dropbox/hnd/python_script")
import csv_load_and_export as dl


class ICosobanbuonProcess(ABC):
    @abstractmethod
    def process(self, df: pd.DataFrame) -> pd.DataFrame:
        pass


class CosobanbuonProcessor(ICosobanbuonProcess):
    def process(self, df: pd.DataFrame) -> pd.DataFrame:
        df = self._uppercase_business_name(df)
        df = self._titlecase_manager_name(df)
        df = self.process_ngayqd(df)
        df = self._remove_duplicates(df)
        df = self._filter_trang_thai(df)
        df = self._insert_stt(df)
        df = self._select_columns(df)
        df = self.format_date(df, ["ngay qd"])
        df = self.format_dkkd(df)
        df = self.format_so_cchnd(df)
        df = self._rename_columns(df)
        return df

    def _uppercase_business_name(self, df: pd.DataFrame) -> pd.DataFrame:
        df["ten cong ty"] = df["ten cong ty"].str.upper()
        return df

    def _titlecase_manager_name(self, df: pd.DataFrame) -> pd.DataFrame:
        df["ten nguoi ptcm"] = df["ten nguoi ptcm"].str.title()
        return df

    def process_ngayqd(self, df: pd.DataFrame) -> pd.DataFrame:
        df["ngay qd"] = pd.to_datetime(
            df["ngay qd"], format="%d/%m/%Y", errors="coerce"
        )
        df["ngay qd"] = df["ngay qd"].fillna(pd.Timestamp("1900-01-01"))
        return df

    def _remove_duplicates(self, df: pd.DataFrame) -> pd.DataFrame:
        return df.sort_values("ngay qd").drop_duplicates(
            subset=["ten cong ty"], keep="last"
        )

    def _insert_stt(self, df: pd.DataFrame) -> pd.DataFrame:
        df.insert(0, "stt", range(1, len(df) + 1))
        return df

    def _select_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        return df[
            [
                "stt",
                "ten cong ty",
                "tru so",
                "ten nguoi ptcm",
                "so cchnd",
                "ngay cap cchnd",
                "noi cap cchnd",
                "so dt chu hs",
                "so_dkkd",
                "ngay qd",
                "pham vi kd",
                "ngay het han gdp",
                "so_gdp",
            ]
        ]

    def format_so_cchnd(self, df: pd.DataFrame) -> pd.DataFrame:
        df["so cchnd"] = df["so cchnd"].apply(
            lambda x: x + "/CCHND-SYT-VP" if "/" not in str(x) else x
        )
        return df

    def format_dkkd(self, df: pd.DataFrame) -> pd.DataFrame:
        df["so_dkkd"] = df["so_dkkd"] + "/ĐKKDD-VP"
        return df

    def format_date(self, df: pd.DataFrame, column_list: list[str]) -> pd.DataFrame:
        """
        Định dạng các cột ngày tháng trong DataFrame theo định dạng '%d/%m/%Y'.

        Args:
            df (pd.DataFrame): DataFrame chứa dữ liệu cần xử lý.
            column_list (list[str]): Danh sách tên các cột cần định dạng ngày tháng.

        Returns:
            pd.DataFrame: DataFrame sau khi đã định dạng các cột ngày tháng.
        """
        for column in column_list:
            df[column] = df[column].dt.strftime("%d/%m/%Y")
            df[column] = df[column].fillna("1900-01-01")
        return df

    def _rename_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        df.rename(
            columns={
                "ten cong ty": "Tên công ty",
                "tru so": "Địa chỉ",
                "ten nguoi ptcm": "Người phụ trách chuyên môn",
                "stt": "STT",
                "so dt chu hs": "Số điện thoại",
                "so cchnd": "Số chứng chỉ hành nghề dược",
                "ngay cap cchnd": "Ngày cấp chứng chỉ hành nghề dược",
                "noi cap cchnd": "Nơi cấp chứng chỉ hành nghề dược",
                "trinh do cm": "Trình độ chuyên môn",
                "so_dkkd": "Số Giấy chứng nhận đủ điều kiện kinh doanh dược",
                "ngay qd": "Ngày cấp",
            },
            inplace=True,
        )
        return df

    def _filter_trang_thai(self, df: pd.DataFrame) -> pd.DataFrame:
        return df[~df["tinh trang"].isin(["stop", "pause"])]


class DataExporter(ABC):
    @abstractmethod
    def export(self, df: pd.DataFrame, path: str) -> None:
        pass


class ExcelExporter(DataExporter):
    def export(self, df: pd.DataFrame, path: str) -> None:
        df.to_excel(path, index=False)


class DataPipeline:
    def __init__(
        self,
        data_loader: dl.CsvLoader,
        data_processor: CosobanbuonProcessor,
        data_exporter: DataExporter,
    ):
        self.data_loader = data_loader
        self.data_processor = data_processor
        self.data_exporter = data_exporter

    def run(self, export_path: str) -> None:
        df = self.data_loader.load_df("gdp")
        df = self.data_processor.process(df)
        self.data_exporter.export(df, export_path)


# get dscs ban le khong trung ten trong pham vi xa/phuong/thi tran
if __name__ == "__main__":
    data_loader = dl.CsvLoaderFactory.create_basic_loader()
    data_processor = CosobanbuonProcessor()
    data_exporter = ExcelExporter()

    pipeline = DataPipeline(
        data_loader=data_loader,
        data_processor=data_processor,
        data_exporter=data_exporter,
    )

pipeline.run("~/Dropbox/co_so_ban_buon.xlsx")
