from abc import ABC, abstractmethod
import pandas as pd
from icecream import ic

import sys

# Thêm đường dẫn tuyệt đối vào sys.path
sys.path.append("/home/<USER>/Dropbox/hnd/python_script")

from god_class import insert_stt

import csv_load_and_export as dl


class ICosobanleProcess(ABC):
    @abstractmethod
    def process(self, df: pd.DataFrame) -> pd.DataFrame:
        pass


class CosobanleProcessor(ICosobanleProcess):
    def process(self, df: pd.DataFrame) -> pd.DataFrame:
        df = self._uppercase_business_name(df)
        df = self._titlecase_manager_name(df)
        df = self._extract_xa(df)
        df = self._process_decision_date(df)
        df = self._remove_duplicates(df)
        # self._filter_nguoi_nha_nuoc_and_export(df)
        # self._filter_nt_and_export(df)
        # self._filter_qt_and_export(df)
        # self._filter_huyen(df, "phúc yên")
        # self._filter_thuoc_and_export(df)
        df = self._insert_stt(df)
        df = self._select_columns(df)
        df = self.format_date(df, ["ngay qd"])
        df = self.format_dkkd(df)
        df = self.format_so_cchnd(df)
        df = self._rename_columns(df)
        # self.count_trinh_do_cm(df)
        return df

    def _filter_huyen(self, df: pd.DataFrame, huyen: str) -> pd.DataFrame:
        df1 = df[df["dia chi co so"].str.lower().str.contains(huyen)]
        insert_stt(df1)
        df1.to_excel(f"~/Dropbox/ds_{huyen}.xlsx", index=False)

    def _filter_nt_and_export(self, df: pd.DataFrame) -> pd.DataFrame:
        df2 = df[df["ten qt-nt"].str.lower().str.contains("nhà thuốc")]
        insert_stt(df2)
        df2.to_csv("~/Dropbox/ds_nt.csv", index=False)

    def _filter_thuoc_and_export(self, df: pd.DataFrame) -> pd.DataFrame:
        df.fillna("", inplace=True)
        df2 = df[df["ten qt-nt"].str.lower().str.contains("thuốc")]
        df2 = df2[df2["dia chi co so"].str.len() > 10]
        insert_stt(df2)
        df2 = self._select_columns(df2)
        df2 = self._rename_columns(df2)
        df2.to_excel("~/Dropbox/ds_co_so_ban_le.xlsx", index=False)

    def _filter_qt_and_export(self, df: pd.DataFrame) -> pd.DataFrame:
        df3 = df[df["ten qt-nt"].str.lower().str.contains("quầy thuốc")]
        insert_stt(df3)
        df3.to_csv("~/Dropbox/ds_qt.csv", index=False)

    def _filter_nguoi_nha_nuoc_and_export(self, df: pd.DataFrame) -> pd.DataFrame:
        df4 = df[df["ten co so dang ky hn"].str.len() > 1]
        insert_stt(df4)
        df4.to_csv("~/Dropbox/ds_nguoi_nha_nuoc.csv", index=False)
        df4 = df4[
            [
                "stt",
                "ten qt-nt",
                "dia chi co so",
                "ten nguoi ptcm",
                "ten co so dang ky hn",
                "so dt chu hs",
            ]
        ]
        df4.to_excel("~/Dropbox/ds_nguoi_nha_nuoc.xlsx", index=False)

    def _uppercase_business_name(self, df: pd.DataFrame) -> pd.DataFrame:
        df["ten qt-nt"] = df["ten qt-nt"].str.upper()
        return df

    def _titlecase_manager_name(self, df: pd.DataFrame) -> pd.DataFrame:
        df["ten nguoi ptcm"] = df["ten nguoi ptcm"].str.title()
        return df

    def _extract_xa(self, df: pd.DataFrame) -> pd.DataFrame:
        df["xa"] = df["dia chi co so"].str[::-1].str.split(",").str[2].str[::-1]
        return df

    def _process_decision_date(self, df: pd.DataFrame) -> pd.DataFrame:
        df["ngay qd"] = pd.to_datetime(
            df["ngay qd"], format="%d/%m/%Y", errors="coerce"
        )
        df["ngay qd"] = df["ngay qd"].fillna(pd.Timestamp("1900-01-01"))
        return df

    def _remove_duplicates(self, df: pd.DataFrame) -> pd.DataFrame:
        return df.sort_values("ngay qd").drop_duplicates(
            subset=["ten qt-nt", "xa"], keep="last"
        )

    def format_date(self, df: pd.DataFrame, column_list: list[str]) -> pd.DataFrame:
        """
        Định dạng các cột ngày tháng trong DataFrame theo định dạng '%d/%m/%Y'.

        Args:
            df (pd.DataFrame): DataFrame chứa dữ liệu cần xử lý.
            column_list (list[str]): Danh sách tên các cột cần định dạng ngày tháng.

        Returns:
            pd.DataFrame: DataFrame sau khi đã định dạng các cột ngày tháng.
        """
        for column in column_list:
            df[column] = df[column].dt.strftime("%d/%m/%Y")
            df[column] = df[column].fillna("1900-01-01")
        return df

    def _insert_stt(self, df: pd.DataFrame) -> pd.DataFrame:
        df.insert(0, "stt", range(1, len(df) + 1))
        return df
    
    def format_dkkd(self, df: pd.DataFrame) -> pd.DataFrame:
        df["so dkkd"] = df["so dkkd"] + "/ĐKKDD-VP"
        return df
    
    def format_so_cchnd(self, df: pd.DataFrame) -> pd.DataFrame:
        df["so cchnd"] = df["so cchnd"].apply(
            lambda x: x + "/CCHND-SYT-VP" if "/" not in str(x) else x
        )
        return df

    def _select_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        return df[
            [
                "stt",
                "ten qt-nt",
                "dia chi co so",
                "ten nguoi ptcm",
                "so cchnd",
                "ngay cap cchnd",
                "noi cap cchnd",
                "trinh do cm",
                "so dt chu hs",
                "so dkkd",
                "ngay qd",
            ]
        ]

    def count_trinh_do_cm(self, df: pd.DataFrame) -> pd.DataFrame:
        count_series = df["trinh do cm"].value_counts()
        count_df = count_series.reset_index()
        count_df.columns = ["trinh do cm", "count"]
        return ic(count_df)

    def _rename_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        df.rename(
            columns={
                "ten qt-nt": "Tên cơ sở bán lẻ thuốc",
                "dia chi co so": "Địa chỉ cơ sở",
                "ten nguoi ptcm": "Người phụ trách chuyên môn",
                "stt": "STT",
                "so cchnd": "Số chứng chỉ hành nghề dược",
                "ngay cap cchnd": "Ngày cấp chứng chỉ hành nghề dược",
                "noi cap cchnd": "Nơi cấp chứng chỉ hành nghề dược",
                "trinh do cm": "Trình độ chuyên môn",
                "so dt chu hs": "Số điện thoại",
                "so dkkd": "Số đăng ký kinh doanh",
                "ngay qd": "Ngày cấp",
            },
            inplace=True,
        )
        return df


class DataExporter(ABC):
    @abstractmethod
    def export(self, df: pd.DataFrame, path: str) -> None:
        pass


class ExcelExporter(DataExporter):
    def export(self, df: pd.DataFrame, path: str) -> None:
        df.to_excel(path, index=False)


class DataPipeline:
    def __init__(
        self,
        data_loader: dl.CsvLoader,
        data_processor: CosobanleProcessor,
        data_exporter: DataExporter,
    ):
        self.data_loader = data_loader
        self.data_processor = data_processor
        self.data_exporter = data_exporter

    def run(self, export_path: str) -> None:
        df = self.data_loader.load_df("du_lieu_gpp_all")
        # df = self.data_loader.load_df("dshn_duoc")
        df = self.data_processor.process(df)
        self.data_exporter.export(df, export_path)


# get dscs ban le khong trung ten trong pham vi xa/phuong/thi tran
if __name__ == "__main__":
    data_loader = dl.CsvLoaderFactory.create_basic_loader()
    data_processor = CosobanleProcessor()
    data_exporter = ExcelExporter()

    pipeline = DataPipeline(
        data_loader=data_loader,
        data_processor=data_processor,
        data_exporter=data_exporter,
    )

    pipeline.run("~/Dropbox/co_so_ban_le.xlsx")
