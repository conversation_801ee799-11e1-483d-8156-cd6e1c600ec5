import os

import pandas as pd
from loguru import logger
from god_class import PandasCsv, convert_ngay
from top_most_get_text import input_dialog

os.chdir("/home/<USER>/Dropbox/hnd/csv_source")


@logger.catch
def run_main():
    df1 = pd.read_csv("dkkd.csv", dtype=str, index_col="so cchnd")
    df2 = pd.read_csv("dshn_duoc.csv", dtype=str, index_col="so cchnd")
    df3 = pd.read_csv("du_lieu_gpp_all.csv", dtype=str, index_col="so cchnd")
    df4 = pd.read_csv("cchnd.csv", dtype=str, index_col="so cchnd")
    so_cc = input_dialog("NHẬP", "Nhập so cchnd", "") + "/CCHND-SYT-VP"
    if not so_cc:
        return
    if so_cc in df1.df.index:
        default = df1.df.at[so_cc, "ngay cap cchnd"]
    else:
        default = ""
    if isinstance(default, pd.Series):
        default = default.iloc[-1]
    ngay_cap_cu = convert_ngay(input_dialog("NHẬP", "Nhập ngày cấp cũ", default))
    if not ngay_cap_cu:
        return
    ngay_cap_moi = convert_ngay(input_dialog("NHẬP", "Nhập ngày cấp mới", ""))
    if not ngay_cap_moi:
        return
    for df in [df1, df2, df3]:
        mask = (df.df.index == so_cc) & (df.df["ngay cap cchnd"] == ngay_cap_cu)
        df.set_loc_with_mask(mask, ngay_cap_moi, "ngay cap cchnd")
        df.to_csv(df.data, "so cchnd")
    mask2 = (df4.df.index == so_cc) & (df4.df["ngay qd"] == ngay_cap_cu)
    df4.set_loc_with_mask(mask2, ngay_cap_moi[0:2], "ngay")
    df4.to_csv(df4.data, "so cchnd")


if __name__ == "__main__":
    run_main()
