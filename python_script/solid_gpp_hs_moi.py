import os
import subprocess
import pandas as pd

from god_class import (
    get_bottom_values,
    get_dict_from_index_df,
    custom_assert,
    convert_ngay,
    phone_format,
    setup_logging,
    update_df_name_da_<PERSON>han,
    update_df_from_dict_by_index,
    close_app,
    send_notification,
)
from all_pickle import load_pickle
from god_class import show_message
from top_most_get_text import input_dialog


setup_logging("gpp_hs_cap_moi.log")


def gpp_hs_moi(mhs, thutuc="CAP GPP VA DKKD"):
    os.chdir("/home/<USER>/Dropbox/hnd/csv_source")
    df_dshn = pd.read_csv("dshn_duoc.csv", dtype=str, index_col="so cchnd")
    df_dkkd = pd.read_csv("dkkd.csv", dtype=str, index_col="ma ho so")
    # TODO lấy ra số điện tho<PERSON>i theo hành ch<PERSON>h công từ name.csv
    df_name = pd.read_csv("name.csv", dtype=str, index_col="ma ho so")
    sdt = df_name.loc[mhs, "so dt chu hs"]

    # TODO CÁC HÀM

    # TODO 1.lấy index là so cchnd
    so_cchnd = input_dialog("NHẬP", "NHẬP SỐ CCHND", "")
    # TODO 1.1 kiểm tra xem đã hành nghề ở đâu chưa
    if not so_cchnd:
        return
    df_gpp = pd.read_csv("du_lieu_gpp_all.csv", dtype="str", index_col="so cchnd")
    if so_cchnd in df_gpp.index:
        found = (
            "ĐANG ĐỨNG TÊN Ở "
            + get_bottom_values(df_gpp, so_cchnd, "ten qt-nt")
            + " SĐT: "
            + get_bottom_values(df_gpp, so_cchnd, "so dt chu hs")
            + " so dkkd: "
            + get_bottom_values(df_gpp, so_cchnd, "so dkkd")
        )
        show_message("THÔNG BÁO", found)

    df_cham_dut = pd.read_csv("ds_da_cham_dut.csv", dtype="str", index_col="so cchnd")
    if so_cchnd in df_cham_dut.index:
        found = (
            "ĐÃ cham dut: "
            + get_bottom_values(df_cham_dut, so_cchnd, "ten qt-nt")
            + "\nngay qd: "
            + get_bottom_values(df_cham_dut, so_cchnd, "ngay qd")
        )
        send_notification(found)

    # TODO 2. lấy ra dòng cuối cùng trong file  excel
    dict_vals = get_dict_from_index_df(df_dshn, so_cchnd)
    if not dict_vals["ngay cap cchnd"]:
        df_cchnd = pd.read_csv("cchnd.csv", dtype=str, index_col="so cchnd")
        try:
            dict_vals["ngay cap cchnd"] = get_bottom_values(
                df_cchnd, so_cchnd, "ngay qd"
            )
        except:
            dict_vals["ngay cap cchnd"] = ""
    check_cchnd = input_dialog(
        "KIỂM TRA NGÀY CẤP CCHN",
        "NGÀY CẤP CCHND CÓ ĐÚNG KHÔNG",
        dict_vals["ngay cap cchnd"],
    )
    if not check_cchnd:
        show_message(
            "THÔNG BÁO", "NGÀY CẤP CCHND KHÔNG ĐÚNG\nCÓ THỂ SỬ DỤNG CCHND ĐÃ BỊ THU HỒI"
        )
        return

    # TODO 3. SETUP CÁC NHÓM

    list_include = get_include_list(df_dshn, dict_vals)

    list_ngay = ["ngay cap cchnd", "ngay tot nghiep", "ngay sinh", "ngay cap cmnd"]
    dict_radio = {
        "pham vi kd": ["thường", "lạnh", "mát lạnh"],
        "NGHIỆN, HƯỚNG THẦN, TIỀN CHẤT": ["Không", "Có"],
    }
    dict_widget = {}
    dict_combo = {}
    list_title = []
    list_phone = []

    list_upper = ["ten nguoi ptcm", "ten qt-nt"]

    default_values = {
        "noi cap cchnd": "Sở Y tế tỉnh Phú Thọ",
        "gioi tinh": "0",
        "ngay cap cchnd": convert_ngay(check_cchnd),
        "noi cap cmnd": "Cục Cảnh sát Quản lý hành chính về trật tự xã hội",
        "bao quan lanh": "0",
        "truc thuoc": "0",
        "ĐÃ CÓ CẬP NHẬT KT CHƯA": "1",
        "CÓ truc thuoc KHÔNG": "0",
        "co nhan vien khong": "0",
        "co quan chu quan": "0",
        "dia chi co quan chu quan": "0",
        "CÓ PHẠM VI BÁN LẺ THUỐC KHÔNG": "",
        "CHỮ KÝ VÀ TÊN TRONG DKKD": "HỢP LỆ",
        "loai hinh": dict_vals["vi tri hanh nghe"],
    }

    multiline = ""

    # TODO 5.xử lý dữ liệu trước khi ghi vào excel
    def func(values):
        dict_to_update = {"ma ho so": mhs, "thu tuc": thutuc, "so cchnd": so_cchnd}
        values.update(dict_to_update)
        if not_loai_hinh_in_name(values):
            values["ten qt-nt"] = values["loai hinh"] + " " + values["ten qt-nt"]

        if is_quay_thuoc(values):
            if values["pham vi kd"] == "thường":
                values["pham vi kd"] = (
                    "Mua và bán lẻ thuốc thuộc Danh mục thuốc thiết yếu và Danh mục thuốc không kê đơn (có bao gồm: thuốc dạng phối hợp có chứa dược chất gây nghiện; thuốc dạng phối hợp có chứa dược chất hướng thần; thuốc dạng phối hợp có chứa tiền chất; thuốc độc; thuốc trong danh mục thuốc, dược chất thuộc danh mục chất bị cấm sử dụng trong một số ngành, lĩnh vực), trừ vắc xin; bảo quản ở điều kiện thường."
                )
            elif is_phamvi_lanh(values):
                values["pham vi kd"] = (
                    "Mua và bán lẻ thuốc thuộc Danh mục thuốc thiết yếu và Danh mục thuốc không kê đơn (có bao gồm: thuốc dạng phối hợp có chứa dược chất gây nghiện; thuốc dạng phối hợp có chứa dược chất hướng thần; thuốc dạng phối hợp có chứa tiền chất; thuốc độc; thuốc trong danh mục thuốc, dược chất thuộc danh mục chất bị cấm sử dụng trong một số ngành, lĩnh vực), trừ vắc xin; bảo quản ở điều kiện thường, bảo quản lạnh."
                )
            else:
                values["pham vi kd"] = (
                    "Mua và bán lẻ thuốc thuộc Danh mục thuốc thiết yếu và Danh mục thuốc không kê đơn (có bao gồm: thuốc dạng phối hợp có chứa dược chất gây nghiện; thuốc dạng phối hợp có chứa dược chất hướng thần; thuốc dạng phối hợp có chứa tiền chất; thuốc độc; thuốc trong danh mục thuốc, dược chất thuộc danh mục chất bị cấm sử dụng trong một số ngành, lĩnh vực), trừ vắc xin; bảo quản ở điều kiện thường, bảo quản mát, bảo quản lạnh."
                )

        else:
            if values["pham vi kd"] == "thường":
                values["pham vi kd"] = (
                    "Mua thuốc để bán lẻ (có bao gồm: thuốc dạng phối hợp có chứa dược chất gây nghiện; thuốc dạng phối hợp có chứa dược chất hướng thần; thuốc dạng phối hợp có chứa tiền chất; thuốc độc; thuốc trong danh mục thuốc, dược chất thuộc danh mục chất bị cấm sử dụng trong một số ngành, lĩnh vực), trừ vắc xin; bảo quản ở điều kiện thường."
                )
            elif is_phamvi_lanh(values):
                values["pham vi kd"] = (
                    "Mua thuốc để bán lẻ (có bao gồm: thuốc dạng phối hợp có chứa dược chất gây nghiện; thuốc dạng phối hợp có chứa dược chất hướng thần; thuốc dạng phối hợp có chứa tiền chất; thuốc độc; thuốc trong danh mục thuốc, dược chất thuộc danh mục chất bị cấm sử dụng trong một số ngành, lĩnh vực), trừ vắc xin; bảo quản ở điều kiện thường, bảo quản lạnh."
                )
            else:
                values["pham vi kd"] = (
                    "Mua thuốc để bán lẻ (có bao gồm: thuốc dạng phối hợp có chứa dược chất gây nghiện; thuốc dạng phối hợp có chứa dược chất hướng thần; thuốc dạng phối hợp có chứa tiền chất; thuốc độc; thuốc trong danh mục thuốc, dược chất thuộc danh mục chất bị cấm sử dụng trong một số ngành, lĩnh vực), trừ vắc xin; bảo quản ở điều kiện thường, bảo quản mát, bảo quản lạnh."
                )

        if is_nghien_huongthan(values):
            values["pham vi kd"] = values["pham vi kd"].replace(
                "có bao gồm:",
                "có bao gồm: thuốc gây nghiện, thuốc hướng tâm thần, thuốc tiền chất,",
            )

        if not_duoc_in_tdcm(values):
            values["trinh do cm"] = (values["trinh do cm"] + " dược").strip()
        custom_assert(
            [
                (
                    values["trinh do cm"]
                    in ["Cao đẳng dược", "Trung cấp dược", "Đại học dược"],
                    "trinh do cm KHÔNG ĐÚNG",
                )
            ]
        )
        values["trinh do tat"] = (
            values["trinh do cm"]
            .replace("Cao đẳng dược", "cao đẳng")
            .replace("Trung cấp dược", "trung cấp")
            .replace("Đại học dược", "đại học")
        )
        values["da nhan"] = "1"
        for key in list_ngay:
            values[key] = convert_ngay(values[key])
        for key in list_phone:
            values[key] = phone_format(values[key])
        for key in list_upper:
            values[key] = values[key].upper()

        update_df_name_da_nhan(df_name, values, mhs)
        update_df_from_dict_by_index(df_dkkd, "dkkd.csv", values, mhs)
        update_df_from_dict_by_index(df_dshn, "dshn_duoc.csv", values, so_cchnd)

        if len(values["co quan chu quan"]) > 2:
            show_message(
                "THÔNG BÁO",
                "XEM KỸ LẠI CHỮ KÝ VÀ CON DẤU TRONG ĐƠN ĐỀ NGHỊ, TRA CỨU pham vi kd CỦA DOANH NGHIỆP",
            )
        values["so dt chu hs"] = sdt
        close_app("okular")
        subprocess.run("wmctrl -xa file_hs.file_hs", shell=True)
        send_notification("ĐÃ XONG")

    def not_duoc_in_tdcm(values):
        return "dược" not in values["trinh do cm"]

    def is_nghien_huongthan(values):
        return values["NGHIỆN, HƯỚNG THẦN, TIỀN CHẤT"] == "Có"

    def is_phamvi_lanh(values):
        return values["pham vi kd"] == "lạnh"

    def is_quay_thuoc(values):
        return values["loai hinh"] == "Quầy thuốc"

    def not_loai_hinh_in_name(values):
        return (
            "NHÀ THUỐC" not in values["ten qt-nt"]
            and "QUẦY THUỐC" not in values["ten qt-nt"]
        )

    one_index(
        df_dshn,
        list_include,
        so_cchnd,
        "so cchnd",
        dict_radio,
        dict_combo,
        dict_widget,
        list_upper,
        list_title,
        default_values,
        multiline,
        list_ngay,
        list_phone,
        thutuc,
        func,
        0,
        100,
        600,
        1000,
    )


def get_include_list(df, excel_values):
    if is_nhanvien_noikhac(excel_values):
        # TODO check nếu đang là nhân viên nơi khác.
        exclude = [
            "so cchnd",
            "ma ho so",
            "loai cap",
            "so qd",
            "ngay",
            "thang",
            "nam",
            "ngay qd",
            "HẠN XỬ LÝ",
            "pham vi hanh nghe cu",
            "dang ky pham vi",
            "so cchnd cu",
            "ngay cchnd cu",
            "cong ty xac nhan",
            "noi dung thuc hanh",
            "ngay bat dau th",
            "ngay ket thuc th",
            "noi dung dieu chinh",
            "ngay ket thuc th.1",
            "thu tuc",
            "ten cong ty",
            "trinh do tat",
            "da nhan",
            "CHỜ TRẢ",
            "ten nguoi ptcm",
            "ngay td",
            "so gpp",
            "so dkkd",
            "ngay het han gpp",
            "ly do CẤP LẠI",
            "Unnamed: 8",
            "LẦN",
            "GHI CHÚ",
            "Đã có CNKT chưa?",
            "so qd",
        ]
    else:
        exclude = [
            "so cchnd",
            "ma ho so",
            "loai cap",
            "so qd",
            "ngay",
            "thang",
            "nam",
            "ngay qd",
            "HẠN XỬ LÝ",
            "pham vi hanh nghe cu",
            "dang ky pham vi",
            "so cchnd cu",
            "ngay cchnd cu",
            "cong ty xac nhan",
            "ten cong ty",
            "noi dung thuc hanh",
            "ngay bat dau th",
            "ngay ket thuc th",
            "noi dung dieu chinh",
            "ngay ket thuc th.1",
            "thu tuc",
            "trinh do tat",
            "da nhan",
            "CHỜ TRẢ",
            "ten nguoi ptcm",
            "ngay td",
            "so gpp",
            "so dkkd",
            "ngay het han gpp",
            "ly do CẤP LẠI",
            "Unnamed: 8",
            "SỐ GCN ĐĐKKDD CƠ SỞ ĐĂNG KÝ HN",
            "ten nhan vien",
            "thoi gian dang ky lam viec",
            "vi tri chuyen mon",
            "NGÀY VÀO",
            "LẦN",
            "GHI CHÚ",
            "Đã có CNKT chưa?",
            "so qd",
        ]
    exclude.extend(
        [
            "so dkkd cu",
            "ngay dkkd cu",
            "so gpp CŨ",
            "ngay gpp cu",
            "ngay qd CẤP",
            "ngay han",
        ]
    )
    include_list2 = [col for col in df.columns.tolist() if col not in exclude]
    return include_list2


def is_nhanvien_noikhac(excel_values):
    return excel_values.get("thoi gian dang ky lam viec") != ""


if __name__ == "__main__":
    path = load_pickle(
        "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pickle_file/hs_path.pk"
    )
    mhs, _, _ = path.split("/")[-1].split("--")
    gpp_hs_moi(mhs)
