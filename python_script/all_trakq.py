import glob
import os
import sys
import time
import pyperclip

import pandas as pd
from god_class import (
    TelegramSend,
    show_message,
    upload_file_dialog,
    # wait_for_window_with_class,
)
from playwright_class import DichVuCong
from god_class import send_notification
from loguru import logger

os.chdir("/home/<USER>/Dropbox/hnd/csv_source")


def update_df_datrakq(data_frame):
    df_append = data_frame[["thu tuc", "ten nguoi ptcm", "han back_up"]]
    df_append["ma ho so"] = df_append.index
    df_datra = pd.read_csv("da_tra_kq.csv", dtype=str, index_col="stt")
    results = pd.concat([df_datra, df_append], ignore_index=True)
    results.reset_index(inplace=True, drop=True)
    results.index += 1
    results.to_csv("da_tra_kq.csv", index_label="stt")


def create_path(
    mhs,
):  # TODO tạo đường dẫn file chỉ cần có chứa ma ho so trong đó là được
    return " ".join(
        f'"{f}"'
        for f in glob.glob(
            "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pdf_documents/kq/*.pdf"
        )
        if mhs.lower() in f.lower()
    )


class TraKq(DichVuCong):
    DF_NAME = pd.read_csv("name.csv", dtype="str", index_col="ma ho so")

    def __init__(self, user):
        super().__init__(user)
        self.user = user
        self.df_thamdinhtt = None
        self.df_left = None
        self.page = None

    def update_df_name(self):
        list_mhs = self.df_left.index.tolist() + self.df_thamdinhtt.index.tolist()
        TraKq.DF_NAME.loc[list_mhs, "van de hs"] = "Đã trả"
        TraKq.DF_NAME.loc[list_mhs, "CHỜ TRẢ"] = "0"
        TraKq.DF_NAME.to_csv("name.csv", index_label="ma ho so")

    def upload_file_and_wait(self, path):
        self.page.locator("div#uploaderketqua button:has-text('Chọn tệp tin')").click()
        self.page.locator(
            "(//a[@class='selector-scanner'][contains(text(),'Chọn tệp tin')])[1]"
        ).click()
        upload_file_dialog(path)
        self.page.wait_for_selector("td.text-center:has-text('1')", timeout=180000)
        time.sleep(2)

    def remove_files(self):
        try:
            files_to_remove = [
                f
                for f in glob.glob("/home/<USER>/anh_cchnd/*.png")
                if f.split("/")[-1].split("--")[0] in self.df_left.index.tolist()
            ]
            all_files_to_remove = files_to_remove
            for f in all_files_to_remove:
                os.remove(f)
        except:
            pass

    def tra_kq(self, df):
        result = ""
        counter = 0
        for mhs, values in df.iterrows():
            counter += 1
            self.open_new_hs_page(mhs)
            path = mhs
            self.upload_file_and_wait(path)
            result += f"{counter}. {mhs} -- {values['thu tuc']}--{values['ten nguoi ptcm']}\nHẠN TRẢ KẾT QUẢ: {values['han back_up']}\n\n"
            self.close_window_and_switch()
        self.tick_chon_multil_hs(df.index.tolist())
        self.chuyen_buoc("tp")
        update_df_datrakq(df)
        return result

    def check_hs(self):
        mask_chotra = TraKq.DF_NAME["CHỜ TRẢ"] == "1"
        mask_trangthai = TraKq.DF_NAME["trang thai"] == "THẨM ĐỊNH THỰC TẾ"
        self.df_thamdinhtt = TraKq.DF_NAME[mask_chotra & mask_trangthai]

        self.df_left = TraKq.DF_NAME[mask_chotra & ~mask_trangthai]
        if self.df_left.empty and self.df_thamdinhtt.empty:
            show_message("THÔNG BÁO", "KHÔNG CÓ HỒ SƠ ĐỂ TRẢ")
            sys.exit()
        else:
            send_notification(f"chuẩn bị trả {sum(mask_chotra)} hồ sơ")

    @logger.catch
    def run(self):
        send_notification("đang chạy trakq")
        self.check_hs()
        super().setup()
        super().login_dvc_by_user(self.user)
        result1 = ""
        result2 = ""
        if not self.df_thamdinhtt.empty:
            self.go_to_trangthai("TDTT")
            self.expand_ds()
            send_notification(f"chuẩn bị trả {len(self.df_thamdinhtt)} hồ sơ tdtt")
            result1 = self.tra_kq(self.df_thamdinhtt)
            pyperclip.copy(result1)
        if not self.df_left.empty:
            self.go_to_trangthai("TD")
            self.expand_ds()
            send_notification(f"chuẩn bị trả {len(self.df_left)} hồ sơ")
            result2 = self.tra_kq(self.df_left)
            pyperclip.copy(result2)
        self.remove_files()
        self.update_df_name()
        tele = TelegramSend("hnd")
        tele.send_message_warp("LÊ TÙNG SƠN ĐÃ TRẢ KẾT QUẢ\n\n" + result1 + result2)
        pyperclip.copy("LÊ TÙNG SƠN ĐÃ TRẢ KẾT QUẢ\n\n" + result1 + result2)
        send_notification("xong")


dvc = TraKq("tungson")
dvc.run()
