import sys

import pandas as pd

file_name = sys.argv[1]
col_index = sys.argv[2]

# file_name="/home/<USER>/Dropbox/hnd/csv_source/gdp.csv"
# col_index="49"
def print_row_as_dict(file, index):
    df = pd.read_csv(file, dtype=str)
    df.reset_index(drop=True, inplace=True)
    df.index+=1
    row_dict = df.loc[int(index)-1].to_dict()
    for key, value in row_dict.items():
        print(f"{key}:", value)


print_row_as_dict(file_name, col_index)
# Tạo DataFrame ví dụ