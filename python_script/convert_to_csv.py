import re
import csv
import pandas as pd


def convert_snx_to_csv(input_file, output_file):
    """
    Chuyển đổi file sắp xếp xã thành file CSV với 2 cột: xa_cu và xa_moi

    Args:
        input_file (str): Đường dẫn file input (.md)
        output_file (str): Đường dẫn file output (.csv)
    """

    # Danh sách để lưu dữ liệu
    data = []

    # Đọc file
    with open(input_file, "r", encoding="utf-8") as f:
        content = f.read()

    # Tách thành các dòng
    lines = content.strip().split("\n")

    for line in lines:
        if line.strip() == "":
            continue

        # Pattern để extract thông tin
        # Tìm phần giữa "của" và "thành xã mới" hoặc "thành phường mới"
        pattern_old = r"của (.+?) thành (?:xã|phường) mới"
        # Tìm tên xã/phường mới sau "tên gọi là" (giữ nguyên tiền tố)
        pattern_new = r"tên gọi là ((?:xã|phường) .+?)\."

        old_match = re.search(pattern_old, line)
        new_match = re.search(pattern_new, line)

        if old_match and new_match:
            # Lấy danh sách các xã cũ
            old_areas_text = old_match.group(1)
            new_area = new_match.group(1)  # Giữ nguyên "xã" hoặc "phường"

            # Tách các xã cũ (phân tách bởi dấu phẩy và "và")
            # Trước tiên thay thế " và " bằng ", "
            old_areas_text = old_areas_text.replace(" và ", ", ")

            # Tách theo dấu phẩy
            old_areas = [area.strip() for area in old_areas_text.split(",")]

            # Làm sạch tên (loại bỏ tiền tố "thị trấn", "xã", "phường", "các xã", "các phường")
            cleaned_old_areas = []
            for area in old_areas:
                # Loại bỏ các tiền tố
                clean_area = re.sub(
                    r"^(thị trấn|xã|phường|các xã|các phường)\s+", "", area.strip()
                )
                if clean_area:
                    cleaned_old_areas.append(clean_area)

            # Thêm vào danh sách dữ liệu
            for old_area in cleaned_old_areas:
                data.append({"xa_cu": old_area, "xa_moi": new_area})

    # Tạo DataFrame và lưu thành CSV
    df = pd.DataFrame(data)
    df.to_csv(output_file, index=False, encoding="utf-8")

    print(f"Đã chuyển đổi thành công! File CSV được lưu tại: {output_file}")
    print(f"Tổng số dòng dữ liệu: {len(data)}")

    # Hiển thị vài dòng đầu để kiểm tra
    print("\nVài dòng đầu tiên:")
    print(df.head(10).to_string(index=False))

    return df


# Sử dụng hàm
if __name__ == "__main__":
    # Chuyển đổi file
    input_file = "/home/<USER>/Dropbox/hnd/Obsidian/HND/snx.md"
    output_file = "/home/<USER>/Dropbox/hnd/Obsidian/HND/sap_xep_xa.csv"

    df = convert_snx_to_csv(input_file, output_file)
