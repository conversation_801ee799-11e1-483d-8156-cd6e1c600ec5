import os
from datetime import datetime, timedelta
import sys

import pandas as pd

from god_class import (
    dataframe_to_latex,
    TextProcess,
    get_current_date,
    insert_stt,
    show_message,
)
from csv_load_and_export import CsvLoaderFactory

os.chdir("/home/<USER>/Dropbox/hnd/csv_source")
df_gpp = CsvLoaderFactory.create_datetime_loader(
    ["ngay het han gpp", "ngay qd", "ngay cap cchnd"]
).load_df("du_lieu_gpp_all")
df_chamdut = CsvLoaderFactory.create_datetime_loader(
    ["ngay qd", "ngay qd cham dut"]
).load_df("ds_da_cham_dut")
df_cchnd = CsvLoaderFactory.create_datetime_loader(
    ["ngay cchnd cu", "ngay qd"]
).load_df("cchnd")





class FilterDenHan:
    def __init__(self, df_gpp):
        self.df_gpp = df_gpp

    def filter(self):
        self.df_gpp = self.df_gpp.loc[self.df_gpp["ngay het han gpp"].notnull()]
        now = datetime.now()
        future_date = now + timedelta(days=60)
        # Lọc ra các dòng có 'ngay het han gpp' nằm trong khoảng từ ngày hiện tại đến 15 ngày tới
        mask1 = self.df_gpp["ngay het han gpp"] >= now
        mask2 = self.df_gpp["ngay het han gpp"] <= future_date

        return self.df_gpp.loc[mask1 & mask2]

tcv=DateTimeConverterFacade(df_thuhoicchnd,df_gpp,df_chamdut)
tcv.convert()

filtered_df = FilterDenHan(df_gpp).filter()

sys.exit()
# TODO check cham dut

df_chamdutroi = filtered_df.merge(
    df_chamdut[["so dkkd", "ngay qd"]], on=["so dkkd", "ngay qd"], how="inner"
)
if df_chamdutroi.shape[0] > 0:
    show_message("thong bao", "Có một số cơ sở đã chấm dứt hoạt động")
    df_chamdutroi.to_csv("df_chamdutroi.csv", index=False)

sys.exit()


df_thay_doi_cchnd = filtered_df.merge(
    df_thuhoicchnd[["so cchnd", "ngay cap cchnd"]],
    on=["so cchnd", "ngay cap cchnd"],
    how="inner",
)
if df_thay_doi_cchnd.shape[0] > 0:
    show_message("thong bao", "Có một số cơ sở đã thay đổi thông tin")
    df_thay_doi_cchnd.to_csv("df_thay_doi_cchnd.csv", index=False)


filtered_df = filtered_df.sort_values(by="ngay het han gpp")
filtered_df["ngay het han gpp"] = filtered_df["ngay het han gpp"].dt.strftime(
    "%d/%m/%Y"
)

insert_stt(filtered_df)
filtered_df["so cchnd"] = filtered_df["so cchnd"].apply(
    lambda x: x + "/CCHND-" + "\par SYT-VP" if "-" not in x else x
)
filtered_df["ten qt-nt"] = (
    filtered_df["ten qt-nt"].str.title().str.replace("Thuốc", "thuốc")
)
filtered_df["so gpp"] = filtered_df["so gpp"] + "/GPP"

filtered_df = filtered_df[
    [
        "stt",
        "ten qt-nt",
        "dia chi co so",
        "ten nguoi ptcm",
        "so cchnd",
        "so gpp",
        "ngay het han gpp",
    ]
]

values["ngay"], values["thang"], values["nam"], values["today"] = get_current_date()
values["so_cs"] = str(filtered_df.shape[0]).zfill(2)
values["clip"] = dataframe_to_latex(filtered_df)
text = TextProcess("gpp_thong_bao_den_han")
text.format_text(values)
text.compile_latex()
text.open_pdf()
tieude = "THÔNG BÁO Danh sách cơ sở bán lẻ thuốc đến hạn đánh giá định kỳ việc duy trì đáp ứng Thực hành tốt cơ sở bán lẻ thuốc"
text.auto_day_van_ban(tieude, "TB", "0")
