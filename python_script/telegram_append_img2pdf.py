import time
from PIL import Image
import os
import shutil
from PyPDF2 import PdfMerger
import pyautogui

from god_class import copy_file_to_clip, send_notification


def click_ui(x, y, class_name):
    original_position = pyautogui.position()
    pyautogui.click()
    time.sleep(0.5)
    pyautogui.leftClick(x, y)
    pyautogui.press("escape")
    pyautogui.moveTo(original_position)
    switch_to_class(class_name)


def switch_to_class(class_name):
    """Chuyển đổi sang ứng dụng Telegram sử dụng wmctrl"""
    import subprocess

    try:
        # Sử dụng wmctrl để chuyển đến cửa sổ Telegram
        subprocess.run(["wmctrl", "-xa", class_name], check=True)
        # Đợi một chút để ứng dụng hiển thị
        time.sleep(0.5)
        return True
    except subprocess.CalledProcessError as e:
        print(f"Lỗi khi chuyển đến {class_name}: {e}")
        return False
    except FileNotFoundError:
        print(
            "Lỗi: <PERSON>h<PERSON>ng tìm thấy lệnh wmctrl. H<PERSON>y cài đặt nó bằng 'sudo apt install wmctrl'"
        )
        return False


def save_clipboard_to_pdf(path_source):
    pdf_path = "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pdf_documents/file_hs_online/file.pdf"
    temp_pdf = "/home/<USER>/Pcloud_ssd/Pcloud/anh_hs/temp.pdf"

    files = [
        os.path.join(path_source, f)
        for f in os.listdir(path_source)
        if os.path.isfile(os.path.join(path_source, f))
    ]
    if not files:
        return False

    # Sắp xếp theo thời gian sửa đổi, lấy file mới nhất
    latest_file = max(files, key=os.path.getmtime)

    # Mở file ảnh mới nhất
    try:
        img = Image.open(latest_file)
    except Exception as e:
        print(f"Lỗi khi mở file: {e}")
        return False

    # Chuyển sang RGB nếu cần
    if img.mode != "RGB":
        img = img.convert("RGB")

    # Lưu ảnh clipboard thành PDF tạm thời
    img.save(temp_pdf, "PDF")

    # Kiểm tra file PDF đích đã tồn tại chưa
    if not os.path.exists(pdf_path):
        # Thay os.rename bằng shutil.move
        shutil.move(temp_pdf, pdf_path)
        send_notification("Đã tạo file pdf mới file_hs.pdf")
    else:
        # Nếu đã có, merge PDF tạm với PDF đích
        merger = PdfMerger()
        merger.append(pdf_path)
        merger.append(temp_pdf)
        merger.write(pdf_path)
        merger.close()
        # Xóa file tạm
        os.remove(temp_pdf)

        send_notification("Thêm hình ảnh vào PDF thành công")
    # Mở file PDF bằng Okular
    return True


if __name__ == "__main__":
    file_path = "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pdf_documents/file_hs_online/file.pdf"
    #
    # Lưu vị trí ban đầu của chuột
    click_ui(1770, 1047, "telegram-desktop")
    save_clipboard_to_pdf("/home/<USER>/Downloads/AyuGram Desktop")
    copy_file_to_clip(file_path)
    send_notification("Đã copy file_hs.pdf vào clipboard")
