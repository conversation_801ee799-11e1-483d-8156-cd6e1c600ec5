import pandas as pd
from god_class import TelegramSend, send_notification, send_error_to_telegram
from playwright_class import DichVuCong
from loguru import logger
import os

os.chdir("/home/<USER>/Dropbox/hnd/csv_source")


class PhanHoSo(DichVuCong):
    DICT_THUTUC_CHAM = {
        "2.000984.000.00.00.H62": "Cấp giấy phép hoạt động đối với cơ sở dịch vụ y tế thuộc thẩm quyền của Sở Y tế",
        "1.003628.000.00.00.H62": "Cấp giấy phép hoạt động đối với cơ sở khám bệnh, chữa bệnh khi thay đổi tên cơ sở khám bệnh, chữa bệnh thuộc thẩm quyền của Sở Y tế",
        "1.003531.000.00.00.H62": "Cấp giấy phép hoạt động đối với cơ sở khám bệnh, chữa bệnh khi thay đổi người chịu trách nhiệm chuyên môn của cơ sở khám bệnh, chữa bệnh thuộc thẩm quyền của Sở Y tế",
        "1.003516.000.00.00.H62": "Cấp lại giấy phép hoạt động đối với cơ sở khám bệnh, chữa bệnh thuộc thẩm quyền của Sở Y tế do bị mất, hoặc hư hỏng hoặc bị thu hồi do cấp không đúng thẩm quyền",
        "1.002205.000.00.00.H62": "Cấp giấy phép hoạt động khám bệnh, chữa bệnh nhân đạo đối với Phòng chẩn trị y học cổ truyền thuộc thẩm quyền của Sở Y tế",
        "1.002140.000.00.00.H62": "Cấp giấy phép hoạt độngkhám bệnh, chữa bệnh nhân đạo đối với cơ sở dịch vụ tiêm (chích), thay băng, đếm mạch, đo nhiệt độ, đo huyết áp",
        "1.002111.000.00.00.H62": "Cấp giấy phép hoạt động khám bệnh, chữa bệnh nhân đạo đối với cơ sở dịch vụ chăm sóc sức khoẻ tại nhà",
        "1.002015.000.00.00.H62": "Cấp giấy phép hoạt động khám bệnh, chữa bệnh nhân đạo đối với cơ sở khám bệnh, chữa bệnh thuộc thẩm quyền của Sở Y tế khi thay đổi tên cơ sở khám chữa bệnh",
        "1.002000.000.00.00.H62": "Cấp lại giấy phép hoạt động khám bệnh, chữa bệnh nhân đạo đối với cơ sở khám bệnh, chữa bệnh thuộc thẩm quyền của Sở Y tế do bị mất hoặc hư hỏng hoặc giấy phép bị thu hồi do cấp không đúng thẩm quyền",
        "1.008069.000.00.00.H62": "Cấp giấy phép hoạt động đối với Phòng khám, điều trị bệnh nghề nghiệp thuộc thẩm quyền của Sở Y tế",
        "1.003876.000.00.00.H62": "Cấp giấy phép hoạt động đối với Phòng khám đa khoa thuộc thẩm quyền của Sở Y tế",
        "1.003848.000.00.00.H62": "Cấp giấy phép hoạt động đối với bệnh viện thuộc Sở Y tế và áp dụng đối với trường hợp khi thay đổi hình thức tổ chức, chia tách, hợp nhất, sáp nhập",
        "1.003803.000.00.00.H62": "Cấp giấy phép hoạt động đối với Phòng khám chuyên khoa thuộc thẩm quyền của Sở Y tế",
        "1.003774.000.00.00.H62": "Cấp giấy phép hoạt động đối với nhà hộ sinh thuộc thẩm quyền của Sở Y tế",
        "1.003746.000.00.00.H62": "Cấp giấy phép hoạt động đối với trạm xá, trạm y tế xã",
        "1.003547.000.00.00.H62": "Điều chỉnh giấy phép hoạt động đối với cơ sở khám bệnh, chữa bệnh khi thay đổi quy mô giường bệnh hoặc cơ cấu tổ chức hoặc phạm vi hoạt động chuyên môn thuộc thẩm quyền của Sở Y tế",
        "1.002230.000.00.00.H62": "Cấp giấy phép hoạt động khám bệnh, chữa bệnh nhân đạo đối với Phòng khám đa khoa thuộc thẩm quyền của Sở Y tế",
        "1.002215.000.00.00.H62": "Cấp giấy phép hoạt động khám bệnh, chữa bệnh nhân đạo đối với Phòng khám chuyên khoa thuộc thẩm quyền của Sở Y tế",
        "1.002191.000.00.00.H62": "Cấp giấy phép hoạt động khám bệnh, chữa bệnh nhân đạo đối với Nhà Hộ Sinh thuộc thẩm quyền của Sở Y tế",
        "1.002182.000.00.00.H62": "Cấp giấy phép hoạt động khám bệnh, chữa bệnh nhân đạo đối với phòng khám chẩn đoán hình ảnh thuộc thẩm quyền của Sở Y tế",
        "1.002162.000.00.00.H62": "Cấp giấy phép hoạt động khám bệnh, chữa bệnh nhân đạo đối với phòng xét nghiệm thuộc thẩm quyền của Sở Y tế",
        "1.002073.000.00.00.H62": "Cấp giấy phép hoạt động khám bệnh, chữa bệnh nhân đạo đối với cơ sở dịch vụ cấp cứu, hỗ trợ vận chuyển người bệnh",
        "1.002037.000.00.00.H62": "Cấp giấy phép hoạt động khám bệnh, chữa bệnh nhân đạo đối với cơ sở khám bệnh, chữa bệnh thuộc thẩm quyền của Sở Y tế khi thay đổi địa điểm",
        "1.001987.000.00.00.H62": "Điều chỉnh giấy phép hoạt động khám bệnh, chữa bệnh nhân đạo đối với cơ sở khám bệnh, chữa bệnh truc thuoc Sở Y tế khi thay đổi quy mô giường bệnh hoặc cơ cấu tổ chức hoặc phạm vi hoạt động chuyên môn",
        "1.001907.000.00.00.H62": "Cấp giấy phép hoạt động khám, chữa bệnh nhân đạo đối với bệnh viện trên địa bàn quản lý của Sở Y tế (trừ các bệnh viện thuộc thẩm quyền của Bộ trưởng Bộ Y tế và Bộ Quốc phòng) và áp dụng đối với trường hợp khi thay đổi hình thức tổ chức, chia tách, hợp nhất, sáp nhập",
        "1.002600.000.00.00.H62": "CAP SO TNMP SAN XUAT TRONG NUOC",
    }
    DICT_THUTUC_DA = {
        "1.012289.000.00.00.H62": "Cấp mới giấy phép hành nghề trong giai đoạn chuyển tiếp đối với hồ sơ nộp từ ngày 01 tháng 01 năm 2024 đến thời điểm kiểm tra đánh giá năng lực hành nghề đối với các chức danh bác sỹ, y sỹ, điều dưỡng, hộ sinh, kỹ thuật y, dinh dưỡng lâm sàng, cấp cứu viên ngoại viện, tâm lý lâm sàng",
        "1.003709.000.00.00.H62": "CAP CCHN KBCB",
    }

    def __init__(self, user):
        super().__init__(user)
        self.user = user
        self.tele = TelegramSend("tbhs")

    def get_ho_so_info(self, element, dict_thutuc):
        mhs = self.get_attribute("value", element)
        thutuc = dict_thutuc[self.get_attribute("data-tentatthutuc", element)]
        nguoinop = self.get_attribute("data-tennguoinop", element)
        return f"--{mhs}--{thutuc}--{nguoinop}"

    def wait_for_pc_xl_hs_link(self):
        try:
            self.page.wait_for_selector(
                'a[href="/vinhphuc/hoso/&mlcv=PC_XL_HS"]', timeout=5000
            )
        except Exception as e:
            return False
        return True

    def send_current_report(self, reports):
        current_report = "".join(reports.values())
        self.tele.send_message_warp(current_report)

    def phan_cong_nv(self):
        df_thutuc_duoc = pd.read_csv(
            "thu_tuc_duoc.csv", dtype="str", index_col="ma thu tuc"
        )
        dict_duoc = df_thutuc_duoc.to_dict()["TÊN TẮT"]
        df_thutuc_kcb = pd.read_csv(
            "thu_tuc_kcb.csv", dtype="str", index_col="ma thu tuc"
        )
        dict_kcb = df_thutuc_kcb.to_dict()["TÊN thu tuc"]
        reports = {"SON": "", "DA": "", "TP": "", "CHAM": ""}

        def process_elements(category, condition, action):
            if not self.wait_for_pc_xl_hs_link():
                return
            nonlocal reports
            count = 0
            elements = self.get_row_elements()
            # if category= ='TP':
            #     send_notification('debug hồ sơ TP now', True)
            #     self.send_current_report(reports)
            #     sys.exit()

            for element in elements:
                if condition(element):
                    count += 1
                    # element.locator("ins.iCheck-helper").click()
                    reports[category] += f"{count}{action(element)}\n"
            if count != 0:
                reports[category] = f"{category}:\n\n{reports[category]}\n\n"
                # self.chuyen_buoc(category.lower())
                # self.wait_for_load_done()

        process_elements(
            "CHAM",
            lambda e: self.get_attribute("data-tentatthutuc", e)
            in self.DICT_THUTUC_CHAM.keys(),
            lambda e: f"--{self.get_attribute('value', e)}--{self.DICT_THUTUC_CHAM[self.get_attribute('data-tentatthutuc', e)]}--{self.get_attribute('data-tennguoinop', e)}",
        )
        process_elements(
            "SON",
            lambda e: self.get_attribute("data-tentatthutuc", e)
            in df_thutuc_duoc.index.tolist(),
            lambda e: self.get_ho_so_info(e, dict_duoc),
        )

        process_elements(
            "DA",
            lambda e: self.get_attribute("data-tentatthutuc", e)
            in self.DICT_THUTUC_DA.keys(),
            lambda e: f"--{self.get_attribute('value', e)}--{self.DICT_THUTUC_DA[self.get_attribute('data-tentatthutuc', e)]}--{self.get_attribute('data-tennguoinop', e)}",
        )

        process_elements(
            "TP",
            lambda e: (
                self.get_attribute("data-tentatthutuc", e)
                in df_thutuc_kcb.index.tolist()
                and self.get_attribute("data-tentatthutuc", e)
                not in self.DICT_THUTUC_CHAM.keys()
                and self.get_attribute("data-tentatthutuc", e)
                not in self.DICT_THUTUC_DA.keys()
            ),
            lambda e: self.get_ho_so_info(e, dict_kcb),
        )
        self.send_current_report(reports)

    @logger.catch
    @send_error_to_telegram
    def run(self):
        super().setup(headless=False)
        super().login_dvc_by_user(self.user)
        self.go_to_trangthai("PC_XL_HS")
        self.phan_cong_nv()
        send_notification("Đã phân hồ sơ xong", True)


dvc = PhanHoSo("tp")
dvc.run()
