import os
import pickle
from typing import Any

from create_snippets import creat_and_get


os.chdir("/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pickle_file")


def load_pickle(file: str) -> Any:
    try:
        with open(file, "rb") as f:
            return pickle.load(f)
    except FileNotFoundError:
        return None


# %%
def save_last_dict(values, file):
    with open(file, "wb") as f:
        pickle.dump(values, f)

    # Chạy script để mở file .tex mới nhất


# save_last_dict('16',"/home/<USER>/Dropbox/hnd/latexall\soqd_ntbv.pk")
# print(load_last_dict("/home/<USER>/Dropbox/hnd/latexall/soqd_gpp.pk"))
# print(load_last_dict("/home/<USER>/Dropbox/hnd/latexall/soqd_cc.pk"))
# save_last_dict("1480","/home/<USER>/Dropbox/hnd/latexall/soqd_cc.pk")
