import os

import pandas as pd
import unidecode
from loguru import logger

from all_pickle import load_pickle
from god_class import (
    TextProcess,
    get_dict_from_index_df,
    send_notification,
    update_df_from_dict_by_index,
    update_df_name_da_<PERSON>han,
    get_current_date,
)
from top_most_get_text import input_dialog
from create_snippets import creat_and_get
from solid_string_format import DictStringFormatter

os.chdir("/home/<USER>/Dropbox/hnd/csv_source")


@logger.catch
def gdp_new(mhs):
    df_gdp = pd.read_csv("gdp.csv", dtype="str", index_col="ma ho so")
    send_notification("Lưu ý bấm 3 trong okular")
    df_dshn = pd.read_csv("dshn_duoc.csv", dtype="str", index_col="so cchnd")
    df_name = pd.read_csv("name.csv", dtype="str", index_col="ma ho so")

    so_cchnd = input_dialog("NHẬP", "NHẬP so cchnd", "")
    old_dict = get_dict_from_index_df(df_dshn, so_cchnd)  # lấy dữ liệu t

    list_upper = ["ten nguoi ptcm", "GIÁM ĐỐC"]
    list_ngay = ["ngay cap cchnd", "ngay sinh", "ngay cap cmnd"]
    list_phone = ["so dt chu hs"]
    list_include = [
        col
        for col in df_gdp.columns.tolist()
        if col
        not in [
            "ma ho so",
            "ngay dkkd cu",
            "ngay het han gdp",
            "so qd",
            "ngay qd",
            "so gdp",
            "loai cap",
            "ly do",
            "so dt chu hs",
            "so dkkd",
            "so cchnd",
        ]
    ]
    values = dict.fromkeys(list_include, "")
    values["pham vi kd-1 thuoc thuong-0 bao quan lh"] = ""
    values["loai hinh-0 dia diem-1 cong ty-2 chi nhanh"] = ""
    values["noi cap cchnd"] = "Sở Y tế tỉnh Phú Thọ"
    for key, _ in values.items():
        if key in old_dict and old_dict[key]:
            values[key] = old_dict[key]
    val = creat_and_get(values)
    fm = DictStringFormatter(val)
    fm.apply_upper(list_upper)
    fm.apply_date_format(list_ngay)
    fm.apply_phone_format(list_phone)
    val = fm.get_result()

    val["so cchnd"] = so_cchnd
    val["trinh do cm"] = "Đại học dược"
    val["ma ho so"] = mhs
    val["loai cap"] = "Cấp lần đầu GDP và GCN ĐĐKKDD"
    base_pvkd = "Bán buôn thuốc thành phẩm (bao gồm cả thuốc dạng phối hợp có chứa dược chất gây nghiện; thuốc dạng phối hợp có chứa dược chất hướng thần; thuốc dạng phối hợp có chứa tiền chất; thuốc độc; thuốc trong danh mục thuốc, dược chất thuộc danh mục chất bị cấm sử dụng trong một số ngành, lĩnh vực), trừ vắc xin; bảo quản ở điều kiện thường"
    if val["pham vi kd"] == "thuốc thường":
        val["pham vi kd"] = base_pvkd
    elif val["pham vi kd"] == "bảo quản lạnh":
        val["pham vi kd"] = (
            r"Bán buôn thuốc thành phẩm (bao gồm cả thuốc dạng phối hợp có chứa dược chất gây nghiện; thuốc dạng phối hợp có chứa dược chất hướng thần; thuốc dạng phối hợp có chứa tiền chất; thuốc độc; thuốc trong danh mục thuốc, dược chất thuộc danh mục chất bị cấm sử dụng trong một số ngành, lĩnh vực), trừ vắc xin; bảo quản ở điều kiện thường, bảo quản lạnh."
        )
    val["dia chi co so"] = val["KHO 1"] if val["KHO 1"] else val["tru so"]
    val["ten qt-nt"] = val["ten cong ty"]
    val["trinh do tat"] = "đại học"
    update_df_from_dict_by_index(df_dshn, "dshn_duoc.csv", val, so_cchnd)
    update_df_name_da_nhan(df_name, val, mhs)
    update_df_from_dict_by_index(df_gdp, "gdp.csv", val, mhs)
    file_text_gdp = TextProcess("bbtd_gdp_new")
    val["ngay"], val["thang"], val["nam"], _ = get_current_date()
    file_text_gdp.format_text(val)
    file_text_gdp.compile_latex()
    tenct = unidecode.unidecode(val["ten cong ty"]).replace(" ", "_")
    file_text_gdp.copy_latex_file(
        f"/home/<USER>/Dropbox/hnd/latexall/bbtd_cong_ty/bbtd_gdp/{val['nam']}/{tenct}.tex"
    )
    file_text_gdp.copy_pdf_to_dropbox()


if __name__ == "__main__":
    path = load_pickle(
        "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pickle_file/hs_path.pk"
    )
    mhs, _, _ = path.split("/")[-1].split("--")
    gdp_new(mhs)
