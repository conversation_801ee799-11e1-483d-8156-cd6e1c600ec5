import os
import shutil

def copy_xlsx_files(src_dir, dest_dir):
    # Walk through the source directory
    for root, dirs, files in os.walk(src_dir):
        for file in files:
            if file.endswith('.xlsx'):
                # Construct full file path
                file_path = os.path.join(root, file)
                # Copy the file to the destination directory
                shutil.copy(file_path, dest_dir)
                print(f"Copied: {file_path} to {dest_dir}")

# Define your source and destination directories
src_dir = os.path.expanduser("~/Dropbox/hnd/latexall/documents/chu_hao/CÔNG VIỆC HÀNG NGÀY/QĐ_CCHN_.2021")
dest_dir = os.path.expanduser("~/Dropbox/hnd/latexall/documents/son_tong_hop_2020_2021/qd_cchnd_2021")

# Ensure the destination directory exists
os.makedirs(dest_dir, exist_ok=True)

# Call the function to copy .xlsx files
copy_xlsx_files(src_dir, dest_dir)