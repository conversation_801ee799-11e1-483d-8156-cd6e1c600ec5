from PyQt5.QtWidgets import QApplication
from src.gui.main_window import MainWindow
from src.models.template_manager import TemplateManager


def main(values):
    app = QApplication([])
    # Khởi tạo TemplateManager với thu_tuc và values
    template_manager = TemplateManager(values)
    window = MainWindow(template_manager, values)
    window.show()

    return app.exec_()


if __name__ == "__main__":
    from all_pickle import load_pickle

    values = load_pickle("values_vdhs.pk")  # Load values từ file pickle
    main(values)
