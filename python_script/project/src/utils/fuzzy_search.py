import os
from fuzzywuzzy import fuzz, process
from typing import List, <PERSON><PERSON>


def fuzzy_search(query: str, choices: list) -> list:
    """
    Thực hiện tìm kiếm fuzzy với các từ không theo thứ tự
    Returns: List of tuples (choice, score)
    """
    results = []
    # Tách query thành list các từ và chuyển về lowercase
    query_words = set(query.lower().split())

    for choice in choices:
        choice_lower = choice.lower()
        # Kiểm tra xem tất cả các từ trong query có nằm trong choice không
        if all(word in choice_lower for word in query_words):
            results.append((choice, 1))

    return sorted(results, key=lambda x: x[1], reverse=True)
