import pandas as pd
from typing import List

import unidecode


class TemplateManager:
    def __init__(self, values: dict = None):
        self.values = values
        self.thu_tuc = values["thu tuc"]
        # Mapping cho các file CSV
        self.csv_mapping = {
            "ycbs": "/home/<USER>/Dropbox/hnd/csv_source/tb_bs_hs.csv",
            "tkd": "/home/<USER>/Dropbox/hnd/csv_source/tb_khong_dat.csv",
        }
        # Lấy loại văn bản từ values, mặc định là "ycbs"
        loai_vb = values.get("loai vb", "ycbs") if values else "ycbs"
        self.csv_path = self.csv_mapping.get(loai_vb, self.csv_mapping["ycbs"])
        self.df = self._load_csv()

    def _load_csv(self) -> pd.DataFrame:
        """Load CSV file và lọc theo thủ tục"""
        try:
            df = pd.read_csv(self.csv_path, dtype=str)
            return df[df["thu tuc"] == self.thu_tuc]
        except Exception as e:
            print(f"Lỗi khi đọc file CSV: {e}")
            return pd.DataFrame()

    def get_viet_tat_list(self) -> List[str]:
        """Trả về danh sách các viết tắt"""
        return self.df["viet tat"].unique().tolist()

    def get_ly_do(self, viet_tat: str) -> str:
        """Lấy lý do theo viết tắt"""
        try:
            return self.df[self.df["viet tat"] == viet_tat]["ly do"].iloc[0]
        except:
            return ""

    def save_ly_do(self, ly_do: str) -> bool:
        """Lưu lý do mới vào CSV"""
        df = pd.read_csv(self.csv_path, dtype=str, index_col="ma ho so")
        df.at[self.values["ma ho so"], "ly do"] = ly_do
        df.at[self.values["ma ho so"], "viet tat"] = ly_do
        df.at[self.values["ma ho so"], "thu tuc"] = unidecode.unidecode(
            self.thu_tuc
        ).replace(" ", "_")
        df.to_csv(self.csv_path, index_label="ma ho so")
