from datetime import datetime
import os
import glob
import subprocess
import sys
import pandas as pd
import pyperclip

project_parent = os.path.dirname(
    os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
)
sys.path.append(project_parent)
from all_pickle import save_last_dict
from god_class import (
    TextProcess,
    convert_unix_style,
    send_notification,
    show_message,
    get_current_date,
)


project_parent = os.path.dirname(
    os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
)
sys.path.append(project_parent)


class LatexFileManager:
    def __init__(self, template_manager, values):
        self.template_manager = template_manager
        self.base_path = "/home/<USER>/Dropbox/hnd/latexall/"
        self.values = values
        # Thêm mapping cho các loại văn bản
        self.folder_mapping = {
            "ycbs": {"folder": "tb_bo_sung", "template": "template_tb_bs"},
            "tkd": {"folder": "tb_khong_dat", "template": "template_tb_kd"},
        }
        self.pattern_map = {
            "cap_gcn_du_dieu_kien_sx_mp": "cấp Giấy chứng nhận đủ điều kiện sản xuất mỹ phẩm",
            "cap_gpp_va_dkkd": "cấp Giấy chứng nhận đủ điều kiện kinh doanh dược",
            "cap_cchnd": "xét cấp Chứng chỉ hành nghề dược",
            "dieu_chinh_nd_cchnd": "điều chỉnh nội dung Chứng chỉ hành nghề dược theo hình thức xét hồ sơ",
            "cap_lan_dau_gcn_ddkdd_va_gdp_cong_ty": "cấp Giấy chứng nhận đủ điều kiện kinh doanh dược",
            "danh_gia_duy_tri_dap_ung_gdp": "đánh giá duy trì đáp ứng Thực hành tốt phân phối thuốc",
            "cap_lai_cchnd_do_lam_mat": "cấp lại Chứng chỉ hành nghề dược theo hình thức xét hồ sơ",
            "dieu_chinh_dkkd": "điều chỉnh Giấy chứng nhận đủ điều kiện kinh doanh dược",
            "cap_thay_doi_dkkd": "cấp Giấy chứng nhận đủ điều kiện kinh doanh dược do thay đổi địa điểm kinh doanh",
            "xac_nhan_qc_mp": "cấp Giấy xác nhận nội dung quảng cáo mỹ phẩm",
        }

    def get_file_paths(self, viet_tat, viet_tat_moi=None):
        """Tạo các đường dẫn file cần thiết"""
        thu_tuc = convert_unix_style(self.template_manager.thu_tuc)

        # Sử dụng viet_tat_moi nếu có, ngược lại sử dụng viet_tat
        base_filename = f"{thu_tuc}_{convert_unix_style(viet_tat_moi) if viet_tat_moi else convert_unix_style(viet_tat)}"

        # Tạo timestamp và lưu vào DataFrame
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        van_de_string = (
            f"0-{timestamp}" if self.values["loai vb"] == "tkd" else timestamp
        )

        df_name = pd.read_csv("name.csv", dtype=str, index_col="ma ho so")
        df_name.loc[self.values["ma ho so"], "van de hs"] = van_de_string
        df_name.to_csv("name.csv")
        df_cchnd = pd.read_csv(
            "/home/<USER>/Dropbox/hnd/csv_source/cchnd.csv",
            dtype=str,
            index_col="ma ho so",
        )
        if self.values["ma ho so"] in df_cchnd.index:
            df_cchnd.at[self.values["ma ho so"], "van de hs"] = van_de_string
            df_cchnd.to_csv(
                "/home/<USER>/Dropbox/hnd/csv_source/cchnd.csv", index_label="ma ho so"
            )
        else:
            df_dkkd = pd.read_csv(
                "/home/<USER>/Dropbox/hnd/csv_source/dkkd.csv",
                dtype=str,
                index_col="ma ho so",
            )
            if self.values["ma ho so"] in df_dkkd.index:
                df_dkkd.at[self.values["ma ho so"], "van de hs"] = van_de_string
                df_dkkd.to_csv(
                    "/home/<USER>/Dropbox/hnd/csv_source/dkkd.csv",
                    index_label="ma ho so",
                )

        # Lấy thông tin folder dựa trên loại văn bản
        loai_vb = self.values.get("loai vb", "ycbs")
        folder_info = self.folder_mapping.get(loai_vb, self.folder_mapping["ycbs"])

        # Thêm pattern cho cả trường hợp viet_tat_moi và viet_tat
        if viet_tat_moi:
            file_pattern = [
                f"{self.base_path}{folder_info['folder']}/{thu_tuc}_{convert_unix_style(viet_tat)}_*.tex",
            ]
        else:
            file_pattern = [
                f"{self.base_path}{folder_info['folder']}/{base_filename}_*.tex"
            ]
        # Tìm tất cả các file phiên bản
        existing_versions = []
        for pattern in file_pattern:
            existing_versions.extend(glob.glob(pattern))
        if not existing_versions:
            send_notification("Chua co pattern mau")
        # Tìm file gần nhất nếu có
        latest_version = None
        if existing_versions:
            latest_version = max(existing_versions)

        # Tạo tên file mới với timestamp đã tạo ở trên
        new_filename = f"{base_filename}_{van_de_string}.tex"

        return {
            "template": f"{folder_info['template']}/{thu_tuc}.tex",
            "source": latest_version if latest_version else None,
            "new_file": f"{self.base_path}/{folder_info['folder']}/{new_filename}",
        }

    def extract_content(self, file_path):
        """Trích xuất nội dung giữa %begin và %end"""
        with open(file_path, "r", encoding="utf-8") as file:
            content = file.read()
        # Tìm vị trí %begin
        start_index = content.find("%begin")
        if start_index == -1:
            return ""

        # Tìm vị trí xuống dòng sau %begin
        start_index = content.find("\n", start_index) + 1

        # Tìm vị trí %end
        end_index = content.find("%end")
        if end_index == -1:
            return None

        # Trích xuất nội dung giữa %begin và %end
        extracted_text = content[start_index:end_index].strip()

        return extracted_text

    def prepare_and_open_file(self, viet_tat, viet_tat_moi=None):
        """Quy trình hoàn chỉnh để chuẩn bị và mở file"""
        paths = self.get_file_paths(viet_tat, viet_tat_moi)
        self.values["ngay"], self.values["thang"], self.values["nam"], _ = (
            get_current_date()
        )

        if not os.path.exists(self.base_path + "/source_latex/" + paths["template"]):
            show_message("thong bao", "chua co file nguon")
            raise FileNotFoundError(f"Không tìm thấy file template {paths['template']}")

        text = TextProcess(paths["template"].replace(".tex", ""))
        if paths["source"]:
            self.values["extracted"] = self.extract_content(paths["source"])
        else:
            self.values["extracted"] = ""
        text.format_text(self.values)
        text.copy_latex_file(paths["new_file"])
        self.get_dt_and_thu_tuc()

        if self.values["loai vb"] == "tkd":
            tieude = f"THÔNG BÁO KẾT QUẢ Thẩm định hồ sơ đề nghị {self.values['thu_tuc_full']}: {self.values['doi_tuong_trong_tieu_de']}"
        else:
            tieude = f"THÔNG BÁO Yêu cầu bổ sung hồ sơ đề nghị {self.values['thu_tuc_full']}: {self.values['doi_tuong_trong_tieu_de']}"
        # Copy tiêu đề vào clipboard để sử dụng sau này
        pyperclip.copy(tieude)  # noqa: F821
        save_last_dict(
            [tieude, paths["new_file"], "TB"],
            "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pickle_file/tieude.pk",
        )
        command = f"nvim '{paths['new_file']}'"
        subprocess.run("wmctrl -xa terminator.Terminator", shell=True)
        subprocess.run(["tmux", "new-window", "-n", "latex-edit", command])

    def get_dt_and_thu_tuc(self):
        try:
            self.values["thu_tuc_full"] = self.pattern_map[self.values["thu tuc"]]
        except KeyError:
            show_message(
                "thong bao",
                f"thu tuc {self.values['thu tuc']} khong co trong pattern map, mo file latex_file_manager",
            )
            raise KeyError("thu tuc khong co trong pattern map")

        self.values["doi_tuong_trong_tieu_de"] = self.values["ten nguoi ptcm"]
