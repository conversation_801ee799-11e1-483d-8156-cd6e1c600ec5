from PyQt5.QtWidgets import (
    QMainW<PERSON>ow,
    QWidget,
    QVBoxLayout,
    QHBoxLayout,
    QLineEdit,
    QPushButton,
    QLabel,
    QListWidget,
    QSplitter,
    QMessageBox,
)
from PyQt5.QtCore import Qt, pyqtSlot
from PyQt5.QtGui import QKeyEvent
import sys
import os

from src.utils.fuzzy_search import fuzzy_search
from src.gui.latex_file_manager import LatexFileManager

# Lấy đường dẫn tuyệt đối đến thư mục chứa project
project_parent = os.path.dirname(
    os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
)
sys.path.append(project_parent)


class VimStyleListWidget(QListWidget):
    def __init__(self, main_window, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.main_window = main_window

    def keyPressEvent(self, event: QKeyEvent):
        """Override keyPressEvent để xử lý phím j, k và space"""
        key = event.key()

        if key == Qt.Key_J:  # Phím j - di chuyển xuống
            current_row = self.currentRow()
            if current_row < self.count() - 1:
                self.setCurrentRow(current_row + 1)
        elif key == Qt.Key_K:  # Phím k - di chuyển lên
            current_row = self.currentRow()
            if current_row > 0:
                self.setCurrentRow(current_row - 1)
        elif key == Qt.Key_Return:  # Phím enter - chọ focus vào input
            self.main_window.ly_do_input.setFocus()
        else:
            super().keyPressEvent(event)


class MainWindow(QMainWindow):
    def __init__(self, template_manager, values):
        super().__init__()
        self.template_manager = template_manager
        self.latex_manager = LatexFileManager(template_manager, values)
        self.values = values
        self.setup_ui()
        self.setWindowTitle("Title")
        # Thêm event filter cho input
        self.ly_do_input.installEventFilter(self)

    def setup_ui(self):
        """Setup the UI components"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        self.setGeometry(100, 100, 800, 600)  # Giảm kích thước cửa sổ

        # Main layout
        main_layout = QVBoxLayout(central_widget)

        # Search section
        search_layout = QHBoxLayout()
        search_layout.addWidget(QLabel("Tìm kiếm:"))
        self.search_box = QLineEdit()
        self.search_box.setPlaceholderText("Tìm kiếm viết tắt...")
        self.search_box.textChanged.connect(self.on_search)
        self.search_box.returnPressed.connect(self.focus_list)
        search_layout.addWidget(self.search_box)
        main_layout.addLayout(search_layout)

        # Split view
        splitter = QSplitter(Qt.Horizontal)
        splitter.setChildrenCollapsible(False)  # Không cho phép thu gọn hoàn toàn

        # Left side: List of viet tat
        list_widget = QWidget()
        list_layout = QVBoxLayout(list_widget)
        list_layout.addWidget(QLabel("Danh sách viết tắt:"))
        self.viet_tat_list = VimStyleListWidget(self)
        self.viet_tat_list.addItems(self.template_manager.get_viet_tat_list())
        list_layout.addWidget(self.viet_tat_list)
        splitter.addWidget(list_widget)

        # Right side: Editor
        editor_widget = QWidget()
        editor_layout = QVBoxLayout(editor_widget)

        # Tạo layout ngang cho label và input
        ly_do_layout = QHBoxLayout()
        ly_do_layout.addWidget(QLabel("Lý do mới:"))
        self.ly_do_input = QLineEdit()
        self.ly_do_input.returnPressed.connect(self.open_latex_file)
        ly_do_layout.addWidget(self.ly_do_input)

        # Thêm layout ngang vào editor layout
        editor_layout.addLayout(ly_do_layout)

        splitter.addWidget(editor_widget)
        main_layout.addWidget(splitter)

        # Set tỷ lệ mặc định cho splitter (1/2 màn hình)
        splitter.setSizes([400, 400])

        # Buttons
        button_layout = QHBoxLayout()
        save_btn = QPushButton("Lưu")
        save_btn.clicked.connect(self.on_save)
        button_layout.addWidget(save_btn)

        clear_btn = QPushButton("Xóa trắng")
        clear_btn.clicked.connect(self.clear_form)
        button_layout.addWidget(clear_btn)

        main_layout.addLayout(button_layout)

    @pyqtSlot(str)
    def on_search(self, text: str):
        """Xử lý tìm kiếm"""
        if not text:
            # Nếu không có text tìm kiếm, hiển thị lại toàn bộ danh sách
            self.viet_tat_list.clear()
            self.viet_tat_list.addItems(self.template_manager.get_viet_tat_list())
            return

        # Tìm kiếm fuzzy trong danh sách viết tắt
        choices = self.template_manager.get_viet_tat_list()
        results = fuzzy_search(text, choices)

        # Cập nhật danh sách kết quả
        self.viet_tat_list.clear()
        for result, _ in results:
            self.viet_tat_list.addItem(result)

    def on_viet_tat_selected(self, current):
        """Xử lý khi chọn một viết tắt từ danh sách - chua lien ket"""
        if not current:
            return

        viet_tat = current.text()
        ly_do = self.template_manager.get_ly_do(viet_tat)
        self.ly_do_input.setText(ly_do)
        self.ly_do_input.selectAll()  # Chọn toàn bộ text để dễ sửa

    @pyqtSlot()
    def on_save(self):
        """Lưu lý do mới"""
        if not self.ly_do_input.text():
            QMessageBox.warning(self, "Lỗi", "Vui lòng nhập lý do!")
            return

        current_item = self.viet_tat_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "Lỗi", "Vui lòng chọn một viết tắt!")
            return

        if self.template_manager.save_ly_do(self.ly_do_input.text()):
            QMessageBox.information(self, "Thông báo", "Đã lưu thành công!")
        else:
            QMessageBox.warning(self, "Lỗi", "Không thể lưu lý do!")

    def clear_form(self):
        """Xóa trắng form"""
        self.ly_do_input.clear()
        self.viet_tat_list.clearSelection()

    def focus_list(self):
        """Focus vào item đầu tiên trong danh sách khi nhấn Enter"""
        if self.viet_tat_list.count() > 0:
            self.viet_tat_list.setCurrentRow(0)
            self.viet_tat_list.setFocus()

    def on_item_selected(self, item):
        """Xử lý chung cho cả click chuột và space - chua lien ket"""
        if item:
            viet_tat = item.text()
            ly_do = self.template_manager.get_ly_do(viet_tat)
            self.ly_do_input.setText(ly_do)
            self.ly_do_input.setFocus()
            self.ly_do_input.selectAll()

    def get_current_viet_tat(self):
        """Lấy viết tắt đang được chọn"""
        current_item = self.viet_tat_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "Lỗi", "Vui lòng chọn một viết tắt!")
            return None
        return current_item.text()

    def open_latex_file(self):
        """Mở file latex khi bấm Enter ở input và thoát chương trình"""
        viet_tat = self.get_current_viet_tat()
        if not viet_tat:
            return

        # Lấy lý do mới từ input
        ly_do_moi = self.ly_do_input.text()
        if ly_do_moi:  # Nếu có nhập lý do mới
            # Lưu lý do mới vào CSV
            self.template_manager.save_ly_do(ly_do_moi)
            # Sử dụng lý do mới làm viết tắt mới
            viet_tat_moi = ly_do_moi
            self.latex_manager.prepare_and_open_file(viet_tat, viet_tat_moi)
        else:
            self.latex_manager.prepare_and_open_file(viet_tat)

        self.close()  # Đóng cửa sổ PyQt
        sys.exit(0)  # Thoát chương trình hoàn toàn

    def eventFilter(self, obj, event):
        """Xử lý các sự kiện cho các widget"""
        if obj == self.ly_do_input and event.type() == event.KeyPress:
            if event.key() == Qt.Key_Return or event.key() == Qt.Key_Enter:
                self.open_latex_file()
                return True
        return super().eventFilter(obj, event)
