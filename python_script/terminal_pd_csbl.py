import os
import sys

import pandas as pd
import unidecode

# Tắt cảnh báo SettingWithCopyWarning
pd.options.mode.chained_assignment = None  # default='warn'

os.chdir("/home/<USER>/Dropbox/hnd/csv_source")

# Đ<PERSON><PERSON> các tham số từ dòng lệnh
file_path = sys.argv[1]
name = sys.argv[2]


def filter_rows_by_condition(path, name_ptcm):
    # Đọc file Excel
    df = pd.read_csv(path, dtype=str)
    df.fillna("", inplace=True)
    df["check"] = (
        df["ten qt-nt"].str.upper().apply(unidecode.unidecode)
    )  # Lọc DataFrame dựa trên điều kiện trong cột Y
    filtered_df = df[
        df["check"].str.contains(unidecode.unidecode(name_ptcm.upper()), na=False)
    ]
    filtered_df.drop(columns=["check"], inplace=True)
    # In ra từng dòng của filtered_df dưới dạng dict
    for index, row in filtered_df.iterrows():
        print(row.to_dict())
        print("\n")


# Gọi hàm và in kết quả
filter_rows_by_condition(file_path, name)
# filter_rows_by_condition('vanbandi.csv','trich_yeu','cấp điều chỉnh')