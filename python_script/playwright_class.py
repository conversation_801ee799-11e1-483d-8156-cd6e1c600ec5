import datetime
import os
from pathlib import Path
import sys
import time
import pandas as pd
from playwright.sync_api import sync_playwright, TimeoutError as PlaywrightTimeoutError
import pyautogui
import pyperclip
import tkinter as tk

from csv_load_and_export import CsvLoaderFactory
from god_class import (
    TextProcess,
    insert_stt,
    send_notification,
    upload_file_dialog,
)

os.chdir("/home/<USER>/Dropbox/hnd/csv_source")


def wait_for_user_action():
    root = tk.Tk()
    root.withdraw()  # Ẩn cửa sổ chính

    dialog = tk.Toplevel(root)
    dialog.title("Dừng thao tác")
    dialog.geometry(
        "400x150+1000+300"
    )  # width x height + x_offset + y_offset (sang phải)
    dialog.attributes("-topmost", True)  # Always on top
    dialog.resizable(False, False)

    # Tạo nội dung hộp thoại
    label = tk.Label(
        dialog,
        text="🔄 Vui lòng thao tác thủ công trên trình duyệt.\n\nBấm OK để tiếp tục...",
        font=("Arial", 11),
        justify="center",
        padx=20,
        pady=20,
    )
    label.pack(expand=True)

    # Tạo nút OK
    ok_button = tk.Button(
        dialog,
        text="OK",
        command=dialog.destroy,
        font=("Arial", 10, "bold"),
        bg="#4CAF50",
        fg="white",
        padx=30,
        pady=5,
    )
    ok_button.pack(pady=10)

    # Đợi người dùng đóng hộp thoại
    dialog.wait_window()
    root.destroy()


def is_file_created_today(file_path):
    try:
        # Lấy thời gian hiện tại
        today = datetime.datetime.now().date()

        # Lấy thời gian tạo file
        creation_time = datetime.datetime.fromtimestamp(
            os.path.getctime(file_path)
        ).date()

        # So sánh ngày tạo file với ngày hiện tại
        send_notification("File Created Today")
        return today == creation_time

    except FileNotFoundError:
        send_notification("File Not Found")
        return False  # File không tồn tại


class DichVuCong:
    LIST_NV = {
        "tp": "//b[contains(text(),'Lưu Thị Hồng Lê')]",
        "son": "(//b[contains(text(),'Lê Tùng Sơn')])[1]",
        "da": "(//b[contains(text(),'Nguyễn Thị Đào Anh')])[1]",
        "cham": "//b[contains(text(),'Đỗ Thị Châm')]",
    }

    def __init__(self, web):
        self.playwright = None
        self.browser = None
        self.context = None
        self.page = None
        self.df_name = None
        self.folder = None
        self.user = None
        self.store_file = None
        self.trangthai = None

    def setup(self, headless=False):
        self.playwright = sync_playwright().start()
        self.browser = self.playwright.chromium.launch(headless=headless)

    def close_window_and_switch(self):
        self.page.close()
        self.page = self.context.pages[0]

    def login_dvc_by_user(self, user):
        self.user = user
        if self.user == "tungson":
            self.store_file = "dvc_ts.json"
            self.login_dvc("cv.pnvd.syt", "Phutho@01072025")
        elif self.user == "tp":
            self.store_file = "dvc_le.json"
            self.login_dvc("lelth", "Phutho@2025")

    def load_state(self):
        self.context = self.browser.new_context(
            storage_state=f"/home/<USER>/{self.user}.json"
        )

    # def login_dvc(self, name, password):
    #     max_attempts = 3
    #     attempt = 0
    #
    #     while attempt < max_attempts:
    #         if attempt == 0:  # Chỉ tạo context và page mới trong lần đầu tiên
    #             if is_file_created_today("/home/<USER>/{0}".format(self.store_file)):
    #                 self.load_state()
    #                 self.page = self.context.new_page()
    #             else:
    #                 self.context = self.browser.new_context()
    #                 self.page = self.context.new_page()
    #
    #         self.page.goto("https://dichvucong.vinhphuc.gov.vn/hethong/login")
    #         self.dang_nhap_sso()
    #
    #         if not is_file_created_today("/home/<USER>/{0}".format(self.store_file)):
    #             self.page.get_by_test_id("login-page-username-input").fill(name)
    #             self.page.get_by_test_id("login-page-password-input").fill(password)
    #             self.page.get_by_test_id("login-page-continue-login-button").click()
    #         try:
    #             self.page.wait_for_load_state("domcontentloaded", timeout=50000)
    #
    #             start_time = time.time()
    #             timeout = 30  # Thời gian tối đa để kiểm tra (giây)
    #
    #             while time.time() - start_time < timeout:
    #                 login_success = self.page.locator("span.main-sname").is_visible(
    #                     timeout=1000
    #                 )
    #                 login_fail = self.page.locator("h1#kc-page-title").is_visible(
    #                     timeout=1000
    #                 )
    #
    #                 if login_success:
    #                     send_notification("Đăng nhập thành công")
    #                     return  # Thoát khỏi hàm nếu đăng nhập thành công
    #                 elif login_fail:
    #                     break  # Thoát khỏi vòng lặp while nếu đăng nhập thất bại
    #
    #                 time.sleep(1)  # Đợi 1 giây trước khi kiểm tra lại
    #
    #             if login_fail:
    #                 attempt += 1
    #                 send_notification(f"Đăng nhập thất bại, thử lại lần {attempt}")
    #                 continue
    #             else:
    #                 send_notification(
    #                     "Không thể xác định trang thai đăng nhập sau thời gian chờ"
    #                 )
    #                 attempt += 1
    #         except PlaywrightTimeoutError:
    #             send_notification("Timeout khi chờ trang tải")
    #             attempt += 1
    #
    #     if attempt == max_attempts:
    #         raise Exception("Đăng nhập thất bại sau 3 lần thử")

    def login_dvc(self, name, password):
        if is_file_created_today(f"/home/<USER>/{self.user}.json"):
            self.load_state()
            self.page = self.context.new_page()
            self.page.goto("https://motcua.phutho.gov.vn/vi/")
        else:
            self.context = self.browser.new_context()
            self.page = self.context.new_page()
            self.page.goto("https://motcua.phutho.gov.vn/vi/")
            self.page.locator("//input[@id='username']").fill(name)
            self.page.locator("//input[@id='password']").fill(password)
            # Hiển thị hộp thoại GUI always on top ở phía bên phải
            wait_for_user_action()
            self.page.locator("//input[@id='kc-login']").click()

    def click_dependent_element(self, first_xpath: str, relative_xpath: str) -> None:
        """Click vào phần tử phụ thuộc dựa trên phần tử đầu tiên.
        Args:
            first_xpath: XPath của phần tử đầu tiên cần click
            relative_xpath: XPath tương đối của phần tử thứ hai cần click

        Raises:
            PlaywrightTimeoutError: Nếu không tìm thấy phần tử trong thời gian chờ
            Exception: Nếu có lỗi khác xảy ra
        """
        try:
            # Tìm phần tử đầu tiên bằng XPath
            first_element = self.page.locator(first_xpath)

            # Kiểm tra xem phần tử đầu tiên có tồn tại không
            if first_element.count() > 0:
                # Click vào phần tử đầu tiên
                # first_element.click()
                # send_notification(f"Đã click vào phần tử đầu tiên với xpath: {first_xpath}")

                # Tìm phần tử thứ hai dựa trên phần tử đầu tiên với XPath tương đối
                second_element = first_element.locator(f"xpath={relative_xpath}")

                # Kiểm tra xem phần tử thứ hai có tồn tại không
                if second_element.count() > 0:
                    # Click vào phần tử thứ hai
                    second_element.click()
                    send_notification(
                        f"Đã click vào phần tử thứ hai với xpath tương đối: {relative_xpath}"
                    )
                else:
                    send_notification("Không tìm thấy phần tử thứ hai")
                    raise PlaywrightTimeoutError("Không tìm thấy phần tử thứ hai")
            else:
                send_notification("Không tìm thấy phần tử đầu tiên")
                raise PlaywrightTimeoutError("Không tìm thấy phần tử đầu tiên")
        except PlaywrightTimeoutError as e:
            send_notification(f"Timeout khi tìm phần tử: {str(e)}")
            raise
        except Exception as e:
            send_notification(f"Lỗi khi click phần tử phụ thuộc: {str(e)}")
            raise

    def click_by_xpath(self, xpath, timeout=30000):
        element = self.page.wait_for_selector(xpath, timeout=timeout)
        element.click()

    def open_new_hs_page(self, ma_ho_so):
        url = self.page.locator(f"//a[normalize-space()='{ma_ho_so}']").get_attribute(
            "href"
        )
        full_url = (
            f"https://motcua.phutho.gov.vn/https:tracuuvinhphuc.phutho.gov.vn{url}"
        )
        new_page = self.context.new_page()
        new_page.goto(full_url)
        self.page = new_page

    def dang_nhap_sso(self):
        try:
            self.page.get_by_role("link", name=" Đăng nhập SSO").click(timeout=200000)
        except PlaywrightTimeoutError:
            send_notification("Khong the dang nhap sso")
            raise

    def tick_chon_multil_hs(self, list_mhs):
        elements = self.get_row_elements()
        for element in elements:
            if self.get_attribute("value", element) in list_mhs:
                element.locator("ins.iCheck-helper").click()

    def tick_chon_single_hs(self, mhs):
        elements = self.get_row_elements()
        for element in elements:
            if self.get_attribute("value", element) == mhs:
                element.locator("ins.iCheck-helper").click()
                break

    def wait_for_load_done(self):
        self.page.wait_for_load_state("networkidle", timeout=60000)

    def go_to_trangthai(self, trangthai):
        self.trangthai = trangthai
        # if trangthai == "PC_XL_HS":
        #     try:
        #         self.page.locator(f'a[href="/vinhphuc/hoso/&mlcv={trangthai}"]').click(
        #             timeout=10000
        #         )
        #     except PlaywrightTimeoutError:
        #         send_notification(f"Không có hồ sơ để phân công", True)
        #         sys.exit()
        # else:
        #     self.page.locator(
        #         f"//span[@class='submenuTitle ng-star-inserted'][contains(text(),'{trangthai}')]"
        #     ).click(timeout=600000)
        self.page.locator(
            f"//span[@class='submenuTitle ng-star-inserted'][contains(text(),'{trangthai}')]"
        ).click(timeout=600000)
        self.save_state("son")

    def wait_for_frame_to_disappear(
        self, frame_selector, timeout=60, check_interval=0.5
    ):
        start_time = time.time()
        while time.time() - start_time < timeout:
            if not self.page.locator(frame_selector).is_visible():
                return True
            time.sleep(check_interval)
        raise TimeoutError(
            f"Frame {frame_selector} vẫn còn hiển thị sau {timeout} giây"
        )

    def chuyen_buoc(self, name):
        self.page.evaluate("window.scrollTo(0, 0)")
        # Nhấp vào nút chuyển hồ sơ
        self.page.locator("#chuyen_ho_so").click()
        # Đợi và chuyển đổi sang iframe
        frame = self.page.wait_for_selector("#popupFrame")
        frame_content = frame.content_frame()
        label_element = frame_content.locator(f"xpath={DichVuCong.LIST_NV[name]}")
        button_element = label_element.locator("xpath=ancestor::div[1]/div[1]/ins[1]")
        button_element.click()
        frame_content.locator("#btn_chuyenhs").click()

    def save_state(self, user_name):
        self.context.storage_state(
            path="/home/<USER>/{0}".format(f"{user_name}.json"),
        )

    def expand_ds(self):
        time.sleep(5)
        num_rows = self.count_row_hs()
        if num_rows == 10:
            self.page.click("(//div[@class='mat-form-field-infix ng-tns-c111-45'])[1]")
            self.page.locator(
                "(//span[@class='mat-option-text'][normalize-space()='50'])[1]"
            ).click()
            self.page.wait_for_load_state("domcontentloaded", timeout=50000)

        time.sleep(5)

    def download_hs(self, mhs_download, mhs_bs):
        time.sleep(1)
        if len(mhs_bs + mhs_download) > 0:
            # Đặt event listener cho dialog trước khi bắt đầu tải xuống
            dialog_handler = lambda dialog: dialog.accept()
            self.page.once("dialog", dialog_handler)
            with self.page.expect_download(timeout=6000000) as download_info:
                self.page.locator("span#taiGiayTo").click(timeout=60000000)
            download = download_info.value
            download.save_as(
                "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pdf_documents/filehs/hoso.zip"
            )
            send_notification(
                "Đã tải về {0} hồ sơ mới và {1} hồ sơ bổ sung {2}".format(
                    len(mhs_download), len(mhs_bs), self.trangthai
                )
            )

    def is_not_empty_df_name(self):
        return self.df_name is not None and not self.df_name.empty

    def get_bo_sung_and_status(self, element):
        def get_bo_sung_text(element):
            try:
                return element.locator("td.showhide-cot9 p").first.text_content(
                    timeout=5000
                )
            except:
                return element.locator("td.showhide-cot9 div").first.text_content(
                    timeout=5000
                )

        trang_thai = element.locator("td.showhide-cot10").text_content()

        if "Yêu cầu bổ sung" in trang_thai:
            status = "ĐANG TẠM DỪNG"
            bo_sung = get_bo_sung_text(element)
        elif "Đã bổ sung hồ sơ" in trang_thai:
            status = "ĐÃ BS HỒ SƠ\nCHƯA TIẾP TỤC"
            bo_sung = get_bo_sung_text(element)
        else:
            bo_sung = ""
            status = "THẨM ĐỊNH" if self.trangthai == "TD" else "THẨM ĐỊNH THỰC TẾ"

        return bo_sung, status

    def is_tam_dung_csv(self, mahoso):
        return self.df_name.at[mahoso, "trang thai"] == "ĐANG TẠM DỪNG"

    def get_ngay_nhan(self, mahoso):
        self.open_new_hs_page(mahoso)
        for i in range(1, self.page.locator("tr.cen").count()):
            if (
                "Hồng Lê"
                in self.page.locator("tr.cen")
                .locator(f"nth={i}")
                .locator("td:nth-child(2)")
                .text_content()
            ):
                thoi_diem_nhan = (
                    self.page.locator("tr.cen")
                    .locator(f"nth={i}")
                    .locator("td:nth-child(1)")
                    .text_content()
                )
                break
        self.close_window_and_switch()
        return thoi_diem_nhan

    def process_elements(self, mhs_not_download):
        ma_hs_download = []
        ma_hs_bosung = []
        rows = []
        elements = self.get_row_elements()
        print(f"Tổng số elements: {len(elements)}")
        print(f"Số elements row0: {self.page.locator('tr.row0').count()}")
        print(f"Số elements row1: {self.page.locator('tr.row1').count()}")
        for element in elements:
            try:
                bo_sung, status = self.get_bo_sung_and_status(element)
                mahoso = self.get_attribute("value", element)
                if not mahoso:
                    print(f"Không tìm thấy mã hồ sơ cho element")
                    continue

                chu_ho_so = ""
                cccd = ""
                ngay_sinh = ""
                doi_tuong = ""
                alert = ""
                nguoi_dang_ky = self.get_attribute("data-tennguoinop", element)
                if mahoso not in mhs_not_download:
                    print(f"Hồ sơ {mahoso} không nằm trong mhs_not_download")
                    element.locator("ins.iCheck-helper").click()
                    ma_hs_download.append(mahoso)
                    self.open_new_hs_page(mahoso)
                    self.page.locator(
                        "//div[contains(@class,'collapsed-box')]//div[contains(@class,'box-header')]//button[1]"
                    ).click()  # click để mở rộng box chứa thông tin chủ hồ sơ
                    chu_ho_so = self.page.locator("#_fctenChuHoSo").input_value()
                    cccd = self.page.locator("#_fcsoCMNDChuHoSo").input_value()
                    ngay_sinh = self.page.locator("#_fcngaySinhChuHoSo").input_value()
                    doi_tuong = self.page.locator("#_fcmaDoiTuongNopHS").input_value()
                    if nguoi_dang_ky.upper() != chu_ho_so.upper() and doi_tuong == "CN":
                        alert += f"{mahoso} {nguoi_dang_ky} không phải chủ hồ sơ {doi_tuong}\n"
                    self.close_window_and_switch()
                elif (
                    self.is_not_empty_df_name()
                    and status != "ĐANG TẠM DỪNG"
                    and self.is_tam_dung_csv(mahoso)
                ):
                    print(f"Hồ sơ {mahoso} thỏa điều kiện bổ sung")
                    element.locator("ins.iCheck-helper").click()
                    ma_hs_bosung.append(mahoso)
                ma_thu_tuc = self.get_attribute("data-tentatthutuc", element)
                ngaytiepnhan = (
                    element.locator("td.showhide-cot8")
                    .text_content()
                    .strip()
                    .split("  ")[0]
                )
                dia_chi = self.get_attribute("data-diachicongdan", element)
                so_dien_thoai = self.get_attribute("data-didong", element)
                han = self.get_attribute("data-ngayhentra", element)
                row = [
                    mahoso,
                    ma_thu_tuc,
                    chu_ho_so,
                    cccd,
                    ngay_sinh,
                    doi_tuong,
                    status,
                    dia_chi,
                    so_dien_thoai,
                    ngaytiepnhan,
                    bo_sung,
                    han,
                ]
                rows.append(row)
            except Exception as e:
                print(f"Lỗi khi xử lý element: {str(e)}")
                continue

        print(f"Tổng số rows: {len(rows)}")
        if alert:
            send_notification(alert, True)
        return rows, ma_hs_download, ma_hs_bosung

    @staticmethod
    def get_attribute(val, element):
        return element.locator(
            'div.icheckbox_minimal input[type="checkbox"]'
        ).get_attribute(val)

    def cleanup(self):
        self.browser.close()
        self.playwright.stop()

    def count_row_hs(self):
        return self.page.locator("mat-row").count()

    def get_row_elements(self):
        elements = []
        element_0 = []
        element_1 = []

        # Debug số lượng row0 và row1
        count_row0 = self.page.locator("tr.row0").count()
        count_row1 = self.page.locator("tr.row1").count()

        # Lấy elements từ row0
        for i in range(count_row0):
            try:
                element = self.page.locator("tr.row0").locator(f"nth={i}")
                if element:
                    element_0.append(element)
            except Exception as e:
                print(f"Debug - Lỗi khi lấy row0 thứ {i}: {str(e)}")

        # Lấy elements từ row1
        for i in range(count_row1):
            try:
                element = self.page.locator("tr.row1").locator(f"nth={i}")
                if element:
                    element_1.append(element)
            except Exception as e:
                print(f"Debug - Lỗi khi lấy row1 thứ {i}: {str(e)}")

        elements = element_0 + element_1

        # Kiểm tra và loại bỏ trùng lặp
        unique_elements = []
        seen_mahoso = set()
        for element in elements:
            try:
                mahoso = self.get_attribute("value", element)
                if mahoso and mahoso not in seen_mahoso:
                    seen_mahoso.add(mahoso)
                    unique_elements.append(element)
                else:
                    print(
                        f"Debug - Bỏ qua hồ sơ {mahoso} do trùng lặp hoặc không hợp lệ"
                    )
            except Exception as e:
                print(f"Debug - Lỗi khi lấy mã hồ sơ: {str(e)}")
                continue

        print(
            f"Debug - Số lượng elements sau khi loại trùng lặp: {len(unique_elements)}"
        )
        return unique_elements


def get_diachi(string):
    comma_count = 0
    start_index = 0
    for i, char in enumerate(string):
        if char == ",":
            comma_count += 1
        if comma_count == 3:
            start_index = i + 1
            break
    return string[start_index:]


class CapTaiKhoanKetNoi:
    def __init__(self, ngayqd):
        self.playwright = None
        self.browser = None
        self.context = None
        self.path_tkkn = "/home/<USER>/Pcloud_ssd/Pcloud/tkkn.xlsx"
        self.page = None
        self.ngayqd = ngayqd

    def setup(self, headless=False):
        self.playwright = sync_playwright().start()
        self.browser = self.playwright.chromium.launch(headless=headless)

    def login_dqg(self, username, password):
        # Khởi tạo playwright và browser nếu chưa có
        if self.playwright is None:
            self.playwright = sync_playwright().start()

        if self.browser is None:
            self.browser = self.playwright.chromium.launch(headless=False)

        if self.context is None:
            self.context = self.browser.new_context()

        self.page = self.context.new_page()
        self.page.goto("http://duocquocgia.com.vn")
        # self.page.goto(
        #     "http://duocquocgia.com.vn/Application/Index#!/cosokinhdoanh/quanlytaikhoan"
        # )
        self.page.locator(
            "(//input[@placeholder='Tên người dùng hoặc email'])[1]"
        ).fill(username, timeout=60000)
        self.page.locator("//input[@placeholder='Mật Khẩu']").fill(password)
        self.page.locator("//button[contains(text(),'Đăng nhập')]").click()

    def wait_for_download_excel(self):
        with self.page.expect_download(timeout=6000000) as download_info:
            self.page.locator("//button[contains(text(),'Lưu danh sách')]").click()
        download = download_info.value
        download.save_as(self.path_tkkn)

    def add_excel_file(self):
        self.page.locator("//button[contains(text(),'Thêm từ excel')]").click()
        self.page.locator("//span[@class='btn green fileinput-button']").click()
        time.sleep(2)
        upload_file_dialog("CAP_TK_KN.xlsx", Pcloud=True)
        pyautogui.press("enter")

    def generator_excel_file_gdp(self):
        dt = CsvLoaderFactory.create_datetime_loader(["ngay qd"])
        df_gdp = dt.load_df("gdp")
        ngay, thang, nam = self.ngayqd.split("/")
        ngay_qd = f"{nam}-{thang.zfill(2)}-{ngay.zfill(2)}"
        mask1 = df_gdp["ngay qd"] == ngay_qd
        mask2 = df_gdp["so dkkd"].notnull()
        mask = mask1 & mask2
        df_filter = df_gdp[mask]
        insert_stt(df_filter)
        df_filter["SỐ DK KT"] = ""
        df_filter["EMAIL"] = ""

        df_filter["HUYỆN"] = (
            df_filter["KHO 1"]
            .apply(lambda x: x[::-1])
            .str.split(",")
            .str.get(1)
            .apply(lambda x: x[::-1])
            .str.replace(" huyện ", "")
            .str.replace("Thành phố Phúc Yên", "Thị xã Phúc Yên")
            .str.replace("thành phố Vĩnh Yên", "Thành phố Vĩnh Yên")
            .str.replace("thành phố Phúc Yên", "Thị xã Phúc Yên")
        )
        df_filter["XÃ"] = (
            df_filter["tru so"]
            .apply(lambda x: x[::-1])
            .str.split(",")
            .str.get(2)
            .apply(lambda x: x[::-1])
            .str.replace(" xã ", "")
        )
        df_filter["TỈNH"] = "Vĩnh Phúc"
        df_filter["NGƯỜI ĐẠI DIỆN"] = df_filter["ten nguoi ptcm"]
        df_filter["cmnd2"] = df_filter["cmnd"]
        df_filter["SỐ ĐT"] = df_filter["so dt chu hs"]
        df_filter["trinh do cm"] = "Dược sĩ đại học"
        df_filter["dia chi"] = (
            df_filter["tru so"]
            .apply(lambda x: x[::-1])
            .apply(get_diachi)
            .apply(lambda x: x[::-1])
        )
        df_filter["loai hinh"] = "Cơ sở bán buôn thuốc"
        df_filter["so dkkd"] = df_filter["so dkkd"] + "/ĐKKDD-VP"
        df_filter = df_filter[
            [
                "stt",
                "ten cong ty",
                "so dkkd",
                "SỐ DK KT",
                "loai hinh",
                "dia chi",
                "TỈNH",
                "HUYỆN",
                "XÃ",
                "ten nguoi ptcm",
                "cmnd",
                "so dt chu hs",
                "EMAIL",
                "NGƯỜI ĐẠI DIỆN",
                "cmnd2",
                "so cchnd",
                "trinh do cm",
                "SỐ ĐT",
                "ma ho so",
            ]
        ]
        df_filter.fillna("", inplace=True)
        self.df_filter = df_filter
        df_filter.to_excel("/home/<USER>/Dropbox/CAP_TK_KN.xlsx", index=False)
        pyperclip.copy("/home/<USER>/Dropbox/CAP_TK_KN.xlsx")

    def generator_excel_file_gpp(self):
        dt = CsvLoaderFactory.create_datetime_loader(["ngay qd"])
        df_dkkd = dt.load_df("dkkd")

        ngay, thang, nam = self.ngayqd.split("/")
        ngay_qd = f"{nam}-{thang.zfill(2)}-{ngay.zfill(2)}"
        mask1 = df_dkkd["ngay qd"] >= ngay_qd
        mask2 = df_dkkd["thu tuc"] == "CAP GPP VA DKKD"
        mask = mask1 & mask2

        df_filter = df_dkkd[mask]
        insert_stt(df_filter)
        df_filter["SỐ DK KT"] = ""
        df_filter["EMAIL"] = ""

        df_filter["HUYỆN"] = (
            df_filter["dia chi co so"]
            .apply(lambda x: x[::-1])
            .str.split(",")
            .str.get(1)
            .apply(lambda x: x[::-1])
            .str.replace(" huyện ", "")
            .str.replace("Thành phố Phúc Yên", "Thị xã Phúc Yên")
            .str.replace("thành phố Vĩnh Yên", "Thành phố Vĩnh Yên")
            .str.replace("thành phố Phúc Yên", "Thị xã Phúc Yên")
        )
        df_filter["XÃ"] = (
            df_filter["dia chi co so"]
            .apply(lambda x: x[::-1])
            .str.split(",")
            .str.get(2)
            .apply(lambda x: x[::-1])
            .str.replace(" xã ", "")
        )
        df_filter["TỈNH"] = "Vĩnh Phúc"
        df_filter["NGƯỜI ĐẠI DIỆN"] = df_filter["ten nguoi ptcm"]
        df_filter["cmnd2"] = df_filter["cmnd"]
        df_filter["SỐ ĐT"] = df_filter["so dt chu hs"]
        df_filter["dia chi"] = (
            df_filter["dia chi co so"]
            .apply(lambda x: x[::-1])
            .apply(get_diachi)
            .apply(lambda x: x[::-1])
        )
        df_filter["loai hinh"] = df_filter["loai hinh"].str.replace(
            "Nhà thuốc", "Nhà thuốc GPP"
        )
        df_filter["so dkkd"] = df_filter["so dkkd"] + "/ĐKKDD-VP"
        if (df_filter["van de hs"] == "gpp").any():
            df_filter.loc[df_filter["van de hs"] == "gpp", "so dkkd"].str.replace(
                "/ĐKKDD-VP", "/GPP"
            )
        df_filter = df_filter[
            [
                "stt",
                "ten qt-nt",
                "so dkkd",
                "SỐ DK KT",
                "loai hinh",
                "dia chi",
                "TỈNH",
                "HUYỆN",
                "XÃ",
                "ten nguoi ptcm",
                "cmnd",
                "so dt chu hs",
                "EMAIL",
                "NGƯỜI ĐẠI DIỆN",
                "cmnd2",
                "so cchnd",
                "trinh do cm",
                "SỐ ĐT",
                "ma ho so",
            ]
        ]
        df_filter.fillna("", inplace=True)
        self.df_filter = df_filter
        df_filter.to_excel("/home/<USER>/Pcloud_ssd/Pcloud/CAP_TK_KN.xlsx", index=False)
        pyperclip.copy("/home/<USER>/Pcloud_ssd/Pcloud/CAP_TK_KN.xlsx")

    def generator_tk(self):
        df_tkkn = pd.read_excel(self.path_tkkn, header=10, dtype="str").dropna()
        df_tkkn = df_tkkn.head(self.df_filter.shape[0] + 1)
        # df_tkkn = df_tkkn.head(2)
        df_tkkn["Tên cơ sở"] = df_tkkn["Tên cơ sở"].str.upper()
        df_tkkn["Tên người đại diện"] = df_tkkn["Tên người đại diện"].str.upper()
        if "ten cong ty" in self.df_filter.columns:
            ten_loai_hinh = "ten cong ty"
        else:
            ten_loai_hinh = "ten qt-nt"

        self.df_filter[ten_loai_hinh] = self.df_filter[ten_loai_hinh].str.upper()
        self.df_filter["NGƯỜI ĐẠI DIỆN"] = self.df_filter["NGƯỜI ĐẠI DIỆN"].str.upper()
        df_tkkn = pd.merge(
            df_tkkn,
            self.df_filter[[ten_loai_hinh, "NGƯỜI ĐẠI DIỆN", "ma ho so", "so dkkd"]],
            left_on=["SDKKD"],
            right_on=["so dkkd"],
            how="left",
        )
        df_tkkn["dia chi"] = (
            df_tkkn["Địa chỉ"]
            + ", "
            + df_tkkn["Xã/ Phường"]
            + ", "
            + df_tkkn["Quận/ Huyện"]
        )
        df_tkkn["Mật khẩu"] = r"\textbf{123456a}"
        df_tkkn["SDKKD"] = df_tkkn["SDKKD"]
        df_tkkn["Tài khoản kết nối"] = (
            r"\textbf{" + df_tkkn["Tài khoản kết nối"].str.replace("_", r"\_") + "}"
        )
        text = TextProcess("tkkn")
        for _, values in df_tkkn.iterrows():
            text.format_text(values)
            text.compile_latex_confirm()
            name = f"{values['ma ho so']}-TKKN {values['Tên cơ sở']}"
            text.copy_pdf_to_kq(name)
        text.copy_latex_file("/home/<USER>/Dropbox/hnd/latexall/mylatex/tkkn.tex")

    def cleanup(self):
        self.browser.close()
        self.playwright.stop()

    def rename_latest_excel_file(self) -> None:
        """
        Tìm file Excel mới nhất trong thư mục Pcloud và đổi tên thành tkkn.xlsx.

        Returns:
            None
        """
        pcloud_dir = Path("/home/<USER>/Pcloud_ssd/Pcloud")

        # Tìm tất cả file .xlsx trong thư mục
        # Chờ cho đến khi file xlsx xuất hiện
        # Đếm số file xlsx hiện có trong thư mục
        initial_excel_files = list(pcloud_dir.glob("*.xlsx"))
        initial_count = len(initial_excel_files)
        print(f"Số file Excel ban đầu: {initial_count}")

        max_wait_time = 120  # Tối đa chờ 60 giây
        wait_time = 0

        while wait_time < max_wait_time:
            current_excel_files = list(pcloud_dir.glob("*.xlsx"))
            current_count = len(current_excel_files)

            # Kiểm tra xem có thêm file mới không
            if current_count > initial_count:
                # Sắp xếp theo thời gian tạo, lấy file mới nhất (loại trừ CAP_TK_KN.xlsx)
                excel_files_excluding_cap = [
                    f for f in current_excel_files if f.name != "CAP_TK_KN.xlsx"
                ]
                if excel_files_excluding_cap:
                    latest_file = max(
                        excel_files_excluding_cap, key=lambda x: x.stat().st_mtime
                    )
                    print(f"Phát hiện file mới: {latest_file.name}")
                    break

            time.sleep(1)  # Chờ 1 giây
            wait_time += 1

        if wait_time >= max_wait_time:
            print("Không tìm thấy file Excel mới trong thư mục Pcloud sau 60 giây")
            return

        # Tạo đường dẫn mới cho file tkkn.xlsx
        new_file_path = pcloud_dir / "tkkn.xlsx"

        # Nếu file tkkn.xlsx đã tồn tại, xóa nó trước
        if new_file_path.exists():
            new_file_path.unlink()

        # Đổi tên file
        try:
            latest_file.rename(new_file_path)
            print(f"Đã đổi tên file {latest_file.name} thành tkkn.xlsx")
        except Exception as e:
            print(f"Lỗi khi đổi tên file: {e}")

    def process_all_gpp(self):
        self.generator_excel_file_gpp()
        # self.setup()
        # self.login_dqg("syt_vinhphuc", "@lovelyly92H")
        # self.add_excel_file()
        # self.wait_for_download_excel()

        # Gọi hàm đổi tên file
        self.rename_latest_excel_file()
        self.generator_tk()
        # self.cleanup()

    def process_all_gdp(self):
        self.generator_excel_file_gdp()
        self.setup()
        self.login_dqg("syt_vinhphuc", "@lovelyly92H")

        self.add_excel_file()
        self.wait_for_download_excel()
        self.generator_tk()
        self.cleanup()
