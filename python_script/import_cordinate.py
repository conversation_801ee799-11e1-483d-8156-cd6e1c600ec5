import re
import urllib.parse
import csv
import os
import pyperclip
from god_class import send_notification
from typing import Tuple, List


def extract_info_from_google_maps_url(url: str) -> Tuple[str, float, float]:
    """
    Trích xuất tên địa điểm và tọa độ từ URL Google Maps.

    Args:
        url: URL Google Maps

    Returns:
        <PERSON><PERSON> chứa tên địa điểm, vĩ độ và kinh độ
    """
    # Trích xuất tên địa điểm
    place_match = re.search(r"/place/([^/@]+)", url)
    place_name = ""
    if place_match:
        place_name = urllib.parse.unquote(place_match.group(1).replace("+", " "))
    place_name = place_name.split(",")[0].lower()

    # Trích xuất tọa độ chính xác từ URL
    coords_match = re.search(r"!3d(-?\d+\.\d+)!4d(-?\d+\.\d+)", url)
    if coords_match:
        latitude = float(coords_match.group(1))
        longitude = float(coords_match.group(2))
        return place_name, latitude, longitude

    # Nếu không tìm thấy tọa độ chính xác, thử tìm tọa độ từ phần @ trong URL
    coords_match = re.search(r"@(-?\d+\.\d+),(-?\d+\.\d+)", url)
    if coords_match:
        latitude = float(coords_match.group(1))
        longitude = float(coords_match.group(2))
        return place_name, latitude, longitude

    raise ValueError("Không thể trích xuất tọa độ từ URL Google Maps")


def read_existing_place_names(csv_path: str) -> List[str]:
    """
    Đọc tất cả tên địa điểm đã tồn tại trong file CSV.

    Args:
        csv_path: Đường dẫn đến file CSV

    Returns:
        Danh sách các tên địa điểm đã tồn tại (đã được chuyển thành chữ thường)
    """
    place_names = []

    if not os.path.isfile(csv_path):
        return place_names

    try:
        with open(csv_path, mode="r", newline="", encoding="utf-8") as file:
            reader = csv.reader(file)
            next(reader, None)  # Bỏ qua header
            for row in reader:
                if len(row) >= 1:
                    try:
                        # Lấy tên địa điểm từ cột đầu tiên và chuyển thành chữ thường
                        place_name = row[0].strip().lower()
                        if place_name:
                            place_names.append(place_name)
                    except IndexError:
                        continue
    except Exception:
        # Nếu có lỗi khi đọc file, trả về danh sách rỗng
        pass

    return place_names


def is_duplicate_place_name(
    place_name: str,
    existing_place_names: List[str],
) -> bool:
    """
    Kiểm tra xem tên địa điểm có bị trùng lặp không.
    Tên địa điểm được kiểm tra đã được chuẩn hóa về chữ thường.

    Args:
        place_name: Tên địa điểm cần kiểm tra (đã ở dạng chữ thường).
        existing_place_names: Danh sách các tên địa điểm đã tồn tại (đã ở dạng chữ thường).

    Returns:
        True nếu tên địa điểm đã tồn tại, False nếu không.
    """
    return place_name in existing_place_names


def save_to_csv(
    place_name: str,
    latitude: float,
    longitude: float,
    csv_path: str = "/home/<USER>/Dropbox/hnd/csv_source/toa_do.csv",
) -> bool:
    """
    Lưu thông tin địa điểm và tọa độ vào file CSV nếu tọa độ chưa tồn tại.

    Args:
        place_name: Tên địa điểm
        latitude: Vĩ độ
        longitude: Kinh độ
        csv_path: Đường dẫn đến file CSV đích

    Returns:
        True nếu thông tin được lưu, False nếu tọa độ đã tồn tại
    """
    # Tạo thư mục nếu không tồn tại
    os.makedirs(os.path.dirname(csv_path), exist_ok=True)

    # Kiểm tra xem file đã tồn tại chưa
    file_exists = os.path.isfile(csv_path)

    # Đọc tên địa điểm đã tồn tại
    existing_place_names = read_existing_place_names(csv_path)

    # Kiểm tra xem tên địa điểm đã tồn tại chưa
    if is_duplicate_place_name(place_name, existing_place_names):
        return False

    # Lưu thông tin mới nếu tên địa điểm chưa tồn tại
    with open(csv_path, mode="a", newline="", encoding="utf-8") as file:
        writer = csv.writer(file)

        # Ghi header nếu file mới
        if not file_exists:
            writer.writerow(["Tên địa điểm", "Vĩ độ", "Kinh độ"])

        # Ghi thông tin
        writer.writerow([place_name, latitude, longitude])

    return True


def main() -> None:
    """
    Hàm chính để xử lý việc trích xuất thông tin từ URL và lưu vào CSV.
    URL được lấy tự động từ clipboard.
    """
    try:
        # Lấy URL từ clipboard
        url = pyperclip.paste()
        send_notification(
            f"Đã lấy URL từ clipboard: {url[:60]}..." if len(url) > 60 else url
        )

        place_name, latitude, longitude = extract_info_from_google_maps_url(url)

        if save_to_csv(place_name, latitude, longitude):
            send_notification(
                f"Đã lưu thông tin địa điểm '{place_name}' với tọa độ ({latitude}, {longitude}) vào file CSV."
            )
        else:
            send_notification(
                f"Bỏ qua: Tên địa điểm '{place_name}' đã tồn tại trong file CSV."
            )

    except Exception as e:
        send_notification(f"Lỗi: {e}")


if __name__ == "__main__":
    main()
