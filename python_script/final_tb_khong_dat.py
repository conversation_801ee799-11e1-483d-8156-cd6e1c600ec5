from final import Final
from PyQt5 import QtWidgets


class TbKhongDat(Final):
    def __init__(self):
        super().__init__()  # Gọi constructor của class cha

        # Thêm các thuộc t<PERSON>h và phương thức mới
        self.setup_additional_ui()

    def setup_additional_ui(self):
        # V<PERSON> dụ thêm một nút mới
        new_button = QtWidgets.QPushButton("New Button", self.tab_active_hs)
        new_button.setGeometry(QtCore.QRect(800, 0, 75, 51))
        new_button.clicked.connect(self.new_button_clicked)

    def new_button_clicked(self):
        # Xử lý sự kiện click
        print("New button clicked!")

    # Override các phương thức của class cha nếu cần
    def load_csv(self):
        super().load_csv()  # Gọi phương thức của class cha
        # Thêm xử lý mới
        print("Additional CSV loading logic")


# <PERSON><PERSON> chạy ứng dụng
if __name__ == "__main__":
    import sys

    app = QtWidgets.QApplication(sys.argv)
    window = TbKhongDat()
    window.show()
    sys.exit(app.exec_())
