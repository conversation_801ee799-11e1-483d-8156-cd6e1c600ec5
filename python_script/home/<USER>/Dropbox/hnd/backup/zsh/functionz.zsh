function rmf() {
    if [ $# -eq 0 ]; then
        echo "Sử dụng: rmf <đường_dẫn>"
        return 1
    fi

    local target_path=$(realpath "$1")
    
    # Kiểm tra xem đường dẫn có tồn tại không
    if [ ! -e "$target_path" ]; then
        echo "Đường dẫn không tồn tại: $target_path"
        return 1
    fi
    
    # Kiểm tra xem có phải là thư mục con của Dropbox hoặc Pcloud không
    if [[ "$target_path" == /home/<USER>/Dropbox/* ]] || [[ "$target_path" == /home/<USER>/Pcloud_ssd/Pcloud/* ]]; then
        rm -rf "$target_path"
        echo "Đã xóa: $target_path"
    else
        echo "Cảnh báo: Đường dẫn không nằm trong Dropbox hoặc Pcloud"
        echo "Bạn có chắc chắn muốn xóa $target_path? (yes/no)"
        read answer
        if [[ "$answer" == "yes" ]]; then
            rm -rf "$target_path"
            echo "Đã xóa: $target_path"
        else
            echo "Đã hủy thao tác xóa"
        fi
    fi
} 