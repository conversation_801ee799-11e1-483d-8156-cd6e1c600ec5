import sys
import csv_load_and_export as load
import date_solid as dt
from god_class import convert_ngay
from top_most_get_text import input_dialog


df_dkkd = load.load_dkkd(mhs=True)
df_lich_td = load.load_lichthamdinh(mhs=True)
ngaytd = convert_ngay(input_dialog("NHẬP ngay td", "NHẬP NGÀY", dt.get_current_date()))

if not ngaytd or ngaytd not in df_lich_td["ngay td"].values:
    sys.exit()

list_chuyen = []
ngaytd = convert_ngay(ngaytd)
df_lich_td = df_lich_td[df_lich_td["ngay td"] == ngaytd]
for index, values in df_lich_td.iterrows():
    # TODO khởi tạo list chuyển
    def laycotindex(cot, dataframe, ind=index):
        return dataframe.at[ind, cot]

    vande = input_dialog(
        "VẤN ĐỀ SAU THẨM ĐỊNH",
        "VẤN ĐỀ " + values["ten qt-nt"] + " " + values["dia chi co so"],
        "đạt",
    )
    if not vande:
        continue
    self.set_cell_value_df(index, "van de hs", vande)
    self.update_pyqt_item(self.get_index_by_mhs(index), "van de hs", vande)
    self.set_cell_value_df(index, "ngay td", ngaytd)
    self.update_pyqt_item(self.get_index_by_mhs(index), "ngay td", ngaytd)
    df_dkkd.loc[index, "van de hs"] = vande
    df_dkkd.loc[index, "ngay td"] = ngaytd
    if vande:
        self.set_cell_value_df(index, "da di", "1")
        self.update_pyqt_item(self.get_index_by_mhs(index), "da di", "1")
        if self.get_values_at_mhs_df_name(index, "trang thai") == "THẨM ĐỊNH":
            list_chuyen.append(index)
    else:
        self.set_cell_value_df(index, "da di", "")
        self.update_pyqt_item(self.get_index_by_mhs(index), "da di", "")
    if vande == "0":
        # TODO lấy ds các lỗi trượt.
        ds = pd.read_csv("tbkd.csv", dtype="str")
        ds.index += 1

        # Generate string of numbered reasons
        string = "\n".join(f"{i}. {row['Lý do']}" for i, row in ds.iterrows())

        ngay, thang, nam, _ = get_current_date()

        sott = [
            int(num) for num in input_dialog("NHẬP SỐ THỨ TỰ", string, "").split("+")
        ]
        dsf = ds.loc[sott]
        insert_stt(dsf)
        dsf = dsf[["stt", "Lý do"]]

        if len(dsf) == 1:
            lydo_full = r"\textbf{{Lý do: }}" + dsf.at[sott[0], "Lý do"] + "./."
            lydo_full = (
                lydo_full.replace("&", "")
                .replace(r"\\", ")")
                .replace("Khoản", " (căn cứ Khoản")
                .replace("Điểm", "(điểm")
            )
        else:
            lydo = dsf.astype(str).apply("".join, axis=1).str.cat(sep="\n")
            lydo_full = rf"""\vspace{{0.5\baselineskip}}                            
\setstretch{{1}}
\noindent
\begin{{minipage}}{{\textwidth}}
\begin{{tblr}}{{width=1\linewidth,hlines,vlines,
colspec={{X[0.6,c]X[4,c]X[3,c]}}, 
colsep=1pt,
rowsep=1pt,
rows={{font=\small,m}},row{{1}}={{font=\bfseries,c}}}}
STT&Lý do&Căn cứ\\
{lydo}
\end{{tblr}}
\end{{minipage}}
"""

        if 7 in sott:
            diemso = QInputDialog.getText(
                None,
                f"{laycotindex('ten qt-nt', df_dkkd)}",
                laycotindex("ten qt-nt", df_dkkd) + " - Nhập điểm số",
            )
            lydo_full = lydo_full.replace("diemso", diemso[0])

        qthaynt = laycotindex("loai hinh", df_dkkd)
        if qthaynt == "Nh�� thuốc":
            lydo_full = lydo_full.replace("2b", "2a")

        tencskdbb = (
            laycotindex("ten qt-nt", df_dkkd)
            .title()
            .replace("Nhà Thuốc", "Nhà thuốc")
            .replace("Quầy Thuốc", "Quầy thuốc")
        )
        mahs = index
        diachi = laycotindex("dia chi co so", df_dkkd)
        # tdcm = laycotindex("trinh do tat", dsfulldk)
        # ten = laycotindex("ten nguoi ptcm", dsfulldk).title()
        # socchnd = laycotindex("so cchnd", dsfulldk)
        # ngaycapcchnd = laycotindex("ngay cap cchnd", dsfulldk)
        # noicapcchnd = laycotindex("noi cap cchnd", dsfulldk)
        text = rf"""\documentclass{{article}}
\usepackage[T5]{{fontenc}}
\usepackage{{parskip}}
\nonstopmode
\usepackage[fontsize=14pt]{{scrextend}}
\usepackage[a4paper,vmargin=2cm,right=2cm,left=3cm,]{{geometry}}
\usepackage{{mathptmx}}
\usepackage{{ulem}}
\renewcommand{{\ULdepth}}{{7pt}}
\renewcommand{{\ULthickness}}{{0.5pt}}
\usepackage{{setspace}}
\usepackage{{tabularray}}
\usepackage{{gensymb}}
\usepackage{{indentfirst}}
\setlength{{\parindent}}{{1.27cm}}
\setlength{{\parskip}}{{0pt}}
\thispagestyle{{empty}}
\usepackage{{fancyhdr}}
\fancyhf{{}}
\fancyhead[C]{{\small\thepage}}
\renewcommand{{\headrulewidth}}{{0pt}}
\pagestyle{{fancy}}
\begin{{document}}

\setstretch{{1}}
\noindent
\begin{{minipage}}{{\textwidth}}
\begin{{tblr}}{{width=1\linewidth,
colspec={{X[1,c] X[2,c]}},
colsep=0pt,
rowsep=-3pt,
row{{2}} = {{font=\bfseries}}}}
\fontsize{{13pt}}{{0pt}}\selectfont UBND TỈNH VĨNH PHÚC & \fontsize{{13pt}}{{0pt}}\selectfont \textbf{{CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM}}\\
{{\fontsize{{13pt}}{{0pt}}\selectfont SỞ Y TẾ\\[3pt]
\rule[0.6\baselineskip]{{.14\linewidth}}{{.5pt}}}} & \fontsize{{14pt}}{{0pt}}\selectfont \uline{{Độc lập - Tự do - Hạnh phúc}}\\[12pt]
\fontsize{{14pt}}{{0pt}}\selectfont Số: \hspace{{1.5cm}}/TB-SYT & \fontsize{{14pt}}{{0pt}}\selectfont \textit{{Phú Thọ, ngày {ngay} tháng {thang} năm {nam}}}\\[6pt]
\end{{tblr}}
\end{{minipage}}

\begin{{center}}

\textbf{{THÔNG BÁO KẾT QUẢ\\Thẩm định điều kiện cấp Giấy chứng nhận đủ điều kiện kinh doanh dược}}\\
\rule[0.6\baselineskip]{{.4\linewidth}}{{.5pt}}


\noindent
\begin{{minipage}}{{\textwidth}}
\begin{{tblr}}{{width=1\linewidth,
colspec={{X[1,r]X[3,l]}},
rowsep=0pt,
colsep=2pt
}}
Kính gửi: & {tencskdbb}\\
& Địa chỉ: {diachi}.\\
\end{{tblr}}
\end{{minipage}}

\end{{center}}
\vspace{{0.5\baselineskip}}

Căn cứ Luật Dược năm 2016; 

Căn cứ Luật sửa đổi, bổ sung một số điều của Luật Dược 2024;

Căn cứ Nghị định số 163/2025/NĐ-CP ngày 29/6/2025 của Chính phủ quy định chi tiết một số điều và biện pháp tổ chức, hướng dẫn thi hành Luật Dược;

Căn cứ Thông tư số 02/2018/TT-BYT ngày 22/01/2018 của Bộ trưởng Bộ Y tế quy định về Thực hành tốt cơ sở bán lẻ thuốc;

Căn cứ hồ sơ đề nghị cấp giấy chứng nhận đủ điều kiện kinh doanh dược của {tencskdbb}, mã hồ sơ: {mahs};

Căn cứ biên bản đánh giá Thực hành tốt cơ sở bán lẻ thuốc kèm danh mục kiểm tra Thực hành tốt cơ sở bán lẻ thuốc đối với {qthaynt} ngày {ngaytd} của {tencskdbb},

Sở Y tế tỉnh Phú Thọ thông báo kết quả thẩm định như sau: Không đủ điều kiện cấp Giấy chứng nhận đủ điều kiện kinh doanh dược.


{lydo_full}

\vspace{{-0.5\baselineskip}}

\setstretch{{1}}
\noindent
\begin{{minipage}}[t]{{0.5\textwidth}}
\singlespacing
\fontsize{{12pt}}{{14pt}}\selectfont \textbf{{\textit{{Nơi nhận:}}}}\\
\fontsize{{11pt}}{{13pt}}\selectfont - Như kính gửi;\\
- Giám đốc, các PGĐ Sở;\\
- Các phòng CN Sở Y tế;\\\
- Lưu: VT, NVD.\\
\end{{minipage}}
\begin{{minipage}}[t]{{0.5\textwidth}}\singlespacing
\begin{{center}}
\textbf{{KT. GIÁM ĐỐC}}\\
\textbf{{PHÓ GIÁM ĐỐC}}\\
\vspace{{2.8cm}}
\textbf{{Nguyễn Đắc Ca}}\\
\end{{center}}
\end{{minipage}}
\end{{document}}"""
        name = f"THÔNG BÁO KẾT QUẢ Thẩm định điều kiện cấp Giấy chứng nhận đủ điều kiện kinh doanh dược: {tencskdbb}"
        auto_text_to_ioffice(name, "TB", index, text)
        os.chdir("/home/<USER>/Dropbox/hnd/csv_source")
    elif vande == "kp":
        ten = laycotindex("ten nguoi ptcm", df_dkkd).title()
        tenqtnt = laycotindex("ten qt-nt", df_dkkd).title().replace("Thuốc", "thuốc")
        from datetime import datetime

        ngayht = convert_ngay(datetime.now().strftime("%d%m%Y"))

        ngay = ngayht.split("/")[0]
        thang = ngayht.split("/")[1]
        nam = ngayht.split("/")[2]
        texts = rf"""\documentclass{{article}}
\usepackage[T5]{{fontenc}}
\usepackage{{parskip}}
\nonstopmode
\usepackage[fontsize=14pt]{{scrextend}}
\usepackage[a4paper,vmargin=2cm,right=2cm,left=3cm,]{{geometry}}
\usepackage{{mathptmx}}
\usepackage[none]{{hyphenat}}
\usepackage{{ulem}}
\renewcommand{{\ULdepth}}{{7pt}}
\renewcommand{{\ULthickness}}{{0.5pt}}
\usepackage{{setspace}}
\usepackage{{tabularray}}
\usepackage{{indentfirst}}
\sloppy
\setlength{{\parindent}}{{1.27cm}}
\setlength{{\parskip}}{{6pt}}
\pagenumbering{{gobble}}
\begin{{document}}

\setstretch{{1}}
\noindent
\begin{{minipage}}{{\textwidth}}
\begin{{tblr}}{{width=1\linewidth,
colspec={{X[1,c] X[2,c]}},
colsep=0pt,
rowsep=-3pt,
row{{2}} = {{font=\bfseries}}}}
\fontsize{{13pt}}{{0pt}}\selectfont UBND TỈNH VĨNH PHÚC & \fontsize{{13pt}}{{0pt}}\selectfont \textbf{{CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM}}\\
{{\fontsize{{13pt}}{{0pt}}\selectfont SỞ Y TẾ\\[3pt]
\rule[0.6\baselineskip]{{.14\linewidth}}{{.5pt}}}} & \fontsize{{14pt}}{{0pt}}\selectfont \uline{{Độc lập - Tự do - Hạnh phúc}}\\[12pt]
\fontsize{{14pt}}{{0pt}}\selectfont Số: \hspace{{1.5cm}}/TB-SYT & \fontsize{{14pt}}{{0pt}}\selectfont \textit{{Phú Thọ, ngày {ngay} tháng {thang} năm {nam}}}\\[6pt]
\end{{tblr}}
\end{{minipage}}

\vspace{{-0.4cm}}

\begin{{center}}

\textbf{{THÔNG BÁO\\Yêu cầu khắc phục, sửa chữa tồn tại sau thẩm định thực tế ngày {ngaytd}}}\\

\rule[0.6\baselineskip]{{.4\linewidth}}{{.5pt}}

\vspace{{-0.2cm}}

Kính gửi: {tenqtnt}
\end{{center}}

\vspace{{-0.2cm}}

\setstretch{{1}}

Căn cứ Thông tư số 02/2018/TT-BYT ngày 22/01/2018 của Bộ trưởng Bộ Y tế quy định về Thực hành tốt cơ sở bán lẻ thuốc;\par
Căn cứ hồ sơ đề nghị cấp giấy chứng nhận đủ điều kiện kinh doanh dược của {tenqtnt}, mã hồ sơ: {index};\par
Căn cứ Biên bản đánh giá Thực hành tốt cơ sở bán lẻ thuốc ngày {ngaytd} của {tenqtnt}, kết quả đánh giá ở mức ``Cơ sở bán lẻ thuốc phải báo cáo khắc phục'' (đính kèm Thông báo),\par
Tên cơ sở bán lẻ: {tenqtnt}; địa chỉ: {laycotindex('dia chi co so', df_dkkd)};\par
Người phụ trách chuyên môn: {ten}; số chứng chỉ hành nghề dược: {laycotindex('so cchnd', df_dkkd) if '-' in laycotindex('so cchnd', df_dkkd) else laycotindex('so cchnd', df_dkkd) + '/CCHND-SYT-VP'}; nơi cấp: {laycotindex('noi cap cchnd', df_dkkd)}; ngày cấp: {laycotindex('ngay cap cchnd', df_dkkd)}.\par
Sở Y tế yêu cầu {tenqtnt} khắc phục, các tồn tại đã nêu trong Biên bản đánh giá Thực hành tốt cơ sở bán lẻ thuốc trong 07 ngày làm việc, kể từ ngày ban hành Thông báo.\par
Sau khi hoàn thành việc khắc phục, {tenqtnt} báo cáo Sở Y tế việc khắc phục, kèm theo bằng chứng (hồ sơ tài liệu, hình ảnh, video) chứng minh (mẫu báo cáo khắc phục theo Phụ lục đính kèm Thông báo).

Sở Y tế thông báo và yêu cầu {tenqtnt} khẩn trương thực hiện./.\par

\vspace{{-0.5cm}}

\noindent
\begin{{minipage}}[t]{{0.5\textwidth}}\singlespacing
\fontsize{{12pt}}{{14pt}}\selectfont \textbf{{\textit{{Nơi nhận:}}}}\\
\fontsize{{11pt}}{{13pt}}\selectfont - Như kính gửi;\\
\fontsize{{11pt}}{{13pt}}\selectfont - Giám đốc, các PGĐ sở;\\
\fontsize{{11pt}}{{13pt}}\selectfont - Các phòng CN Sở Y tế;\\\
\fontsize{{11pt}}{{13pt}}\selectfont - Lưu: VT, NVD.\\
\end{{minipage}}
\begin{{minipage}}[t]{{0.5\textwidth}}\singlespacing
\begin{{center}}
\textbf{{KT. GIÁM ĐỐC}}\\
\textbf{{PHÓ GIÁM ĐỐC}}\\
\vspace{{3cm}}
\textbf{{Nguyễn Đắc Ca}}\\
\end{{center}}
\end{{minipage}}
\newpage



\newgeometry{{margin=1cm}}
\setstretch{{1.1}}

\setlength{{\parskip}}{{6pt}}
\begin{{center}}

\textbf{{	Phụ lục\\MẪU BÁO CÁO KHẮC PHỤC}}

\textit{{	(Đính kèm Thông báo số \hspace{{1.5cm}}/TB-SYT ngày {ngayht} của Sở Y tế tỉnh Phú Thọ)}}

\bfseries

CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM

\uline{{Độc lập - Tự do - Hạnh phúc}}

\end{{center}}


\newcommand\fillin[1][3cm]{{\makebox[#1]{{\dotfill}}}}

{{\centering    \textbf{{BÁO CÁO KHẮC PHỤC}}\par}}

\vspace{{0.5cm}}

Kính gửi: Sở Y tế tỉnh Phú Thọ

Tên cơ sở bán lẻ: \dotfill

Tên người phụ trách chuyên môn dược: \dotfill

Căn cứ Biên bản đánh giá ``Thực hành tốt cơ sở bán lẻ thuốc'' ngày \fillin[3cm]  của Đoàn thẩm định, Sở Y tế tỉnh Phú Thọ, cơ sở bán lẻ thuốc đã tiến hành khắc phục, sửa chữa những tồn tại ghi trong biên bản. Cụ thể:

\noindent
\begin{{minipage}}{{\textwidth}}
\begin{{tblr}}{{width=1\linewidth,hlines,vlines,
colspec={{X[0.5,c] X[3,c] X[3,c]}},
colsep=3pt,
rowsep=3pt,
rows={{font=\small,m,1.3cm}},
row{{1}}={{font=\bfseries}}}}
STT & Tồn tại nêu trong biên bản & Kết quả khắc phục\\
1\\
2\\
3\\
4\\
5\\
6\\
\end{{tblr}}
\end{{minipage}}

Tôi kính đề nghị Sở Y tế tỉnh Phú Thọ kiểm tra, đánh giá lại và xem xét, công nhận cơ sở bán lẻ thuốc đạt tiêu chuẩn \textbf{{"Thực hành tốt cơ sở bán lẻ thuốc"}}

\vspace{{-0.5cm}}

\hfill\begin{{minipage}}{{0.7\textwidth}}\singlespacing
\begin{{center}}
\textit{{Phú Thọ, ngày \fillin[1.5cm] tháng \fillin[1.5cm] năm \fillin[1.5cm]}}\\
\textbf{{NGƯỜI PHỤ TRÁCH CHUYÊN MÔN}}\\
\textit{{(Ký, ghi rõ họ tên)}}\\
\vspace{{3cm}}
\end{{center}}
\end{{minipage}}
\end{{document}}"""
        auto_text_to_ioffice_sure(
            f"Yêu cầu khắc phục, sửa chữa tồn tại sau thẩm định thực tế ngày {ngaytd}: {tenqtnt}",
            "TB",
            0,
            texts,
        )
        lastest_word = get_lastest_file("/home/<USER>/Desktop", ".docx")
        number_string = lastest_word.split("-")[1]
        self.set_cell_value_df(index, "van de hs", number_string)
        change_workspace("pyqt")
        os.chdir("/home/<USER>/Dropbox/hnd/csv_source")

df_dkkd.to_csv("dkkd.csv", index=True, index_label="ma ho so")
export_name_csv(self.name)
if len(list_chuyen) > 0:
    driver = selenium_start("TD")
    phancong(2, list_chuyen, driver)
    show_message("THONG BAO", f"Đã chuyển TĐTT {len(list_chuyen)} hồ sơ")
