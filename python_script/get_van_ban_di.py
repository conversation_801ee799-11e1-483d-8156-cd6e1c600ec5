from datetime import datetime
import telepot
import demjson3 as demjson
import pandas as pd
import requests
from qlvb_di_GUI import get_current_year

nam = get_current_year() - 2
def request_post(url, payload):
    headers = {
        'Authorization': 'Bearer acKKZDmXqpUmn7D3vuzwk88RN909S7Soc5YlXQhyV6I9lU6UNYaTHtnVEhzxoo6Pul1K2NroQeS6584FZDucIYETRjjn70alDIbOUQ4V3zt8JarwIvOhqZ+z96uFvFuZjsu03k3l5Ou0DhYlEmlQ/15duefP7Ozfa3bfIo3bASLX3cOwnkpUl/pQoUl8aw6UA7KwPJDjnXYn/NR0nW7uuYcPT7ic/DdhkiE08Us6bbSjqfTbHGdR8fC8Nza/I52dVn4RLZDAGfVFhFgZS6qw2j2VTpQ1hpMelekgyrg23fFO2qhb81RllfgYPTifzG9j',
        'Connection': 'keep-alive', 'Content-Type': 'application/x-www-form-urlencoded', }
    response = requests.post(url, headers=headers, data=payload)
    return demjson.decode(response.text)
year_now=datetime.now().year
# year_now=2024
res = request_post("https://qlvb-api.vinhphuc.gov.vn/api/van-ban-di/danh-sach-van-ban-di-da-phat-hanh-cua-chuyen-vien",f'ma_ctcb_cv=6075&ma_don_vi_quan_tri=1573&nam={year_now}&ngay_di_den_ngay=19%2F06%2F2030&ngay_di_tu_ngay=01%1F01%1F{year_now}&page=1&size=15000')


token = '2144388053:AAEzMqn010m3XiLJXXWb45f2hG8POKvjz2M'  # telegram token
receiver_id = '-1001666712476'
bot = telepot.Bot(token)



def format_date(input_date):
    return input_date.split(' ')[0]

rows_list = []
for item in res['data']:
    col1=item['noi_luu_ban_chinh']
    col2=item['trich_yeu']
    col3=item['so_ky_hieu']
    col4=item['ngay_ban_hanh']
    col5=item['nguoi_ky']
    col6=f"https://iqlvb.vinhphuc.gov.vn/van-ban-di/xem-van-ban-di-chi-tiet?id={int(item['ma_van_ban_di_kc'])}&t=vb_di_da_phat_hanh_cua_cv&v=cv&xld={int(item['ma_xu_ly_di'])}"
    row=[col1,col2,col3,col4,col5,col6]
    rows_list.append(row)
column_names = ['phong', 'trich_yeu', 'so_ky_hieu', 'ngay_ban_hanh', 'nguoi_ky', 'link']
df = pd.DataFrame(rows_list, columns=column_names)
df.to_csv('/home/<USER>/Dropbox/hnd/csv_source/vanbandi.csv', index=False)

