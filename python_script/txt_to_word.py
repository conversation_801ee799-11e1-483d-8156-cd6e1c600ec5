#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script để chuyển các dòng bị ngắt trong file txt thành một dòng duy nhất,
và tạo file Word (.docx) với định dạng trang A4, lề trái=3cm, phải=2cm,
trên=2cm, dưới=2cm, giãn dòng 1, cách đoạn 6pt, thụt lề trái firstline=1.27cm.

Quy luật xử lý:
- Các dòng bị ngắt dòng liên nhau (kết thúc và bắt đầu với chữ) sẽ được nối thành một dòng duy nhất
- Dòng chỉ chứa một số duy nhất (số trang) sẽ bị xóa bỏ
- Chỉ xóa các dòng hoàn toàn trống (không có ký tự nào), giữ nguyên cấu trúc đoạn văn
- Tự động tìm file .txt mới nhất trong thư mục Desktop
"""

import re
import sys
import os
import glob
from pathlib import Path
from typing import List, Optional, Tuple
import time

import docx
from docx import Document
from docx.shared import Cm, Pt
from docx.enum.text import WD_LINE_SPACING, WD_PARAGRAPH_ALIGNMENT


def get_latest_txt_file(directory: str) -> str:
    """
    Tìm file .txt mới nhất trong thư mục được chỉ định, không chứa từ "processed" trong tên.

    Args:
        directory: Đường dẫn đến thư mục cần tìm

    Returns:
        Đường dẫn đến file .txt mới nhất

    Raises:
        FileNotFoundError: Nếu không tìm thấy file .txt nào
    """
    # Tìm tất cả các file .txt trong thư mục
    pattern = os.path.join(directory, "*.txt")
    txt_files = glob.glob(pattern)

    # Lọc ra các file không chứa từ "processed" trong tên
    filtered_files = [
        file for file in txt_files if "processed" not in os.path.basename(file)
    ]

    if not filtered_files:
        raise FileNotFoundError(
            f"Không tìm thấy file .txt nào trong thư mục {directory}"
        )

    # Sắp xếp theo thời gian sửa đổi, mới nhất đầu tiên
    latest_file = max(filtered_files, key=os.path.getmtime)

    print(f"Đã tìm thấy file .txt mới nhất: {latest_file}")
    return latest_file


def process_text_file(
    input_path: str, output_path: Optional[str] = None, create_docx: bool = True
) -> Tuple[str, str]:
    """
    Đọc file văn bản và nối các dòng bị ngắt thành một dòng duy nhất.

    Args:
        input_path: Đường dẫn đến file văn bản đầu vào
        output_path: Đường dẫn đến file văn bản đầu ra (tùy chọn, mặc định là thêm "_processed" vào tên file gốc)
        create_docx: Có tạo file .docx hay không

    Returns:
        Tuple gồm đường dẫn file txt đầu ra và đường dẫn file docx (nếu được tạo)
    """
    # Xác định đường dẫn file đầu ra nếu không được cung cấp
    if output_path is None:
        input_file = Path(input_path)
        output_path = str(
            input_file.parent / f"{input_file.stem}_processed{input_file.suffix}"
        )

    try:
        # Đọc nội dung file đầu vào
        with open(input_path, "r", encoding="utf-8") as file:
            content = file.read()

        # Bước 1: Xử lý nội dung để nối dòng bị ngắt và loại bỏ số trang
        processed_content = process_content(content)

        # Bước 2: Xóa các dòng hoàn toàn trống
        final_content = remove_empty_lines(processed_content)

        # Chuyển nội dung đã xử lý thành danh sách các dòng
        processed_lines = final_content.splitlines()

        # Ghi nội dung đã xử lý vào file đầu ra
        with open(output_path, "w", encoding="utf-8") as file:
            file.write(final_content)

        print(f"Đã xử lý thành công file: {input_path}")
        print(f"Kết quả được lưu tại: {output_path}")

        # Tạo file docx nếu yêu cầu
        docx_path = None
        if create_docx:
            docx_path = output_path.replace(".txt", ".docx")
            create_docx_file(processed_lines, docx_path)
            print(f"Đã tạo file Word tại: {docx_path}")

        return output_path, docx_path

    except Exception as e:
        print(f"Lỗi: {e}")
        sys.exit(1)


def process_content(content: str) -> str:
    """
    Xử lý nội dung văn bản đầy đủ để nối các dòng bị ngắt và xóa số trang.

    Args:
        content: Nội dung văn bản đầu vào

    Returns:
        Nội dung văn bản đã được xử lý
    """
    # Loại bỏ các dòng chỉ chứa số trang
    lines = content.splitlines()
    filtered_lines = []

    for line in lines:
        # Bỏ qua các dòng chỉ chứa số trang
        if is_page_number_line(line.strip()):
            continue
        filtered_lines.append(line)

    # Nối các dòng bị ngắt
    result_lines = []
    current_paragraph = ""

    for i, line in enumerate(filtered_lines):
        stripped_line = line.strip()

        # Nếu là dòng trống hoặc chỉ có khoảng trắng, giữ nguyên
        if not stripped_line:
            # Kết thúc đoạn văn hiện tại nếu có
            if current_paragraph:
                result_lines.append(current_paragraph)
                current_paragraph = ""

            # Giữ nguyên dòng trống với định dạng gốc
            result_lines.append(line)
            continue

        # Kiểm tra xem dòng hiện tại có phải là tiếp tục của dòng trước không
        if current_paragraph and not is_new_paragraph(stripped_line):
            # Nối dòng hiện tại vào đoạn văn hiện tại
            current_paragraph += " " + stripped_line
        else:
            # Lưu đoạn văn hiện tại nếu có
            if current_paragraph:
                result_lines.append(current_paragraph)

            # Bắt đầu đoạn văn mới
            current_paragraph = stripped_line

    # Thêm đoạn văn cuối cùng nếu có
    if current_paragraph:
        result_lines.append(current_paragraph)

    # Nối các dòng lại với nhau, giữ nguyên định dạng dòng
    return "\n".join(result_lines)


def remove_empty_lines(content: str) -> str:
    """
    Xóa các dòng hoàn toàn trống (không có ký tự nào), giữ nguyên cấu trúc đoạn văn.

    Args:
        content: Nội dung văn bản đã được xử lý nối dòng

    Returns:
        Nội dung văn bản sau khi xóa dòng hoàn toàn trống
    """
    # Tách nội dung thành các dòng
    lines = content.splitlines()

    # Chỉ loại bỏ các dòng hoàn toàn trống (không có ký tự nào, kể cả khoảng trắng)
    filtered_lines = [line for line in lines if line != ""]

    # Nối lại các dòng
    return "\n".join(filtered_lines)


def create_docx_file(text_lines: List[str], output_path: str) -> None:
    """
    Tạo file Word (.docx) từ danh sách các dòng văn bản với định dạng theo yêu cầu.

    Args:
        text_lines: Danh sách các dòng văn bản đã xử lý
        output_path: Đường dẫn đến file Word đầu ra
    """
    # Tạo document mới
    doc = Document()

    # Thiết lập kích thước trang A4 và lề
    section = doc.sections[0]
    section.page_width = Cm(21)  # A4 width
    section.page_height = Cm(29.7)  # A4 height
    section.left_margin = Cm(3)  # Lề trái 3cm
    section.right_margin = Cm(2)  # Lề phải 2cm
    section.top_margin = Cm(2)  # Lề trên 2cm
    section.bottom_margin = Cm(2)  # Lề dưới 2cm

    # Thiết lập style mặc định
    style = doc.styles["Normal"]
    style.font.name = "Times New Roman"
    style.font.size = Pt(12)

    # Thêm từng đoạn văn vào document
    for line in text_lines:
        # Nếu là dòng chỉ có khoảng trắng, thêm đoạn văn trống
        if line.strip() == "" and line != "":
            doc.add_paragraph()
        else:
            p = doc.add_paragraph(line)
            p.alignment = WD_PARAGRAPH_ALIGNMENT.JUSTIFY
            p.paragraph_format.line_spacing = 1.0
            p.paragraph_format.space_after = Pt(6)
            p.paragraph_format.first_line_indent = Cm(1.27)

    # Lưu document
    doc.save(output_path)


def is_page_number_line(line: str) -> bool:
    """
    Kiểm tra xem dòng có phải chỉ chứa một số duy nhất (số trang) hay không.

    Args:
        line: Dòng văn bản cần kiểm tra

    Returns:
        True nếu dòng chỉ chứa một số duy nhất, False nếu ngược lại
    """
    # Xóa tất cả khoảng trắng và kiểm tra nếu chỉ còn lại số
    cleaned_line = line.strip()
    return cleaned_line.isdigit() or (
        cleaned_line and all(c.isdigit() or c.isspace() for c in cleaned_line)
    )


def is_new_paragraph(line: str) -> bool:
    """
    Xác định xem một dòng có phải là bắt đầu của một đoạn văn mới hay không.

    Args:
        line: Dòng văn bản cần kiểm tra

    Returns:
        True nếu dòng là bắt đầu của đoạn văn mới, False nếu là tiếp tục của đoạn trước
    """
    # Kiểm tra nếu dòng bắt đầu bằng số, dấu gạch ngang, hoặc các ký tự đặc biệt khác
    if re.match(r"^[\d\-\.]+\s+", line):
        return True

    # Kiểm tra các mẫu đánh dấu đoạn mới như tiêu đề, danh mục, v.v.
    if line.isupper() or line.startswith(("CỘNG HÒA", "UBND", "SỞ")):
        return True

    return False


if __name__ == "__main__":
    try:
        # Tự động tìm file .txt mới nhất trong thư mục Desktop
        desktop_dir = "/home/<USER>/Desktop"
        input_file = get_latest_txt_file(desktop_dir)

        # Tạo tên file output
        input_path = Path(input_file)
        output_file = str(
            input_path.parent / f"{input_path.stem}_processed{input_path.suffix}"
        )

        # Xử lý file
        txt_path, docx_path = process_text_file(
            input_file, output_file, create_docx=True
        )
    except Exception as e:
        print(f"Lỗi: {e}")
        sys.exit(1)
