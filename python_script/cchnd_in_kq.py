import os
import pickle
import shutil
import sys
import subprocess
import pandas as pd

from god_class import (
    TextProcess,
    convert_ngay,
    lower_first_char,
    convert_unix_style,
    send_notification,
    show_message,
)

os.chdir("/home/<USER>/Dropbox/hnd/latexall/mylatex")
dsfullcc = pd.read_csv("/home/<USER>/Dropbox/hnd/csv_source/cchnd.csv", dtype="str")
dsrut = dsfullcc.tail(80)
# dsrut = dsfullcc
mask1 = dsfullcc["so qd"].isnull()
mask2 = dsfullcc["van de hs"].isnull()
mask3 = ~dsfullcc["ngay qd"].isnull()
cchnd = []
name = []
compilefile = []
values = {}
with open(
    "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pickle_file/soqd_cc.pk", "rb"
) as file:
    values["so_qd"] = pickle.load(file)
dscc = (
    dsrut[mask1 & mask2 & mask3]
    if len(dsrut[mask1 & mask2 & mask3]) > 0
    else dsrut[dsrut["so qd"] == values["so_qd"]]
)
send_notification(f"Chuan bi in {dscc.shape[0]} cchnd")

dsfullcc.loc[mask1 & mask2 & mask3, "so qd"] = values["so_qd"]
dsfullcc.to_csv("/home/<USER>/Dropbox/hnd/csv_source/cchnd.csv", index=False)
dscc = dscc[
    [
        "ma ho so",
        "so cchnd",
        "loai cap",
        "ten nguoi ptcm",
        "ngay sinh",
        "cmnd",
        "noi cap cmnd",
        "ngay cap cmnd",
        "dia chi thuong tru",
        "trinh do cm",
        "dang ky pham vi",
        "so cchnd cu",
        "ngay cchnd cu",
        "ngay",
        "thang",
        "nam",
        "ngay qd",
        "gioi tinh",
    ]
]

dscc.fillna("", inplace=True)


def thay_the(row):
    if (
        not row["so cchnd cu"] or "thu hồi" in row["loai cap"]
    ):  # neu la cap lan dau hoac cap sau thu hoi
        return ""
    if "/" in row["so cchnd cu"]:
        return rf". \par Thay thế cho Chứng chỉ hành nghề dược số {row['so cchnd cu']} ngày {row['ngay cchnd cu']}"
    else:
        return rf". \par Thay thế cho Chứng chỉ hành nghề dược số {row['so cchnd cu']}/CCHN-D-SYT-PT ngày {row['ngay cchnd cu']}"


try:
    dscc["THAY THẾ"] = dscc.apply(thay_the, axis=1)
except:
    show_message("Thong bao", "Co the khong tim thay dong nao thoa man dieu kien")
    raise Exception("Co the khong tim thay dong nao thoa man dieu kien")


def danh_dau_cho_tra(frame):
    df = pd.read_csv(
        "/home/<USER>/Dropbox/hnd/csv_source/name.csv",
        dtype="str",
        index_col="ma ho so",
    )
    list_cho_tra = frame["ma ho so"].tolist()
    df.loc[df.index.isin(list_cho_tra), "CHỜ TRẢ"] = "1"
    df.to_csv("/home/<USER>/Dropbox/hnd/csv_source/name.csv", index_label="ma ho so")


for mahs in dscc["ma ho so"].tolist():
    # Kiểm tra xem mã hồ sơ có trong thư mục ảnh CCHND hay không
    anh_cchnd_dir = "/home/<USER>/anh_cchnd"
    try:
        # Lấy danh sách tất cả các file trong thư mục ảnh CCHND
        anh_cchnd_files = os.listdir(anh_cchnd_dir)

        # Kiểm tra xem mã hồ sơ có trong tên file nào không
        if not any(mahs in file for file in anh_cchnd_files):
            print(f"Mã hồ sơ {mahs} không có trong thư mục ảnh CCHND")
            sys.exit(1)  # Dừng thực thi mã
    except Exception as e:
        print(f"Lỗi khi kiểm tra thư mục ảnh CCHND: {str(e)}")
        sys.exit(1)  # Dừng thực thi mã nếu có lỗi

danh_dau_cho_tra(dscc)

list_text = []
list_pdf = []
check = True

for _, row in dscc.iterrows():
    values["so_cchnd"] = row["so cchnd"].replace(r"/CCHND-SYT-VP", "")
    values["so_hieu"] = (
        rf"\textbf{{\color{{red}} {values['so_cchnd']}}} /CCHN-D-SYT-PT"
        if ((row["loai cap"] == "") or ("thu hồi" in row["loai cap"]))
        else rf"""\textbf{{\color{{red}} {values["so_cchnd"]}}} /CCHN-D-SYT-PT\\{row["loai cap"].split(":")[0].strip().replace("(", "")}"""
    )
    values["ong-ba"] = "Ông" if row["gioi tinh"] == "1" else "Bà"
    if "thu hồi" not in row["loai cap"] and row["loai cap"] != "":
        values["gian_dong"] = "1.3"
    else:
        values["gian_dong"] = "1.5"
    if len(row["THAY THẾ"]) > 5:
        values["gian_dong"] = "1.3"

    values["nguoiptcm"] = row["ten nguoi ptcm"]

    values["ngaysinh"] = row["ngay sinh"]
    values["cmnd"] = row["cmnd"]
    if len(values["cmnd"]) < 9:
        send_notification(f"{row['ten nguoi ptcm']} sai số cmnd: {row['cmnd']}", True)
        check = False
        break

    values["ngay_cmnd"] = row["ngay cap cmnd"]
    values["noicapcmnd"] = row["noi cap cmnd"]
    values["diachitt"] = lower_first_char(row["dia chi thuong tru"])
    if len(values["diachitt"]) > 60:
        values["diachitt"] = values["diachitt"].replace("tỉnh", "")

    values["trinhdocm"] = r"\textbf{{" + row["trinh do cm"] + r"}}"
    values["dkphamvi"] = r"\textbf{{" + row["dang ky pham vi"] + r"}}"
    values["ngay_qd"] = convert_ngay(row["ngay qd"])
    values["ngay"], values["thang"], values["nam"] = values["ngay_qd"].split("/")
    values["thay_the"] = row["THAY THẾ"]
    values["ma ho so"] = row["ma ho so"]

    source_folder = "/home/<USER>/anh_cchnd"
    dest_folder = "/home/<USER>/Dropbox/hnd/latexall/mylatex"
    cchn = TextProcess("cchnd_in_kq")
    cchn.format_text(values)
    file_name = f"CCHND_{row['ma ho so']}_{convert_unix_style(values['nguoiptcm'])}"
    tex_dest_path = os.path.join(
        "/home/<USER>/Dropbox/hnd/latexall/cchnd", file_name + ".tex"
    )
    cchn.copy_latex_file(tex_dest_path)
    list_text.append(tex_dest_path)
    list_pdf.append(file_name)
if not check:
    sys.exit()
path = "/home/<USER>/Dropbox/hnd/latexall/mylatex/mylatex.tex"
send_notification("Wait! Start Compiling")
for index, text in enumerate(list_text):
    shutil.copy(text, path)
    cchn.compile_latex_confirm()
    cchn.copy_pdf_to_kq(list_pdf[index])

subprocess.run(
    [
        "kitty",
        "-e",
        "yazi",
        "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pdf_documents/kq",
    ]
)
