import os
import pandas as pd

def convert_xlsx_to_csv(directory):
    # Walk through the directory
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith('.xlsx'):
                # Construct full file path
                file_path = os.path.join(root, file)

                # Load the Excel file
                excel_data = pd.read_excel(file_path)

                # Construct the CSV file path
                csv_file_path = os.path.splitext(file_path)[0] + '.csv'

                # Save as CSV
                excel_data.to_csv(csv_file_path, index=False)

                print(f"Converted: {file_path} to {csv_file_path}")

# Define the directory containing the .xlsx files
directory = os.path.expanduser("~/Dropbox/hnd/latexall/documents/son_tong_hop_2020_2021/qd_cchnd_2021")
convert_xlsx_to_csv(directory)

# Call the function to convert .xlsx files to .csv