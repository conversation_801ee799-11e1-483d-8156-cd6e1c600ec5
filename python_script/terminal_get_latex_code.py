import os
import sys
import pandas as pd
import pyperclip

os.chdir("/home/<USER>/Dropbox/hnd/csv_source")
file_name = sys.argv[1]
# file_name = "lichthamdinh.csv"

df = pd.read_csv(file_name, dtype=str)
# N<PERSON>i các cột bằng & và kết thúc dòng bằng \\
rows = []
for _, row in df.iterrows():
    # Nối các giá trị trong hàng bằng & và thêm \\ vào cuối
    row_str = " & ".join(str(val) for val in row.values) + " \\\\"
    rows.append(row_str)

# Nối các dòng lại với nhau
latex_table = "\n".join(rows)
pyperclip.copy(latex_table)
