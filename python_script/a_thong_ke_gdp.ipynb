{"cells": [{"cell_type": "code", "execution_count": 1, "id": "9e8e8122782dad35", "metadata": {}, "outputs": [], "source": ["import pandas as pd "]}, {"cell_type": "code", "execution_count": 4, "id": "263d1059", "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "\n", "os.chdir('/home/<USER>/Dropbox/hnd/csv_source')"]}, {"cell_type": "code", "execution_count": 5, "id": "a23c7c3a", "metadata": {}, "outputs": [], "source": ["df=pd.read_csv('dkkd.csv',dtype=str)"]}, {"cell_type": "code", "execution_count": 6, "id": "752d5a9c", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['CAP GPP VA DKKD', 'DANH GIA DUY TRI DAP UNG GPP',\n", "       'CẤP LẦN ĐẦU GCN ĐĐKKDD VÀ GDP CÔNG TY', nan,\n", "       'DANH GIA DUY TRI DAP UNG GDP', '<PERSON><PERSON><PERSON> lạ<PERSON> (GPP)',\n", "       'CẤP LẠI GPP VÀ ĐKKD',\n", "       'DIEU CHINH DKKD LẦN 1 - THAY NGƯỜI PHỤ TRÁCH CHUYÊN MÔN DƯỢC',\n", "       'CAP LAI GCN ĐĐKKDD LẦN 1 - LỖI SAI CỦA CƠ QUAN CẤP',\n", "       'DANH GIA DAP UNG GPP NTBV', 'CẤP ĐIỀU CHỈNH GPP',\n", "       'DIEU CHINH DKKD LẦN 1 - BỔ SUNG PHẠM VI BÁN THUỐC BẢO QUẢN Ở ĐIỀU KIỆN LẠNH',\n", "       'CAP LAI GCN ĐĐKKDD LẦN 1 - CẤP LẠI DO LÀM MẤT',\n", "       'DIEU CHINH DKKD LẦN 1 - THAY ĐỔI TÊN CƠ SỞ BÁN LẺ',\n", "       'CAP DIEU CHINH LẦN 2 - THAY ĐỔI TÊN CƠ SỞ BÁN LẺ',\n", "       'CAP DIEU CHINH LẦN 2 - THAY NGƯỜI PHỤ TRÁCH CHUYÊN MÔN DƯỢC',\n", "       'CAP DIEU CHINH LẦN 1 - THAY NGƯỜI PHỤ TRÁCH CHUYÊN MÔN DƯỢC',\n", "       'CAP DIEU CHINH LẦN 1 - THAY ĐỔI TÊN CƠ SỞ BÁN LẺ',\n", "       'CAP LẠI LẦN 1 - LỖI SAI CỦA CƠ QUAN CẤP',\n", "       'CAP DIEU CHINH LẦN 1 - THAY ĐỔI SỐ CHỨNG CHỈ HÀNH NGHỀ DƯỢC',\n", "       'CAP DIEU CHINH LẦN 1 - LỖI SAI CỦA CƠ QUAN CẤP'], dtype=object)"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["df['thu tuc'].unique()"]}, {"cell_type": "code", "execution_count": 13, "id": "484fe673", "metadata": {}, "outputs": [], "source": ["df_gdp=pd.read_csv('/home/<USER>/Dropbox/hnd/csv_source/gdp.csv',dtype=str)"]}, {"cell_type": "code", "execution_count": 14, "id": "c4310a55", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_33809/2063860647.py:1: UserWarning: Parsing dates in %d/%m/%Y format when dayfirst=False (the default) was specified. Pass `dayfirst=True` or specify a format to silence this warning.\n", "  df_gdp['ngay qd']=pd.to_datetime(df_gdp['ngay qd'])\n"]}], "source": ["\n", "df_gdp['ngay qd']=pd.to_datetime(df_gdp['ngay qd'])"]}, {"cell_type": "code", "execution_count": 15, "id": "5747f3f9", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_33809/3748700562.py:1: FutureWarning: A value is trying to be set on a copy of a DataFrame or Series through chained assignment using an inplace method.\n", "The behavior will change in pandas 3.0. This inplace method will never work because the intermediate object on which we are setting values always behaves as a copy.\n", "\n", "For example, when doing 'df[col].method(value, inplace=True)', try using 'df.method({col: value}, inplace=True)' or df[col] = df[col].method(value) instead, to perform the operation inplace on the original object.\n", "\n", "\n", "  df_gdp['ngay qd'].fillna(df_gdp['ngay qd'].min(),inplace=True)\n"]}], "source": ["df_gdp['ngay qd'].fillna(df_gdp['ngay qd'].min(),inplace=True)\n", "df_gdp=df_gdp[df_gdp['ngay qd']<=pd.to_datetime('2024-10-10')]"]}, {"cell_type": "code", "execution_count": 16, "id": "719ead14", "metadata": {}, "outputs": [], "source": ["df_gdp['ten cong ty']=df_gdp['ten cong ty'].str.upper()\n", "df_gdp = df_gdp.sort_values('ngay qd', ascending=True).drop_duplicates(subset='ten cong ty', keep='first')"]}, {"cell_type": "code", "execution_count": 17, "id": "11e293e9", "metadata": {}, "outputs": [], "source": ["import god_class"]}, {"cell_type": "code", "execution_count": 18, "id": "ea6245ee", "metadata": {}, "outputs": [], "source": ["god_class.insert_stt(df_gdp)\n"]}, {"cell_type": "code", "execution_count": 19, "id": "3e581d92", "metadata": {}, "outputs": [], "source": ["df_gdp.sort_values(by='loai hinh',inplace=True)"]}, {"cell_type": "code", "execution_count": 20, "id": "c3b5d56b", "metadata": {}, "outputs": [], "source": ["df_gdp['ten cong ty']=df_gdp['ten cong ty'].str.upper()\n", "df_gdp['ten nguoi ptcm']=df_gdp['ten nguoi ptcm'].str.title()\n", "df_gdp['ngay qd']=df_gdp['ngay qd'].dt.strftime('%d/%m/%Y')\n", "df_gdp=df_gdp[['ten cong ty','ten nguoi ptcm','tru so','pham vi kd','ngay qd','so dt chu hs']]\n", "df_gdp.to_excel('ds_co_so_kd_duoc.xlsx',index=False)\n"]}, {"cell_type": "code", "execution_count": null, "id": "63cfc4b0", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "45e9db53", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3f7ed4cf", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "/home/<USER>/python313/bin/python", "name": "/home/<USER>/python313/bin/python"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "i/home/<USER>/python313/bin/python", "version": "3.12.6"}}, "nbformat": 4, "nbformat_minor": 5}