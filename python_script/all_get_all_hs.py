import pandas as pd
import os
import time
from typing import List
import shutil
from playwright.sync_api import Download
from god_class import (
    unidecode,
    send_notification,
    get_weekday,
    update_with_vlookup,
)
from playwright_class import DichVuCong

from loguru import logger


class GetHsDvc(DichVuCong):
    def __init__(self, user):
        super().__init__(user)
        self.user = user
        self.df_name = pd.read_csv("name.csv", dtype=str, index_col="ma ho so")

        self.df_cchnd = pd.read_csv("cchnd.csv", dtype=str, index_col="ma ho so")

    def get_list_mhs_not_down(self) -> List[str]:
        """
        <PERSON><PERSON><PERSON> danh sách mã hồ sơ chưa được tải xuống.

        Returns:
            List[str]: Danh sách các mã hồ sơ chưa tải về
        """
        list_mhs_100_last = self.get_list_last_100_mhs_can_down()
        filehs_path = "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pdf_documents/filehs"

        # <PERSON><PERSON><PERSON> danh sách các thư mục đã có trong filehs
        downloaded_folders = []
        if os.path.exists(filehs_path):
            downloaded_folders = [
                name
                for name in os.listdir(filehs_path)
                if os.path.isdir(os.path.join(filehs_path, name))
            ]

        # Trích xuất mã hồ sơ từ tên thư mục (phần trước dấu -- đầu tiên)
        downloaded_ma_hs = []
        for folder_name in downloaded_folders:
            if "--" in folder_name:
                ma_hs = folder_name.split("--")[0]
                downloaded_ma_hs.append(ma_hs)
                if ma_hs in list_mhs_100_last:
                    shutil.rmtree(os.path.join(filehs_path, folder_name))

        logger.info(
            f"Đã tìm thấy {len(downloaded_ma_hs)} hồ sơ đã tải: {downloaded_ma_hs}"
        )

        # Lấy danh sách tất cả mã hồ sơ cần xử lý từ dataframe
        all_ma_hs = self.df_name.index.tolist()

        # Tìm những mã hồ sơ chưa được tải
        not_downloaded = [ma_hs for ma_hs in all_ma_hs if ma_hs not in downloaded_ma_hs]

        logger.info(f"Còn {len(not_downloaded)} hồ sơ chưa tải: {not_downloaded}")

        return not_downloaded

    def get_list_last_100_mhs_can_down(self) -> List[str]:
        """
        Lấy danh sách 100 mã hồ sơ cuối cùng trong dataframe.

        Returns:
            List[str]: Danh sách 100 mã hồ sơ cuối cùng
        """
        return self.df_cchnd.index.tolist()[-100:]

    def create_dataframe(self, rows):
        df = pd.DataFrame(
            rows,
            columns=[
                "ma ho so",
                "ma thu tuc",
                "ten nguoi ptcm",
                "cmnd",
                "ngay sinh",
                "doi tuong",
                "trang thai",
                "dia chi thuong tru",
                "so dt chu hs",
                "ngay tiep nhan",
                "bo sung",
                "han",
            ],
        )
        return df

    def download_hs(self, i, ma_ho_so, ten_thu_tuc, ten_nguoi_ptcm):
        self.page.locator("mat-row").nth(i).locator("mat-icon.mat-icon").click()

        # Danh sách để lưu tất cả các downloads
        downloads: List[Download] = []

        def handle_download(download: Download) -> None:
            downloads.append(download)

        # Đăng ký event listener để bắt tất cả downloads
        self.page.on("download", handle_download)

        # Click để tải file
        self.click_by_xpath("//span[contains(text(),'Tải văn bản của hồ sơ')]")

        # Đợi một chút để tất cả downloads bắt đầu
        self.page.wait_for_timeout(2000)

        # Đợi tất cả downloads hoàn thành và lưu
        #
        folder_name = f"{ma_ho_so}--{ten_thu_tuc}--{unidecode.unidecode(ten_nguoi_ptcm).lower().strip()}"
        if not os.path.exists(
            f"/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pdf_documents/filehs/{folder_name}"
        ):
            os.makedirs(
                f"/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pdf_documents/filehs/{folder_name}"
            )

        for idx, download in enumerate(downloads):
            try:
                # Tạo tên file duy nhất cho mỗi download
                filename = f"hoso_{ma_ho_so}_{idx + 1}.pdf"
                download_path = f"/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pdf_documents/filehs/{folder_name}/{filename}"
                download.save_as(download_path)
                logger.info(f"Đã lưu file: {filename}")
            except Exception as e:
                logger.error(f"Lỗi khi lưu file {idx + 1}: {e}")

        # Xóa event listener sau khi hoàn thành
        self.page.remove_listener("download", handle_download)
        downloads.clear()

    def get_hs_dataframe(self):
        self.page.locator(
            "//span[@class='ng-star-inserted'][contains(text(),'Xử lý hồ sơ')]"
        ).click()
        self.go_to_trangthai("Xử lý hồ sơ")
        self.save_state(self.user)
        self.expand_ds()
        rows = []
        df_thu_tuc = pd.read_csv(
            "convert_thu_tuc.csv", index_col="ma thu tuc", dtype=str
        )

        for i in range(self.page.locator("mat-row").count()):
            base_part = self.page.locator("mat-row").nth(i)
            ma_thu_tuc = self.page.locator("a.cell_code").nth(i).text_content()
            ten_thu_tuc = (
                base_part.locator("span.ng-star-inserted").nth(0).text_content()
            )
            ten_thu_tuc = df_thu_tuc.loc[ma_thu_tuc, "ten thu tuc"]
            ten_nguoi = base_part.locator("span.ng-star-inserted").nth(1).text_content()
            thoi_gian_con = (
                base_part.locator("span.ng-star-inserted").nth(2).text_content()
            )
            han_cuoi = (
                (base_part.locator("span.ng-star-inserted").nth(5).text_content())
                if "giây"
                not in base_part.locator("span.ng-star-inserted").nth(5).text_content()
                else base_part.locator("span.ng-star-inserted").nth(6).text_content()
            )
            ma_ho_so = (
                self.page.locator("mat-row")
                .filter(has_text="H44")
                .nth(i)
                .text_content()
                .split("1.")[0]
                .split(" ")[-1]
            )
            # Click để mở trong tab mới (Ctrl+Click)
            self.page.locator(f"//a[normalize-space()='{ma_ho_so}']").click()
            time.sleep(3)
            ten_nguoi_ptcm = self.page.locator(
                'input[name="data[ownerFullname]"]'
            ).input_value()
            ngay_sinh = (
                self.page.locator("input.form-control.input").nth(1).input_value()
            )
            sdt = self.page.locator(
                'input[name="data[ownerPhoneNumber]"]'
            ).input_value()
            cmnd = self.page.locator(
                'input[name="data[ownerIdentityNumber]"]'
            ).input_value()
            info = self.page.locator(
                'div.choices__item.choices__item--selectable[data-id="11"]'
            )
            gioi_tinh = info.nth(0).text_content().replace("Remove item", "")
            dc_thon = self.page.locator('input[name="data[address]"]').input_value()
            if info.count() == 3:
                tinh = info.nth(1).text_content()
                dia_chi_thuong_tru = dc_thon + ", " + tinh

            elif info.count() == 4:
                tinh = info.nth(1).text_content()
                dia_chi_thuong_tru = (
                    dc_thon + ", " + info.nth(3).text_content() + ", " + tinh
                )
            elif info.count() == 5:
                tinh = info.nth(1).text_content()
                dia_chi_thuong_tru = (
                    dc_thon + ", " + info.nth(4).text_content() + ", " + tinh
                )
            else:
                dia_chi_thuong_tru = ""

            dia_chi_thuong_tru = (
                dia_chi_thuong_tru.replace("Remove item", "")
                .replace("Xã", "xã")
                .replace("Phường", "phường")
                .replace("Tỉnh", "tỉnh")
            )
            ngay_tiep_nhan = (
                self.page.locator("div.cardContent")
                .filter(has_text="Ngày tiếp nhận")
                .text_content()
                .split("rotate")[0]
                .split("\t")[-1]
                .split("move")[0]
                .strip()
            )

            # Click nút back để quay lại trang trước

            row = [
                ma_thu_tuc,
                ten_thu_tuc,
                ma_ho_so,
                ten_nguoi,
                ten_nguoi_ptcm,
                ngay_sinh,
                sdt,
                cmnd,
                dia_chi_thuong_tru,
                gioi_tinh,
                ngay_tiep_nhan,
                thoi_gian_con,
                han_cuoi,
            ]
            rows.append(row)

            self.page.go_back()
            list_all_not_down = (
                self.get_list_mhs_not_down() + self.get_list_last_100_mhs_can_down()
            )
            if ma_ho_so not in list_all_not_down:
                self.download_hs(i, ma_ho_so, ten_thu_tuc, ten_nguoi_ptcm)

        df = pd.DataFrame(
            rows,
            columns=[
                "ma thu tuc",
                "ten thu tuc",
                "ma ho so",
                "ten nguoi nop hs",
                "ten nguoi ptcm",
                "ngay sinh",
                "so dt chu hs",
                "cmnd",
                "dia chi thuong tru",
                "gioi tinh",
                "ngay tiep nhan",
                "thoi gian con",
                "han hs",
            ],
        )
        df.to_csv("name.csv", index=False)

    def process_dataframe(self, df):
        df_thutuc = pd.read_csv("thu_tuc_duoc.csv", dtype=str)
        update_with_vlookup(df, df_thutuc, "ma thu tuc", "TÊN TẮT", "thu tuc")
        df = pd.merge(df, self.df_name, on="ma ho so", how="left", suffixes=("", "_y"))

        list_col_update = ["ten nguoi ptcm", "cmnd", "ngay sinh", "doi tuong"]
        for col in list_col_update:
            mask = df[col].fillna("").astype(str).str.len() > 1
            df.loc[mask, f"{col}_y"] = df.loc[mask, col]
            df.drop(columns=[col], inplace=True)
            df.rename(columns={f"{col}_y": col}, inplace=True)

        df["han"] = pd.to_datetime(df["han"], format="%d/%m/%Y %H:%M:%S")
        df["han back_up"] = df["han"]
        df["han"] = df["han"] - pd.Timedelta(days=3)
        df.loc[df["han"].dt.dayofweek.isin([4, 5, 6]), "han"] -= pd.Timedelta(days=2)
        # Sắp xếp theo da nhan ('' lên trên, '1' xuống dưới), sau đó đến bo sung và han
        df = df.sort_values(
            by=["da nhan", "bo sung", "han"],
            ascending=[True, False, True],
            na_position="first",
        )
        df["han"] = df["han"].dt.strftime("%d/%m/%Y %H:%M:%S")
        df["han back_up"] = df["han back_up"].dt.strftime("%d/%m/%Y %H:%M:%S")
        df["thu"] = df["han"].apply(get_weekday)
        df = df[
            [
                "ma ho so",
                "ten qt-nt",
                "ten nguoi ptcm",
                "bo sung",
                "thu tuc",
                "han",
                "thu",
                "trang thai",
                "ngay tiep nhan",
                "dia chi co so",
                "so dt chu hs",
                "van de hs",
                "ngay td",
                "ngay sinh",
                "noi nhan",
                "so cchnd",
                "noi cap cchnd",
                "ngay cap cchnd",
                "trinh do cm",
                "trinh do tat",
                "loai hinh",
                "doi tuong",
                "gioi tinh",
                "cmnd",
                "noi cap cmnd",
                "ngay cap cmnd",
                "co nhan vien khong",
                "noi tot nghiep",
                "ngay tot nghiep",
                "Có truc thuoc không",
                "co quan chu quan",
                "dia chi co quan chu quan",
                "dang ky pham vi",
                "cong ty xac nhan",
                "noi dung thuc hanh",
                "ngay bat dau th",
                "ngay ket thuc th",
                "so cchnd cu",
                "ngay cchnd cu",
                "pham vi hanh nghe cu",
                "noi dung dieu chinh",
                "da nhan",
                "CHỜ TRẢ",
                "da di",
                "dia chi thuong tru",
                "ma thu tuc",
                "han back_up",
            ]
        ]
        df["dia chi thuong tru"] = (
            df["dia chi thuong tru"]
            .str.replace("Xã", "xã")
            .str.replace("Huyện", "huyện")
            .str.replace("Tỉnh", "tỉnh")
            .str.replace("Thôn", "thôn")
            .str.replace("Phường", "phường")
            .str.replace("Thị trấn", "thị trấn")
            .str.replace("Thành phố", "thành phố")
        )
        return df

    def creat_df(self, rows, name="temp.csv"):
        df = self.create_dataframe(rows)
        df = self.process_dataframe(df)
        df.to_csv(name, index=False)

    @logger.catch
    def run(self):
        send_notification("Bắt đầu script tải hồ sơ")
        super().setup(headless=False)
        self.login_dvc("cv.pnvd.syt", "Phutho@01072025")

        self.get_hs_dataframe()
        self.cleanup()


dvc = GetHsDvc("son")
dvc.get_list_mhs_not_down()
dvc.run()
