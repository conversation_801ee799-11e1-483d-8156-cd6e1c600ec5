import datetime
import os
import sys

import demjson3 as demjson
import requests
from god_class import TelegramSend

os.chdir("/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pickle_file")

telegram = TelegramSend('hnd')

url = "https://qlvb-api.vinhphuc.gov.vn/api/van-ban-di/danh-sach-van-ban-di-cho-phat-hanh-cua-chuyen-vien"

payload = 'co_tep_tin=-1&ma_can_bo=27683&ma_ctcb_cv=21365&ma_don_vi_quan_tri=1187&ma_loai_ttdh=0&nam=0&ngay_tao_den_ngay=13%2F09%2F2030&ngay_tao_tu_ngay=13%2F08%2F2023&page=1&size=20&trang_thai_ttdh_gui=-1&trang_thai_xu_ly=3'
headers = {
  'Content-Type': 'application/x-www-form-urlencoded',
  'Authorization': 'Bearer acKKZDmXqpUmn7D3vuzwk88RN909S7Soc5YlXQhyV6I9lU6UNYaTHtnVEhzxoo6Pul1K2NroQeS6584FZDucIYETRjjn70alDIbOUQ4V3zt8JarwIvOhqZ+z96uFvFuZjsu03k3l5Ou0DhYlEmlQ/15duefP7Ozfa3bfIo3bASLX3cOwnkpUlyzlxKJ1Dtv93fZ654LGf1En/NR0nW7uuYcPT7ic/DdhkiE08Us6bbSjqfTbHGdR8fC8Nza/I52dVn4RLZDAGfVFhFgZS6qw2j2VTpQ1hpMelekgyrg23fFO2qhb81RllfgYPTifzG9j',
}
# TODO 2. nếu bị lỗi thì thoát
response = requests.request("POST", url, headers=headers, data=payload)
res=demjson.decode(response.text)

if "Không tìm thấy dữ liệu" in res["message"]:
    telegram.send_message_normal("Không có văn bản chờ phát hành")
    sys.exit()
report=''
count=0
for i in range(len(res["data"])):
    count+=1
    trich_yeu=res["data"][i]["trich_yeu"]
    so_ky_hieu=res["data"][i]["so_ky_hieu"]
    ngay_ban_hanh=res["data"][i]["ngay_ban_hanh"].split(' ')[0]
    report+=f"{count}. NGAY {ngay_ban_hanh}: {so_ky_hieu}\n{trich_yeu}\n\n"
report='Chờ phát hành\n\n'+report
telegram.send_message_warp(report)
    

