{"cells": [{"cell_type": "code", "execution_count": 17, "id": "cd682581", "metadata": {}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": 18, "id": "eb5f5833", "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "\n", "os.chdir(\"/home/<USER>/Dropbox/hnd/csv_source\")"]}, {"cell_type": "code", "execution_count": 19, "id": "8690ddf3", "metadata": {}, "outputs": [], "source": ["df=pd.read_csv('du_lieu_gpp_all.csv')"]}, {"cell_type": "code", "execution_count": 20, "id": "ef1f5630", "metadata": {}, "outputs": [], "source": ["mask_tt=df['co quan chu quan'].notnull() & df['co quan chu quan']!='0'"]}, {"cell_type": "code", "execution_count": 21, "id": "da0bd094", "metadata": {}, "outputs": [], "source": ["df_tt=df[mask_tt]"]}, {"cell_type": "code", "execution_count": 22, "id": "f2971f45", "metadata": {}, "outputs": [], "source": ["df_tt.to_csv('du_lieu_tt.csv', index=False)\n"]}, {"cell_type": "code", "execution_count": null, "id": "27218597", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "python313", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.1"}}, "nbformat": 4, "nbformat_minor": 5}