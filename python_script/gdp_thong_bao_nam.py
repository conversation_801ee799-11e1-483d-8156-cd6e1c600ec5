import os
from datetime import datetime

from god_class import (
    dataframe_to_latex,
    TextProcess,
    get_current_date,
    insert_stt,
)
from csv_load_and_export import CsvLoaderFactory


os.chdir("/home/<USER>/Dropbox/hnd/csv_source")
df_gdp = CsvLoaderFactory.create_datetime_loader(
    ["ngay het han gdp", "ngay qd"]
).load_df("gdp")
values = {}

now = datetime.now()
mask1 = df_gdp["ngay het han gdp"] >= datetime(2025, 1, 1)
mask2 = df_gdp["ngay het han gdp"] <= datetime(2025, 12, 31)
mask3 = ~df_gdp["tinh trang"].isin(["pause", "stop"])


filtered_df = df_gdp.loc[mask1 & mask2 & mask3]
filtered_df["ten cong ty"] = filtered_df["ten cong ty"].str.upper()
filtered_df = filtered_df.loc[filtered_df.groupby("ten cong ty")["ngay qd"].idxmax()]

filtered_df = filtered_df.sort_values(by="ngay het han gdp")
filtered_df["ngay het han gdp"] = filtered_df["ngay het han gdp"].dt.strftime(
    "%d/%m/%Y"
)

insert_stt(filtered_df)
filtered_df["so cchnd"] = filtered_df["so cchnd"].apply(
    lambda x: x + "/CCHND-" + "\par SYT-VP" if "-" not in x else x
)
filtered_df["ten cong ty"] = (
    filtered_df["ten cong ty"].str.title().str.replace("Thuốc", "thuốc")
)
filtered_df["so gdp"] = filtered_df["so gdp"] + "/GDP"
filtered_df["ten nguoi ptcm"] = filtered_df["ten nguoi ptcm"].str.title()


filtered_df = filtered_df[
    [
        "stt",
        "ten cong ty",
        "tru so",
        "ten nguoi ptcm",
        "so cchnd",
        "so gdp",
        "ngay het han gdp",
    ]
]

values["ngay"], values["thang"], values["nam"], values["today"] = get_current_date()
values["so_cs"] = str(filtered_df.shape[0]).zfill(2)
values["clip"] = dataframe_to_latex(filtered_df)
text = TextProcess("gdp_thong_bao_den_han")
text.format_text(values)
tieude = "THÔNG BÁO Danh sách cơ sở bán buôn thuốc đến hạn đánh giá định kỳ việc duy trì đáp ứng Thực hành tốt phân phối thuốc năm 2025"
text.auto_day_van_ban(tieude, "TB", "0")
