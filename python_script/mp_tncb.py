import os
from datetime import datetime
import subprocess
import sys

import pandas as pd
from god_class import compile_latex, format_name, get_current_date, close_app
from god_class import show_message
from top_most_get_text import input_dialog

os.chdir("/home/<USER>/Dropbox/hnd/csv_source")


def run_main(mhs):
    congty_dict = {
        "logistics": "Công ty TNHH Đầu tư và Phát triển Logistics",
        "lavitec": "Công ty Cổ phần Công nghệ Lavitec",
        "ma ra do": "Công ty TNHH Mỹ phẩm Marado",
        "marado": "Công ty TNHH Mỹ phẩm Marado",
        "atp": "Công ty TNHH ATP Pacific Việt Nam",
        "ông bụt": "Công ty cổ phần Đông Nam Dược Ông Bụt",
        "gia truyền": "Công ty TNHH thuốc gia truyền đông y TD",
        "lê vĩnh yên": "Công ty TNHH một thành viên <PERSON> Hậ<PERSON>",
        "dương đức anh": "Công ty TNHH Sản xuất thương mại xuất nhập khẩu Hóa Mỹ phẩm HKNA",
    }
    df_name = pd.read_csv("name.csv", dtype=str, index_col="ma ho so")
    df_tnmp = pd.read_csv("cap_so_tiep_nhan_mp.csv", dtype=str, index_col="ma ho so")
    df_tnmp = df_tnmp.fillna("")
    cs_dang_ky = df_name.loc[mhs, "ten nguoi ptcm"].lower()
    ten_cong_ty = next(
        (congty_dict[key] for key in congty_dict if key in cs_dang_ky), None
    )
    if not ten_cong_ty:
        show_message("THÔNG BÁO", "TÊN ten nguoi ptcm SAI")
        return
    df_filter = df_tnmp[df_tnmp["CÔNG TY PHÂN PHỐI"] == ten_cong_ty]
    list_ct_sx = df_filter["CÔNG TY SX"].unique().tolist()

    def func1(value):
        defaul_val = {}
        for keys in df_tnmp.columns.tolist():
            if keys == "TÊN NHÃN HÀNG":
                defaul_val[keys] = ""
            elif keys not in [
                "CÔNG TY SX",
                "ma ho so",
                "CÔNG TY PHÂN PHỐI",
                "CÔNG TY PHÂN PHỐI HOA",
                "CÔNG TY SẢN XUẤT HOA",
                "SỐ TIẾP NHẬN",
                "NGÀY CẤP SỐ TIẾP NHẬN",
                "so tn SYT",
            ]:
                defaul_val[keys] = df_filter[keys].iloc[-1]
        value["ma ho so"] = mhs
        alls = [key for key in defaul_val.keys()]
        list_ngays = []
        multi = []
        return list_ngays, {}, multi, defaul_val, alls, value

    def func2(values):
        values["CÔNG TY PHÂN PHỐI"] = ten_cong_ty
        if values["CÔNG TY SX"] is None:
            show_message("Loi", "Chưa chọn công ty phân phối")
            return
        stn = (
            df_tnmp["SỐ TIẾP NHẬN"].iloc[-1]
            if df_tnmp["SỐ TIẾP NHẬN"].iloc[-1] != ""
            else df_tnmp["SỐ TIẾP NHẬN"].iloc[-2]
        )
        stnsyt = (
            df_tnmp["so tn SYT"].iloc[-1]
            if df_tnmp["so tn SYT"].iloc[-1] != ""
            else df_tnmp["so tn SYT"].iloc[-2]
        )

        lstday = (
            df_tnmp["NGÀY CẤP SỐ TIẾP NHẬN"].iloc[-1]
            if df_tnmp["NGÀY CẤP SỐ TIẾP NHẬN"].iloc[-1] != ""
            else df_tnmp["NGÀY CẤP SỐ TIẾP NHẬN"].iloc[-2]
        )

        thisyear = datetime.today().strftime("%Y")
        if mhs not in df_tnmp.index:
            if thisyear == lstday.split("/")[2]:
                stnnew = (
                    str(int(stn.split("/")[0]) + 1).zfill(2)
                    + "/"
                    + thisyear[-2:]
                    + "/CBMP-VP"
                )

                stnsytnew = str(int(stnsyt.split("/")[0]) + 1).zfill(2) + "/GTNMP-SYT"
            else:
                stnnew = "01/" + thisyear[-2:] + "/CBMP-VP"
                stnsytnew = "01/GTNMP-SYT"
        else:
            stnnew = df_tnmp.at[mhs, "SỐ TIẾP NHẬN"]
            stnsytnew = df_tnmp.at[mhs, "so tn SYT"]

        df_name.loc[mhs, "CHỜ TRẢ"] = "1"
        df_name.loc[mhs, "da nhan"] = "1"
        df_name.loc[mhs, "ten qt-nt"] = values["TÊN NHÃN HÀNG"]
        df_name.loc[mhs, "ten nguoi ptcm"] = values["ten san pham"]
        df_name.to_csv("name.csv", index_label="ma ho so")
        values["SỐ TIẾP NHẬN"] = stnnew
        values["so tn SYT"] = stnsytnew
        # TODO 3. 2 THÊM GIÁ TRỊ THIẾU
        values["stt"] = len(df_tnmp) + 1

        ngay, thang, nam, values["NGÀY CẤP SỐ TIẾP NHẬN"] = get_current_date()
        values["ma ho so"] = mhs

        addition = f"Sở Y tế yêu cầu {values['CÔNG TY SX']} áp dụng hệ thống quản lý chất lượng theo bộ nguyên tắc, tiêu chuẩn “Thực hành tốt sản xuất mỹ phẩm” (CGMP-ASEAN) trong quá trình hoạt động; chịu trách nhiệm trước pháp luật về sản phẩm mỹ phẩm do Công ty sản xuất, đảm bảo sản phẩm có chất lượng, an toàn, hiệu quả./.\par"

        # TODO ghi vào file excel.
        namhan = str(int(nam) + 5)
        ngayhantn = "/".join([ngay, thang, namhan])
        if values["TÊN NHÃN HÀNG"] not in values["ten san pham"]:
            namesp = (
                values["ten san pham"].upper()
                + " "
                + values["TÊN NHÃN HÀNG"].upper().replace("Я", r"\reflectbox{R}")
            )
        else:
            namesp = values["ten san pham"].upper()

        def kiem_tra_trung_lap(df, ten_nhan_hang, ten_san_pham, ten_cong_ty_sx):
            mask = (
                (df["TÊN NHÃN HÀNG"].str.upper() == ten_nhan_hang.upper())
                & (df["ten san pham"].str.upper() == ten_san_pham.upper())
                & (df["CÔNG TY SX"] == ten_cong_ty_sx)
                & (df.index != mhs)
            )
            trung_lap = df[mask]
            if not trung_lap.empty:
                show_message(
                    "Cảnh báo",
                    f"Sản phẩm {ten_san_pham}, nhãn hàng {ten_nhan_hang} đã được cấp số tiếp nhận cho công ty {ten_cong_ty_sx}\n ngày cấp {trung_lap['NGÀY CẤP SỐ TIẾP NHẬN'].iloc[0]}gửi ",
                )
                sys.exit()

        kiem_tra_trung_lap(
            df_tnmp,
            values["TÊN NHÃN HÀNG"],
            values["ten san pham"],
            values["CÔNG TY SX"],
        )
        df_tnmp.loc[mhs] = values
        df_tnmp.to_csv("cap_so_tiep_nhan_mp.csv", index_label="ma ho so")

        def latex_micro_name(tenct):
            if values[tenct] == "Công ty TNHH Đầu tư và Phát triển Logistics":
                m = r"\textls[-20]{Công ty TNHH Đầu tư và Phát triển Logistics}"
            else:
                m = values[tenct]
            return m

        # TODO CĂN CHỈNH LATEX.
        values["CÔNG TY PHÂN PHỐI HOA"] = latex_micro_name("CÔNG TY PHÂN PHỐI")
        values["CÔNG TY SX HOA"] = latex_micro_name("CÔNG TY SX")
        if values["CÔNG TY SX"] == values["CÔNG TY PHÂN PHỐI"]:
            noi_nhan = rf"\fontsize{{11pt}}{{13pt}}\selectfont - {values['CÔNG TY PHÂN PHỐI']};\\"
        else:
            noi_nhan = rf"""\fontsize{{11pt}}{{13pt}}\selectfont - {values["CÔNG TY SX"]};\\
            \fontsize{{11pt}}{{13pt}}\selectfont - {values["CÔNG TY PHÂN PHỐI"]};\\"""

        # TODO TẠO FILE LATEX
        text = rf"""
\documentclass{{article}}
\usepackage[T5]{{fontenc}}
\usepackage{{graphicx}}
\nonstopmode
\usepackage[fontsize=14pt]{{scrextend}}
\usepackage[a4paper,vmargin=2cm,right=2cm,left=3cm,]{{geometry}}
\usepackage{{mathptmx}}
\usepackage{{ulem}}
\usepackage{{microtype}}
\hyphenpenalty=10000
\usepackage{{pdflscape}}
\renewcommand{{\ULdepth}}{{7pt}}
\renewcommand{{\ULthickness}}{{0.5pt}}
\usepackage{{setspace}}
\setlength{{\parskip}}{{6pt}}
\usepackage{{tabularray}}
\usepackage{{indentfirst}}
\setlength{{\parindent}}{{1.27cm}}
\usepackage{{fancyhdr}}
\fancyhf{{}}
\fancyhead[C]{{\small\thepage}}
\renewcommand{{\headrulewidth}}{{0pt}}
\pagestyle{{fancy}}
\thispagestyle{{empty}}
\setstretch{{1}}
\begin{{document}}

    \noindent
    \begin{{minipage}}{{\textwidth}}
\begin{{tblr}}{{width=1\linewidth,            colspec={{X[1,c] X[1.9,c]}},
            colsep=1pt,
            rowsep=-3pt,
            row{{2}} = {{font=\bfseries}}}}
        \fontsize{{13pt}}{{0pt}}\selectfont UBND TỈNH VĨNH PHÚC & \fontsize{{13pt}}{{0pt}}\selectfont \textbf{{CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM}}\\
        {{\fontsize{{13pt}}{{0pt}}\selectfont SỞ Y TẾ\\[3pt]
            \rule[0.6\baselineskip]{{.14\linewidth}}{{.5pt}}}} & \fontsize{{14pt}}{{0pt}}\selectfont \uline{{Độc lập - Tự do - Hạnh phúc}}\\[14pt]
            
        \fontsize{{14pt}}{{0pt}}\selectfont Số: {values["so tn SYT"]} & \fontsize{{14pt}}{{0pt}}\selectfont \textit{{Phú Thọ, ngày {ngay} tháng {thang} năm {nam}}}\\
    \end{{tblr}}
\end{{minipage}}

\vspace{{0.5\baselineskip}}

    \setstretch{{1}}
    \begin{{center}} \bfseries
        GIẤY TIẾP NHẬN\\
        PHIẾU CÔNG BỐ SẢN PHẨM MỸ PHẨM
    \end{{center}}

\vspace{{0.5\baselineskip}}



    Căn cứ Thông tư số 06/2011/TT-BYT ngày 25/01/2011 của Bộ trưởng Bộ Y tế quy định về quản lý mỹ phẩm;\par
    Căn cứ Quyết định số 50/2022/QĐ-UBND ngày 23/12/2022 của UBND tỉnh Vĩnh Phúc quy định vị trí, chức năng, nhiệm vụ, quyền hạn và cơ cấu tổ chức của Sở Y tế tỉnh Phú Thọ;
\par	
    Căn cứ Hồ sơ đề nghị cấp số tiếp nhận Phiếu công bố sản phẩm mỹ phẩm sản xuất trong nước của {values["CÔNG TY PHÂN PHỐI"]}, mã hồ sơ: {mhs},\par
    Sở Y tế đồng ý cấp số tiếp nhận Phiếu công bố sản phẩm mỹ phẩm với sản phẩm {namesp} do {values["CÔNG TY PHÂN PHỐI"]} đứng tên trên hồ sơ công bố:\par 


    \textbf{{1. Thông tin về sản phẩm trong hồ sơ công bố:}}\par
    - Tên nhãn hàng: \textbf{{{values["TÊN NHÃN HÀNG"].replace("Я", r"\reflectbox{R}").upper()}}} \par
    - Tên sản phẩm: \textbf{{{values["ten san pham"].upper()}}} \par

    - Thông tin của Công ty sản xuất:\par
    + Tên công ty: \textbf{{{values["CÔNG TY SX HOA"]}}}\par
    + Địa chỉ: \textbf{{{values["dia chi CÔNG TY SX"]}.}}\par
    + Số điện thoại: \textbf{{{values["SĐT CÔNG TY SX"]}}}\par
    - Thông tin của Công ty chịu trách nhiệm đưa sản phẩm ra thị trường:\par 
    + Tên công ty: \textbf{{{values["CÔNG TY PHÂN PHỐI HOA"]}}}\par
    + Địa chỉ: \textbf{{{values["dia chi CÔNG TY PHÂN PHỐI"]}.}}\par
    + Số giấy phép kinh doanh: \textbf{{{values["SỐ GPKD CÔNG TY PHÂN PHỐI"]}}}\par
    \textbf{{2. Số tiếp nhận Phiếu công bố sản phẩm mỹ phẩm:}}\par
    - Số tiếp nhận: \textbf{{{values["SỐ TIẾP NHẬN"]}}}\par
    - Ngày cấp số tiếp nhận: \textbf{{{values["NGÀY CẤP SỐ TIẾP NHẬN"]}}}\par
    - Số tiếp nhận Phiếu công bố sản phẩm mỹ phẩm có giá trị đến ngày: \textbf{{{ngayhantn}}}\par
{addition}

    \setstretch{{1}}
    \noindent
    \begin{{minipage}}[t]{{0.5\textwidth}}
        \singlespacing
        \fontsize{{12pt}}{{14pt}}\selectfont \textbf{{\textit{{Nơi nhận:}}}}\\
        {noi_nhan}
        \fontsize{{11pt}}{{13pt}}\selectfont - Giám đốc, các PGĐ Sở;\\
        \fontsize{{11pt}}{{13pt}}\selectfont - TT KSBT (đăng tải Website Sở);\\
        \fontsize{{11pt}}{{13pt}}\selectfont - Lưu: VT, NVD.\\
\end{{minipage}}
\begin{{minipage}}[t]{{0.5\textwidth}}\singlespacing
\begin{{center}}
\textbf{{KT. GIÁM ĐỐC}}\\
\textbf{{PHÓ GIÁM ĐỐC}}\\
\vspace{{3cm}}
\textbf{{Nguyễn Đắc Ca}}\\
\end{{center}}
\end{{minipage}}
\end{{document}}
        """
        names = format_name(values["ten san pham"] + " " + values["TÊN NHÃN HÀNG"])
        compile_latex(text, names, mhs, okular=False)
        subprocess.Popen(["pkill", "-f", "okular"])

    list_include = [
        "CÔNG TY PHÂN PHỐI",
        "TÊN NHÃN HÀNG",
        "THANH_PHAN_HC",
        "TEN_SAN_PHAM_CO_HIEU_NHAM",
        "MUC_DICH_SU_DUNG?",
        "ten san pham",
        "CÔNG TY SX",
        "dia chi CÔNG TY SX",
        "SĐT CÔNG TY SX",
        "dia chi CÔNG TY PHÂN PHỐI",
        "SỐ GPKD CÔNG TY PHÂN PHỐI",
    ]

    if mhs in df_tnmp.index:
        default_val = {}
        for i in df_tnmp.columns.tolist():
            if i not in ["ma ho so", "CÔNG TY PHÂN PHỐI HOA", "CÔNG TY SẢN XUẤT HOA"]:
                default_val[i] = df_tnmp.loc[mhs, i]
        upper2 = [
            "TÊN NHÃN HÀNG",
            "ten san pham",
        ]
        multiline2 = ""
        list_ngay2 = ["NGÀY CẤP SỐ TIẾP NHẬN"]
        list_combo = []
        dict_widget = []
        list_phone = []
        list_title = []
        list_radio = {}

        one_index(
            df_tnmp,
            list_include,
            mhs,
            "ma ho so",
            list_radio,
            list_combo,
            dict_widget,
            upper2,
            list_title,
            default_val,
            multiline2,
            list_ngay2,
            list_phone,
            "NHẬP THÔNG TIN ten nguoi ptcm",
            func2,
            0,
            100,
            600,
            500,
        )
    else:
        list_upper = []
        multiline = ""
        list_ngay = []
        list_phone = []
        list_title = []
        list_radio = {}
        default_values = (
            df_tnmp.loc[df_tnmp["CÔNG TY PHÂN PHỐI"] == ten_cong_ty].iloc[-1].to_dict()
        )
        if len(list_ct_sx) == 1:
            dict_widget = {}
            default_values["CÔNG TY SX"] = list_ct_sx[0]
            one_index(
                df_tnmp,
                list_include,
                mhs,
                "ma ho so",
                list_radio,
                {},
                dict_widget,
                list_upper,
                list_title,
                default_values,
                multiline,
                list_ngay,
                list_phone,
                "NHẬP THÔNG TIN ten nguoi ptcm",
                func2,
                0,
                0,
                600,
                600,
            )
        else:
            dict_widget = {"CÔNG TY PHÂN PHỐI": list_ct_sx}
            include = list(dict_widget.keys())
            one_index_multi(
                df_tnmp,
                include,
                ten_cong_ty,
                list_radio,
                {},
                dict_widget,
                list_upper,
                list_title,
                default_values,
                multiline,
                list_ngay,
                list_phone,
                "NHẬP THÔNG TIN",
                func1,
                func2,
                0,
                0,
                500,
                400,
                500,
                900,
            )


if __name__ == "__main__":
    input_mhs = input_dialog("NHẬP", "NHẬP ma ho so", "")
    if input_mhs:
        run_main(input_mhs)
