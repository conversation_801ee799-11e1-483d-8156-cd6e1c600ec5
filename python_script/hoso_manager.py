import os
import pandas as pd
import shutil
from god_class import FolderManager


class HoSoManager:
    def __init__(
        self,
        base_path="/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pdf_documents/filehs",
    ):
        self.base_path = base_path
        self.hoso_path = os.path.join(base_path, "hoso")
        self.df_name = pd.read_csv("name.csv", dtype=str, index_col="ma ho so")
        self._initialize_folders()
        self.list_mhs_not_download = self._get_not_download_list()

    def _initialize_folders(self):
        """Khởi tạo và dọn dẹp thư mục"""
        if os.path.exists(self.hoso_path) and os.path.isdir(self.hoso_path):
            shutil.rmtree(self.hoso_path)

        self.folder = FolderManager(self.base_path)
        self._update_df_name()

    def _update_df_name(self):
        """<PERSON><PERSON><PERSON> nhật df_name vớ<PERSON> c<PERSON><PERSON> <PERSON><PERSON> sơ đã tải về"""
        dict_mhs_path = self.folder.get_dict_mhs_path()
        list_ma_hs_da_tai = list(dict_mhs_path.keys())

        for mhs in list_ma_hs_da_tai:
            if mhs not in self.df_name.index:
                # lay ra thong tin ten nguoi ptcm va ten thu tuc
                path_parts = dict_mhs_path[mhs].split("--")
                self.df_name.loc[mhs, "ten nguoi ptcm"] = path_parts[-1]
                self.df_name.loc[mhs, "thu tuc"] = path_parts[-2]

    def _get_not_download_list(self):
        """Lấy danh sách hồ sơ không cần tải"""
        list_ma_hs_da_nhan = self.df_name[self.df_name["da nhan"] == "1"].index.tolist()
        dict_mhs_path = self.folder.get_dict_mhs_path()
        list_ma_hs_da_tai = list(dict_mhs_path.keys())
        return list_ma_hs_da_tai + list_ma_hs_da_nhan

    def get_not_download_list(self):
        """Public method để lấy danh sách hồ sơ không cần tải"""
        return self.list_mhs_not_download


if __name__ == "__main__":
    hoso_manager = HoSoManager()
    print(hoso_manager.get_not_download_list())
    print(len(hoso_manager.get_not_download_list()))
