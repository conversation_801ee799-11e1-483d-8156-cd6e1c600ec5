import pynput
import subprocess
import pya<PERSON><PERSON><PERSON>


def on_press(key):
    if key == pynput.keyboard.Key.enter:
        print("Đã nhấn phím Enter!")
        # Thực thi lệnh của bạn ở đây
        execute_command()
        # Dừng listener sau khi thực thi lệnh
        return False


def execute_command():
    subprocess.run(["/home/<USER>/Dropbox/hnd/other_script/switch_eng.sh"])
    # Viết code để thực thi lệnh của bạn ở đây

    # Ví dụ: dvc.run()


def listen_for_enter():
    subprocess.run(["/home/<USER>/Dropbox/hnd/other_script/switch_vn.sh"])
    with pynput.keyboard.Listener(on_press=on_press) as listener:
        listener.join()


# G<PERSON>i hàm để bắt đầu lắng nghe
listen_for_enter()
