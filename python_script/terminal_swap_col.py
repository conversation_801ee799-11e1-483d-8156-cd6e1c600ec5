import os
import sys
import pandas as pd

os.chdir("/home/<USER>/Dropbox/hnd/csv_source")
file_name = sys.argv[1]
col1 = sys.argv[2]
col2 = sys.argv[3]


df = pd.read_csv(file_name, dtype=str)


def swap_columns(df, col1_index, col2_index):
    col1_index -= 1
    col2_index -= 1

    columns = list(df.columns)
    columns[col1_index], columns[col2_index] = columns[col2_index], columns[col1_index]
    df = df[columns]
    df.to_csv(file_name, index=False)


swap_columns(df, int(col1), int(col2))

