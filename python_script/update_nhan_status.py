import pandas as pd
import sys
import os

if __name__ == "__main__":
    if len(sys.argv) > 1:
        selected_path: str = sys.argv[1]
        item: str = os.path.basename(selected_path)

        csv_file_path: str = "/home/<USER>/Dropbox/hnd/csv_source/name.csv"

        try:
            df: pd.DataFrame = pd.read_csv(
                csv_file_path, index_col="ma ho so", dtype=str
            )

            if item in df.index:
                # Dòng này thực hiện một phép so sánh, kết qu<PERSON> (True/False) của nó không được sử dụng
                # và không làm thay đổi nội dung của DataFrame.
                # Nếu bạn muốn *gán* giá trị cho cột 'da nhan', bạn cần dùng dấu '='.
                # Ví dụ: df.loc[item, 'da nhan'] = '0'
                _ = df.loc[item, "da nhan"] == "0"
            else:
                print(
                    f"Lỗi: Mục '{item}' không tìm thấy trong chỉ mục 'ma ho so' của file {csv_file_path}"
                )
                sys.exit(1)

            df.to_csv(csv_file_path, index_label="ma ho so")
            print(f"Đã xử lý mục '{item}'. File CSV '{csv_file_path}' đã được lưu.")

        except FileNotFoundError:
            print(f"Lỗi: Không tìm thấy file CSV tại '{csv_file_path}'")
            sys.exit(1)
        except KeyError as e:
            print(f"Lỗi: Không tìm thấy cột hoặc chỉ mục, chi tiết: {e}")
            sys.exit(1)
        except Exception as e:
            print(f"Đã xảy ra lỗi không xác định: {e}")
            sys.exit(1)
    else:
        print("Lỗi: Không có đường dẫn nào được cung cấp cho script.")
        sys.exit(1)
