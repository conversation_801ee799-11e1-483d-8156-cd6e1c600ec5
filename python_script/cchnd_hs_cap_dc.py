import os
from datetime import datetime, timedelta

import pandas as pd

from all_pickle import load_pickle
from god_class import (
    convert_ngay,
    get_dict_from_index_df,
    close_app,
    send_notification,
    update_df_name_da_nhan,
)
from god_class import show_message
from python312 import run_python_312
from top_most_get_text import input_dialog
from create_snippets import creat_and_get
from solid_string_format import DictStringFormatter

os.chdir("/home/<USER>/Dropbox/hnd/csv_source")


def check_thoi_gian(values, ngay_bat_dau, ngay_ket_thuc):
    if values["trinh do cm"] == "Đại học dược":
        if (
            "Nhà thuốc" in values["pham vi hanh nghe cu"]
            or "buôn" in values["pham vi hanh nghe cu"]
        ):
            if ngay_ket_thuc < max(
                datetime.strptime(convert_ngay(values["ngay cchnd cu"]), "%d/%m/%Y"),
                ngay_bat_dau,
            ) + timedelta(days=730):
                message = "KHÔNG ĐỦ 730 NGÀY THỰC HÀNH:\ntính từ ngày được cấp cchnd cũ hoặc ngày bắt đầu thực hành"
                show_message("THÔNG BÁO", message)
                return message

        elif ngay_ket_thuc < max(
            ngay_bat_dau,
            datetime.strptime(convert_ngay(values["ngay tot nghiep"]), "%d/%m/%Y"),
        ) + timedelta(days=730):
            message = "KHÔNG ĐỦ 730 NGÀY THỰC HÀNH:\ntính từ ngày tốt nghiệp hoặc ngày bắt đầu thực hành"
            show_message("THÔNG BÁO", message)
            return message
    else:
        if "Tủ thuốc" in values["dang ky pham vi"]:
            if ngay_ket_thuc < max(
                ngay_bat_dau,
                datetime.strptime(convert_ngay(values["ngay tot nghiep"]), "%d/%m/%Y"),
            ) + timedelta(days=365):
                message = "KHÔNG ĐỦ 365 NGÀY THỰC HÀNH"
                show_message("THÔNG BÁO", message)
                return message
        elif ngay_ket_thuc < max(
            ngay_bat_dau,
            datetime.strptime(convert_ngay(values["ngay tot nghiep"]), "%d/%m/%Y"),
        ) + timedelta(days=540):
            message = "KHÔNG ĐỦ 540 NGÀY THỰC HÀNH"
            show_message("THÔNG BÁO", message)
        else:
            return None


def dc_cchnd(mhs):
    df_name = pd.read_csv("name.csv", dtype=str, index_col="ma ho so")
    df_cchnd = pd.read_csv("cchnd.csv", dtype=str, index_col="ma ho so")
    sdt = df_name.loc[mhs, "so dt chu hs"]
    dia_chi = df_name.loc[mhs, "dia chi thuong tru"]
    cmnd = df_name.loc[mhs, "cmnd"]
    ngay_sinh = df_name.loc[mhs, "ngay sinh"]
    ten_nguoi_ptcm = df_name.loc[mhs, "ten nguoi ptcm"]
    default_origin = {
        "pham vi hanh nghe cu": "Quầy thuốc",
        "ngay cchnd cu": "",
        "gioi tinh": "0",
        "noi dung dieu chinh": "2",
        "nội dung điều chỉnh:1-pv,2-tdcm,3-ngay cccd": "",
        "noi tot nghiep": "",
        "ngay tot nghiep": "",
        "ngay cap cmnd": "",
        "ten nguoi ptcm": ten_nguoi_ptcm,
        "ngay sinh": ngay_sinh,
        "cmnd": cmnd,
        "dia chi thuong tru": dia_chi,
        "noi cap cmnd": "Cục Cảnh sát Quản lý hành chính về trật tự xã hội",
        "so dt chu hs": sdt,
    }
    if mhs in df_cchnd.index:
        values = get_dict_from_index_df(df_cchnd, mhs)
        default_origin = {k: values[k] for k in default_origin.keys() if k in values}
        values = creat_and_get(default_origin)
    else:
        default_val2 = {
            "noi dung dieu chinh": "1",
            "nội dung điều chỉnh:1-pv,2-tdcm,3-ngay cccd": "",
        }
        df_hn = pd.read_csv("dshn_duoc.csv", dtype=str, index_col="so cchnd")
        so_cchnd_cu = input_dialog("Title", "NHẬP so cchnd cu", "")
        if not so_cchnd_cu:
            return
        if so_cchnd_cu not in df_cchnd["so cchnd"].tolist():
            val_update = get_dict_from_index_df(df_hn, so_cchnd_cu)
            default_origin.update(
                {
                    k: v
                    for k, v in val_update.items()
                    if k in default_origin.keys() and v
                }
            )
            list_upper = ["ten nguoi ptcm"]
            list_ngay = [
                "ngay sinh",
                "ngay tot nghiep",
                "ngay cchnd cu",
                "ngay cap cmnd",
            ]
            # Xóa các key từ default_origin nếu có trong default_val2
            # for key in default_val2:
            #     if key in default_origin:
            #         del default_origin[key]
            default_origin["pham vi hanh nghe cu"] = val_update["vi tri hanh nghe"]
            values = creat_and_get(default_origin)
        else:
            list_upper = []
            list_ngay = []
            values = creat_and_get(default_val2)
            df_update = df_cchnd.copy()
            df_update.set_index("so cchnd", inplace=True)
            val_update = get_dict_from_index_df(df_update, so_cchnd_cu)
            default_origin.update(
                {
                    k: v
                    for k, v in val_update.items()
                    if k in default_origin.keys() and v
                }
            )
            for key in default_val2:
                if key in default_origin:
                    del default_origin[key]

            default_origin["pham vi hanh nghe cu"] = val_update["vi tri hanh nghe"]
            default_origin["ngay cchnd cu"] = val_update["ngay qd"]
            values2 = creat_and_get(default_origin)
            values.update(values2)

        formatter = DictStringFormatter(values)
        values = (
            formatter.apply_date_format(list_ngay)
            .apply_phone_format(["so dt chu hs"])
            .apply_upper(list_upper)
            .get_result()
        )
        values["noi dung dieu chinh"] = (
            values["noi dung dieu chinh"]
            .replace("1", "Thay đổi phạm vi hoạt động chuyên môn")
            .replace("2", "Thay đổi trình độ chuyên môn")
            .replace("3", "Thay đổi ngày cấp căn cước công dân")
        )

        values["so cchnd cu"] = so_cchnd_cu

        if values["noi dung dieu chinh"] == "Thay đổi phạm vi hoạt động chuyên môn":
            df_dkpv = "Nhà thuốc"
            df_ndth = "Phân phối thuốc và bảo quản thuốc"
            default_val_second = {
                "noi dung thuc hanh": df_ndth,
                "dang ky pham vi": df_dkpv,
                "cong ty xac nhan": "",
                "ngay bat dau th": "",
                "ngay ket thuc th": "",
                "LẦN": "1",
                "van de hs": "",
            }
            list_ngay_second = ["ngay bat dau th", "ngay ket thuc th"]
            values["trinh do cm"] = "Đại học dược"
        elif values["noi dung dieu chinh"] == "Thay đổi trình độ chuyên môn":
            df_tdcm = "Cao đẳng dược"
            default_val_second = {
                "van de hs": "",
                "trinh do cm": df_tdcm,
                "LẦN": "1",
            }
            list_ngay_second = ["ngay tot nghiep"]
            values["trinh do cm"] = "Cao đẳng dược"
            values["dang ky pham vi"] = "Quầy thuốc"
        else:
            df_tdcm = "Trung cấp dược"
            default_val_second = {
                "van de hs": "",
                "trinh do cm": df_tdcm,
                "LẦN": "1",
            }
            list_ngay_second = []
            values["dang ky pham vi"] = values["pham vi hanh nghe cu"]
        values_update = creat_and_get(default_val_second)
        formatter = DictStringFormatter(values_update)
        values_update = formatter.apply_date_format(list_ngay_second).get_result()
        values.update(values_update)

        if values["noi dung dieu chinh"] != "Thay đổi phạm vi hoạt động chuyên môn":
            values["so cchnd"] = (
                int(values["so cchnd cu"]) if "/" not in values["so cchnd cu"] else ""
            )
        elif "/" not in values["so cchnd cu"] and values["so cchnd cu"][0:2] != "00":
            values["so cchnd"] = values["so cchnd cu"]

        values["so cchnd cu"] = so_cchnd_cu
        values["ma ho so"] = mhs

        values["loai cap"] = (
            f"(Cấp điều chỉnh lần {values['LẦN']}: {values['noi dung dieu chinh']})"
        )

        values["ly do thu hoi"] = "Thu hồi để cấp điều chỉnh"
        if values["noi dung dieu chinh"] == "Thay đổi phạm vi hoạt động chuyên môn":
            ngay_bat_dau = datetime.strptime(values["ngay bat dau th"], "%d/%m/%Y")
            ngay_ket_thuc = datetime.strptime(values["ngay ket thuc th"], "%d/%m/%Y")
            error = check_thoi_gian(values, ngay_bat_dau, ngay_ket_thuc)
            if error:
                values["van de hs"] = error

        values["trinh do tat"] = (
            values["trinh do cm"]
            .replace("Đại học dược", "đại học")
            .replace("Cao đẳng dược", "cao đẳng")
            .replace("Trung cấp dược", "trung cấp")
        )
        values["vi tri hanh nghe"] = values["dang ky pham vi"]
        values["thu tuc"] = "DIEU CHINH ND CCHND"
        values["da nhan"] = "1"

    df_cchnd.loc[mhs] = values
    df_cchnd.to_csv("cchnd.csv", index_label="ma ho so")
    df_name = pd.read_csv("name.csv", dtype=str, index_col="ma ho so")
    update_df_name_da_nhan(df_name, values, mhs)
    close_app("okular")
    send_notification("hoàn thành")
    run_python_312("tach_nen")


if __name__ == "__main__":
    path = load_pickle(
        "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pickle_file/hs_path.pk"
    )
    mhs, _, _ = path.split("/")[-1].split("--")
    dc_cchnd(mhs)
