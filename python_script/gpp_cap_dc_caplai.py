import copy
import os
from loguru import logger
import pandas as pd
from all_pickle import load_pickle

from solid_string_format import DictStringFormatter
from god_class import (
    get_dict_from_index_df,
    send_notification,
    update_df_from_dict_by_index,
    update_df_name_da_nhan,
)
from top_most_get_text import input_dialog
from create_snippets import creat_and_get

os.chdir("/home/<USER>/Dropbox/hnd/csv_source")


def update_cac_truong_pyqt(loai_cap, values, so_dkkd, old_dict):
    values.update(
        {
            "thu tuc": f"{loai_cap} LẦN {values['Lần cấp']} - {values['Lý do cấp'].upper()}",
            "so dkkd": so_dkkd,
            "so gpp": so_dkkd,
            "van de hs": "đạt",
            "so dkkd cu": so_dkkd,
            "ngay dkkd cu": old_dict["ngay qd"],
            "so gpp CŨ": old_dict["so gpp"],
            "ngay gpp cu": old_dict["ngay qd"],  # 'ngay cap gpp
            "ngay cap gpp": old_dict["ngay qd"],
            "ngay het han gpp": old_dict["ngay het han gpp"],
            "da nhan": "1",
        }
    )


def creat_dict_thuhoi(old_dict, so_dkkd):
    dict_thuhoi = copy.deepcopy(old_dict)  # TAO RA MOT BAN SAO DOC LAP
    dict_thuhoi["so dkkd"] = so_dkkd
    dict_thuhoi["ngay cap gpp"] = dict_thuhoi["ngay qd"]
    return dict_thuhoi


def update_df_dkkd(values, df_dkkd, mhs):
    update_blank_values(values)
    df_dkkd["so dkkd"] = df_dkkd.index
    df_dkkd.set_index("ma ho so", inplace=True)
    update_df_from_dict_by_index(df_dkkd, "dkkd.csv", values, mhs)


def is_cap_lai_gcn(values):
    return "CAP LAI GCN ĐĐKKDD" in values["thu tuc"]


def is_thay_ten_csbl(values):
    return "THAY ĐỔI TÊN CƠ SỞ BÁN LẺ" in values["thu tuc"]


def is_thay_nguoi_ptcm(values):
    return "THAY NGƯỜI PHỤ TRÁCH CHUYÊN MÔN DƯỢC" in values["thu tuc"]


def update_blank_values(values):
    for col in [
        "so qd",
        "ngay qd",
        "HẠN XỬ LÝ",
        "ngay",
        "thang",
        "nam",
        "pham vi kd",
    ]:
        values[col] = ""


def is_cap_lai_do_loi_sai(values):
    return "lại" in values["Lý do cấp"] and "sai" in values["Lý do cấp"]


@logger.catch
def gpp_capdc_caplai(mhs):
    values = {}
    df_dkkd = pd.read_csv("dkkd.csv", dtype=str)
    df_dkkd.set_index("so dkkd", inplace=True)
    df_name = pd.read_csv("name.csv", dtype="str", index_col="ma ho so")
    df_ds_thu_hoi = pd.read_csv("dsthuhoigpp.csv", dtype="str", index_col="ten qt-nt")
    so_dkkd = input_dialog("NHẬP", "NHẬP SỐ GCN DKKD", "")
    values["loai cap"] = input_dialog(
        "Title", "Nhap loai cap: 1-cap lai, 2-cap dieu chinh", "2"
    )
    dict_thong_tin_cu = get_dict_from_index_df(df_dkkd, so_dkkd)  # lấy dữ liệu t
    if values["loai cap"] == "1":
        dict_thuhoi = creat_dict_thuhoi(dict_thong_tin_cu, so_dkkd)
        values["ly do"] = dict_thuhoi["ly do"] = (
            "Do lỗi ghi sai thông tin trên GCN ĐĐKKDD của"
        )
        update_df_from_dict_by_index(
            df_ds_thu_hoi, "dsthuhoigpp.csv", dict_thuhoi, values["ten qt-nt"]
        )

    list_upper = ["ten nguoi ptcm", "ten qt-nt"]
    list_ngay = ["ngay cap cmnd", "ngay cap cchnd"]
    list_phone = ["so dt chu hs"]
    list_cap_lai = [
        {"1": "Thay người phụ trách chuyên môn dược"},
        {"2": "Thay đổi tên cơ sở bán lẻ"},
        {"3": "Thay đổi tên dia chi co so"},
        {"4": "Lỗi sai của cơ quan cấp"},
        {"5": "Cấp lại do làm mất"},
        {"6": "Thay đổi số chứng chỉ hành nghề dược"},
    ]

    list_include = [
        "ten qt-nt",
        "dia chi co so",
        "ten nguoi ptcm",
        "gioi tinh",
        "cmnd",
        "noi cap cmnd",
        "ngay cap cmnd",
        "trinh do cm",
        "so cchnd",
        "noi cap cchnd",
        "ngay cap cchnd",
        "dia chi thuong tru",
        "co quan chu quan",
        "dia chi co quan chu quan",
        "pham vi kd",
        "Lần cấp",
        "Lý do cấp",
        "ngay sinh",
    ]

    dict_to_get = dict.fromkeys(list_include, "")
    # convert to string
    dict_to_get["ly do full"] = ", ".join(
        [f"{list(d.keys())[0]}. {list(d.values())[0]}" for d in list_cap_lai]
    )
    dict_to_get.update(
        {k: v for k, v in dict_thong_tin_cu.items() if k in list_include}
    )
    dict_to_get["pham vi kd"] = "default"
    dict_to_get["Lý do cấp"] = "6"
    values = creat_and_get(dict_to_get)
    if is_cap_lai_do_loi_sai(values):
        loai_cap = "CAP LAI"
    else:
        loai_cap = "CAP DIEU CHINH"
    formatter = DictStringFormatter(values)
    values = (
        formatter.apply_date_format(list_ngay)
        .apply_upper(list_upper)
        .apply_phone_format(list_phone)
        .get_result()
    )
    update_cac_truong_pyqt(loai_cap, values, so_dkkd, dict_thong_tin_cu)
    values["ma ho so"] = mhs
    update_df_dkkd(values, df_dkkd, mhs)
    update_df_name_da_nhan(df_name, values, mhs)
    send_notification("Đã xong")


if __name__ == "__main__":
    path = load_pickle(
        "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pickle_file/hs_path.pk"
    )
    mhs, _, _ = path.split("/")[-1].split("--")
    gpp_capdc_caplai(mhs)
