import os
from datetime import datetime

import pandas as pd
from loguru import logger
from god_class import (
    convert_ngay,
    update_df_from_dict_by_index,
    update_df_name_da_<PERSON>han,
    TextProcess,
)
from create_snippets import creat_and_get
from all_pickle import load_pickle
from solid_string_format import DictStringFormatter


@logger.catch
def mp_nhap_hs(mhs):
    os.chdir("/home/<USER>/Dropbox/hnd/csv_source")
    path = "ds_ct_mp.csv"
    indexs = "ma ho so"
    df_mp = pd.read_csv(path, dtype=str, index_col=indexs)
    df_mp.fillna("", inplace=True)
    df_name = pd.read_csv("name.csv", dtype=str, index_col="ma ho so")
    if mhs in df_mp.index.tolist():
        default_values = df_mp.loc[mhs].to_dict()
        values = creat_and_get(default_values)
        formatter = DictStringFormatter(values)
        values = (
            formatter.apply_date_format(["ngay"])
            .apply_phone_format(["so dt chu hs"])
            .apply_title(["giam doc", "nguoi phu trach sx"])
            .get_result()
        )

    else:
        default_values = {
            "ngay": datetime.now().strftime("%d%m%Y"),
            "gcn dkkd": "Giấy chứng nhận đăng ký doanh nghiệp công ty trách nhiệm hữu hạn một thành viên do Sở Kế hoạch và Đầu tư Thành phố Hà Nội cấp, đăng ký lần đầu ngày 22/01/2024, mã số doanh nghiệp: 0110610008",
            "mo ta xuong sx": "Khu vực thay đồ, khu vực rửa tay, khu vực rửa dụng cụ, khu vực buồng đệm, khu vực xử lý bao bì, kho nguyên liệu, khu vực cân, khu vực pha chế, khu vực chiết rót, khu vực đóng gói cấp 2, khu vực kho bao bì cấp 1, khu vực bao bì cấp 2, khu vực thành phẩm",
            "mo ta trang thiet bi": "Công ty trang bị 01 máy khuấy trộn, 01 máy trộn đồng nhất và nhũ hóa, 02 máy chiết rót kem, 01 máy in số lô, NSX, HD, 01 máy đo pH, 01 máy đo độ nhớt, 02 cân điện tử, 02 cân phân tích, 07 nhiệt ẩm kế",
        }
        include = [
            col for col in df_mp.columns.tolist() if col not in ["so_gcn_dksxmp"]
        ]
        dict_second = dict.fromkeys(include, "")
        default_values.update(dict_second)
        values = creat_and_get(default_values)
        formatter = DictStringFormatter(values)
        values = (
            formatter.apply_date_format(["ngay"])
            .apply_phone_format(["so dt chu hs"])
            .apply_title(["giam doc", "nguoi phu trach sx"])
            .get_result()
        )

    if mhs in df_mp.index.tolist():
        values["so_gcn_dksxmp"] = df_mp.loc[mhs, "so_gcn_dksxmp"]
    values["ma ho so"] = mhs
    values["dia chi co so"] = values["dia chi xuong sx"]
    values["ten qt-nt"] = values["ten cong ty mp"]
    values["ten nguoi ptcm"] = values["giam doc"]
    values["ngay"], values["thang"], values["nam"] = convert_ngay(values["ngay"]).split(
        "/"
    )
    update_df_from_dict_by_index(df_mp, path, values, mhs)
    update_df_name_da_nhan(df_name, values, mhs)
    bb = TextProcess("bbtd_mp")
    bb.format_text(values)
    bb.compile_latex()
    bb.copy_latex_file(
        f"/home/<USER>/Dropbox/hnd/latexall/bbtd_cong_ty/bbtd_mp/{values['ten cong ty mp']}.tex"
    )


if __name__ == "__main__":
    path = load_pickle(
        "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pickle_file/hs_path.pk"
    )
    mhs, _, _ = path.split("/")[-1].split("--")
    mp_nhap_hs(mhs)
