import sys
from datetime import datetime, timedelta
import time

import demjson3 as demjson
import requests
from PyQt5.QtGui import QFont
from PyQt5.QtWidgets import QApplication, QMainWindow, QTableWidget, QTableWidgetItem

from god_class import TelegramSend


def get_ma_lich():
    today = datetime.now()
    year = today.year

    urls = f"https://qlvb-api.vinhphuc.gov.vn/api/lich-cong-tac/danh-sach-lich-cong-tac-don-vi-quan-tri?ma_don_vi_quan_tri=1187&nam={year}"
    pay = {}
    head = {
        "Authorization": "Bearer acKKZDmXqpUmn7D3vuzwk88RN909S7Soc5YlXQhyV6I9lU6UNYaTHtnVEhzxoo6Pul1K2NroQeS6584FZDucIYETRjjn70alDIbOUQ4V3zt8JarwIvOhqZ+z96uFvFuZjsu03k3l5Ou0DhYlEmlQ/15duefP7Ozfa3bfIo3bASLX3cOwnkpUlxkAsZgJAAQufC1TYBKfJMIn/NR0nW7uuYcPT7ic/DdhkiE08Us6bbSjqfTbHGdR8fC8Nza/I52dVn4RLZDAGfVFhFgZS6qw2j2VTpQ1hpMelekgyrg23fFO2qhb81RllfgYPTifzG9j",
    }
    responses = requests.request("GET", urls, headers=head, data=pay)
    print(responses.text)
    responses = requests.request("GET", urls, headers=head, data=pay)
    results = demjson.decode(responses.text)
    # Tìm ngày thứ Hai gần nhất
    monday = today - timedelta(days=today.weekday())
    monday = monday.strftime("%d/%m/%Y")
    for i in range(len(results["data"])):
        if results["data"][i]["ngay_bat_dau_vn"] == monday:
            return int(results["data"][i]["ma_lich_cong_tac_kc"])


url = f"https://qlvb-api.vinhphuc.gov.vn/api/LichCongTac/LichCongTacChiTiet/LayLichCongTacChiTiet_Vpc?ma_lich_cong_tac={get_ma_lich()}"

payload = {}
headers = {
    "Authorization": "Bearer acKKZDmXqpUmn7D3vuzwk88RN909S7Soc5YlXQhyV6I9lU6UNYaTHtnVEhzxoo6Pul1K2NroQeS6584FZDucIYETRjjn70alDIbOUQ4V3zt8JarwIvOhqZ+z96uFvFuZjsu03k3l5Ou0DhYlEmlQ/15duefP7Ozfa3bfIo3bASLX3cOwnkpUlxkAsZgJAAQueOVqisskyoon/NR0nW7uuYcPT7ic/DdhkiE08Us6bbSjqfTbHGdR8fC8Nza/I52dVn4RLZDAGfVFhFgZS6qw2j2VTpQ1hpMelekgyrg23fFO2qhb81RllfgYPTifzG9j",
}

response = requests.request("GET", url, headers=headers, data=payload)
res = demjson.decode(response.text)

# Tìm sự kiện gần nhất với thời điểm hiện tại
now = datetime.now()
closest_date = None
min_time_diff = timedelta.max

for item in res["data"]:
    event_date = datetime.strptime(f"{item['ngay_thuc_hien_vn']}", "%d/%m/%Y")
    if event_date.date() > now.date():
        time_diff = event_date - now
        if time_diff < min_time_diff:
            min_time_diff = time_diff
            closest_date = event_date.date().strftime("%d/%m/%Y")
tele = TelegramSend("hnd")
hop_chung = ""
hop_ld = ""
# In ra tất cả sự kiện của ngày gần nhất
for item in res["data"]:
    if item["ngay_thuc_hien_vn"] == closest_date or item[
        "ngay_thuc_hien_vn"
    ] == now.strftime("%d/%m/%Y"):
        if not item["gia_tri_cot_3"]:
            continue
        message = f"{item['ngay_thuc_hien_vn']} {item['gio_thuc_hien']}\n{item['gia_tri_cot_3']}\n\n{item['gia_tri_cot_1']}\n\n{item['noi_dung']}"

        if (
            "Nguyễn Đắc Ca" in item["gia_tri_cot_3"]
            or "các phó giám đốc" in item["gia_tri_cot_3"].lower()
            or "lưu thị hồng lê" in item["gia_tri_cot_3"].lower()
            or "chủ tịch" in item["gia_tri_cot_3"].lower()
            or "lãnh đạo sở" in item["gia_tri_cot_3"].lower()
        ):
            hop_ld += (
                message + "\n********************************************************\n"
            )
        else:
            hop_chung += (
                message + "\n********************************************************\n"
            )
""" if hop_chung:
    # Split message into chunks of 4000 characters
    chunks = [hop_chung[i:i+4000] for i in range(0, len(hop_chung), 4000)]
    for chunk in chunks:
        tele.send_message_warp(chunk)
        time.sleep(1) # Add delay between messages """
if hop_ld:
    tele.send_message_warp(hop_ld + "#lanhdao")
else:
    tele.send_message_warp("Không có lịch họp")
