import pandas as pd
from god_class import TelegramSend, send_notification, send_error_to_telegram
from playwright_class import Dich<PERSON>u<PERSON><PERSON>
from loguru import logger
import os


os.chdir("/home/<USER>/Dropbox/hnd/csv_source")


def sort_df_by_han(df):
    df["han back_up"] = pd.to_datetime(df["han back_up"], format="%d/%m/%Y %H:%M:%S")
    df.sort_values(by="han back_up", ascending=True, inplace=True)
    df["han back_up"] = df["han back_up"].dt.strftime("%d/%m/%Y %H:%M:%S")
    df.reset_index(inplace=True)
    df.index += 1
    return df


def creat_report_and_send(df):
    report = ""
    df = sort_df_by_han(df)
    for index, row in df.iterrows():
        report += f"{index}. {row['thu tuc']} - {row['ten nguoi ptcm']} - {row['han back_up']}\n\n"
    return report


class PhanHoSo(DichVuCong):
    def __init__(self, user):
        super().__init__(user)
        self.user = user
        self.df_dxl = pd.read_csv("da_tra_kq.csv", dtype="str", index_col="ma ho so")

    def get_ho_so_info(self, element, dict_thutuc):
        mhs = self.get_attribute("value", element)
        thutuc = dict_thutuc[self.get_attribute("data-tentatthutuc", element)]
        nguoinop = self.get_attribute("data-tennguoinop", element)
        return f"--{mhs}--{thutuc}--{nguoinop}"

    def get_hs_chua_trinh_ky(self, elements):
        mhs_chua_trinh_ky = []
        for element in elements:
            mhs = self.get_attribute("value", element)
            if mhs in self.df_dxl.index.tolist():
                mhs_chua_trinh_ky.append(mhs)
        df_chua_trinh_ky = self.df_dxl.loc[mhs_chua_trinh_ky]
        mhs_trinh_ky = [
            i for i in self.df_dxl.index.tolist() if i not in mhs_chua_trinh_ky
        ]
        df_trinh_ky = self.df_dxl.loc[mhs_trinh_ky]
        return df_chua_trinh_ky, df_trinh_ky

    @send_error_to_telegram
    @logger.catch
    def run(self):
        super().setup(headless=True)
        super().login_dvc_by_user(self.user)
        self.go_to_trangthai("TKY")
        self.expand_ds()
        elements = self.get_row_elements()
        df_chua_trinh_ky, df_trinh_ky = self.get_hs_chua_trinh_ky(elements)
        report_chua_trinh_ky = creat_report_and_send(df_chua_trinh_ky)
        report_trinh_ky = creat_report_and_send(df_trinh_ky)

        if report_chua_trinh_ky:
            report = "DANH SÁCH HỒ SƠ CHƯA TRÌNH KÝ:\n\n" + report_chua_trinh_ky
            tele = TelegramSend("hnd")
            tele.send_message_warp(report)
        if report_trinh_ky:
            report = "DANH SÁCH HỒ SƠ ĐÃ TRÌNH KÝ:\n\n" + report_trinh_ky
            tele = TelegramSend("hnd")
            tele.send_message_warp(report)
        send_notification("done")


dvc = PhanHoSo("tp")
dvc.run()
