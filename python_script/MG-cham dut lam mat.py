# pylint: disable=consider-using-f-string
import os
import subprocess

import PySimpleGUI as sg

os.chdir('/home/<USER>/Dropbox/hnd/csv_source')
sg.theme("DarkAmber")
layout = [
    [sg.Text("Tên cơ sở"), sg.InputText("")],
    [sg.Text("Địa chỉ"), sg.InputText("")],
    [sg.Text("Số giấy đkkd (TẮT)"), sg.InputText("")],
    [sg.Text("Ngày đkkkd (TẮT)"), sg.InputText("")],
    [sg.Text("so cchnd full"), sg.InputText("")],
    [sg.Text("ngay cap cchnd (TẮT)"), sg.InputText("")],
    [sg.Text("noi cap cchnd"), sg.InputText("Sở Y tế tỉnh Phú Thọ")],
    [sg.Text("<PERSON><PERSON><PERSON> chấ<PERSON> dứ<PERSON> (TẮT)"), sg.InputText("")],
    [sg.Text("ten nguoi ptcm (THƯỜNG)"), sg.InputText("")],
    [sg.<PERSON>ton("Ok", size=(10, 0))],
]
window = sg.Window("Window Title", layout, keep_on_top=True, location=(1300, 200))
while True:
    event, values = window.read()
    if event == sg.WIN_CLOSED or event == "Ok":
        break
window.close()


def convert_ngay(ngay):
    if len(ngay) > 1:
        if "/" not in ngay:
            if str(ngay)[2:4] in ["01", "02", "10", "11", "12"]:
                a = str(ngay)[2:4]
            else:
                a = str(ngay)[3:4]
            ngay = str(ngay)[:2] + "/" + a + "/" + str(ngay)[-4:]
    else:
        ngay = ""
    return ngay


tencs = values[0]
diachi = values[1]
sodkkd = values[2] + r"/ĐKKDD-VP"
ngaycap = values[3]
cchnd = values[4]
noicap = values[6]
ngaycapcchnd = convert_ngay(values[5])
ngaychamdut = convert_ngay(values[7])
nguoiptcm = values[8].title()
if str(tencs).find("Quầy thuốc") > -1:
    loaihinh = "Quầy thuốc"
else:
    loaihinh = "Nhà thuốc"
text = rf"""\documentclass{{article}}

\usepackage[T5]{{fontenc}}
\usepackage{{parskip}}
\usepackage{{setspace}}

\usepackage[fontsize=14pt]{{scrextend}}
\usepackage[a4paper,vmargin=2cm,right=2cm,left=3cm,]{{geometry}}
\usepackage{{mathptmx}}
\usepackage{{ulem}}
\renewcommand{{\ULdepth}}{{7pt}}
\renewcommand{{\ULthickness}}{{0.75pt}}
\usepackage{{setspace}}

\usepackage{{indentfirst}}
\setlength{{\parindent}}{{1.27cm}}
\renewcommand{{\baselinestretch}}{{1}}
\usepackage{{tabularray}}



\thispagestyle{{empty}}
\pagenumbering{{gobble}}

\begin{{document}}
}

\setstretch{{1}}
\begin{{center}}
    \textbf{{CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM }}

    \uline{{\textbf{{Độc lập - Tự do - Hạnh phúc}}}}

    \vspace{{0.5cm}}

    \textbf{{ĐƠN XIN cham dut HOẠT ĐỘNG KINH DOANH}}

\end{{center}}
\setstretch{{1.5}}

Tên cơ sở kinh doanh: {tencs}.

Địa chỉ kinh doanh: {diachi}

Giấy chứng nhận đủ điều kiện kinh doanh dược số: {sodkkd} do Sở Y tế tỉnh Phú Thọ cấp ngày {ngaycap};

Tên người phụ trách chuyên môn về dược: {nguoiptcm}; chứng chỉ hành nghề dược số: {cchnd} do {noicap} cấp ngày {ngaycapcchnd}

Do công việc kinh doanh gặp nhiều khó khăn, {loaihinh} không đủ khả năng tiếp tục hoạt động. Vì vậy, tôi viết đơn này để báo cáo Sở tế Vĩnh Phúc về việc {loaihinh} xin chấm dứt hoạt động kinh doanh dược kể từ ngày {ngaychamdut}

Trong quá trình hoạt động kinh doanh, tôi đã làm mất bản gốc Giấy chứng nhận đạt Thực hành tốt cơ sở bán lẻ thuốc và giấy chứng nhận đủ điều kiện kinh doanh dược

Xin trân trọng cảm ơn!

\vspace{{0.5cm}}

\setstretch{{1}}
\noindent
\begin{{minipage}}{{\textwidth}}
\begin{{tblr}}{{width=1\linewidth,
    colspec={{X[1,c]X[1.5,c]}},
    colsep=3pt,
    rowsep=3pt,
    rows={{m}},row{{1}}={{font=\bfseries,c}}}}
     & NGƯỜI PHỤ TRÁCH CHUYÊN MÔN \\
     &                            \\
     &                            \\
     &                            \\
     &                            \\
     & \textbf{{{nguoiptcm}}}  \\
\end{{tblr}}
\end{{minipage}}
\end{{document}}"""
f = open("mylatex.tex", "w", encoding="utf-8")
f.write(text)
f.close()
subprocess.check_call(["pdflatex", "mylatex.tex"])
subprocess.Popen(["okular", "mylatex.pdf"])
