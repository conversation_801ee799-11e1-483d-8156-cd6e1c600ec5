import os

import pandas as pd

os.chdir("/home/<USER>/Dropbox/hnd/csv_source")
df_traloi_xm = pd.read_csv("danh-sach-thieu-nhi-syt-2024.csv", dtype=str)
phong = df_traloi_xm["PHÒNG"].unique().tolist()

for name in phong:
    df_name = df_traloi_xm[df_traloi_xm["PHÒNG"] == name]
    df_name.reset_index(inplace=True, drop=True)
    df_name.index += 1
    df_name = df_name.drop(columns=["SỐ TIỀN"])
    df_name.to_excel(f"{name}.xlsx", index_label="STT")
    df_name.to_csv(f"{name}.csv", index_label="STT")
