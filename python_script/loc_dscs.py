import os

import pandas as pd


def main():
    os.chdir("/home/<USER>/Dropbox/hnd/csv_source")
    df = pd.read_csv(
        "/home/<USER>/Dropbox/hnd/latexall/documents/HND/LIÊN THÔNG CSKD/all.csv",
        dtype=str,
        header=10,
    )

    df = df[
        [
            "Tên nhà thuốc",
            " loai hinh",
            "Tỉnh/ Thành phố",
            "Quận/ Huyện",
            "Xã/ Phường",
            "Địa chỉ",
            "Tên người đại diện",
            "Số điên thoại NĐD",
            "<PERSON><PERSON> chứng chỉ hành nghề",
            "Trình độ chuyên môn",
        ]
    ]

    df["Tỉnh/ Thành phố"] = df["Tỉnh/ Thành phố"].str.replace("Tỉnh", "tỉnh")
    df["Quận/ Huyện"] = (
        df["Quận/ Huyện"]
        .str.replace("Thị xã Phúc <PERSON>ên", "thành phố Phúc Yên")
        .str.replace("Thành phố Vĩnh Yên", "thành phố Vĩnh Yên")
    )

    df = df[
        (df["Quận/ Huyện"] == "thành phố Phúc Yên")
        | (df["Quận/ Huyện"] == "thành phố Vĩnh Yên")
    ]

    df.reset_index(drop=True, inplace=True)
    df.index += 1

    df["dia chi"] = (
        df["Địa chỉ"]
        + ", "
        + df["Xã/ Phường"]
        + ", "
        + df["Quận/ Huyện"]
        + ", "
        + df["Tỉnh/ Thành phố"]
    )
    df = df[
        [
            "Tên nhà thuốc",
            " loai hinh",
            "dia chi",
            "Tên người đại diện",
            "Số điên thoại NĐD",
            "Số chứng chỉ hành nghề",
            "Trình độ chuyên môn",
        ]
    ]

    df.rename(
        columns={
            "Tên nhà thuốc": "TÊN CƠ SỞ BÁN LẺ",
            " loai hinh": "loai hinh",
            "Tên người đại diện": "ten nguoi ptcm DƯỢC",
            "Số điên thoại NĐD": "SĐT",
            "Số chứng chỉ hành nghề": "so cchnd",
            "Trình độ chuyên môn": "TRÌNH ĐỘ CHUYÊN MÔN",
        },
        inplace=True,
    )

    df.to_csv(
        "/home/<USER>/Dropbox/DANH SÁCH CƠ SỞ BÁN LẺ THUỐC Ở VĨNH YÊN VÀ PHÚC YÊN.csv",
        index_label="stt",
    )


if __name__ == "__main__":
    main()
