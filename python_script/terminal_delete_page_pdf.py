import sys

import pikepdf

file_path = sys.argv[1]
page_to_delete = int(sys.argv[2])


def delete_page(pdf_path, page_to_delete):
    # Mở file PDF
    pdf = pikepdf.open(pdf_path, allow_overwriting_input=True)
    # X<PERSON>c định trang cần xóa, chú ý rằng trong Python chỉ mục bắt đầu từ 0
    del pdf.pages[page_to_delete - 1]
    # Lưu tệp PDF lại với cùng tên file
    pdf.save(pdf_path)
    # Đóng tệp PDF
    pdf.close()
    print(f"Đã xóa trang {page_to_delete} của file {pdf_path}.")


# Ví dụ sử dụng:
delete_page(file_path, page_to_delete)