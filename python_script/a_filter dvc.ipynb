{"cells": [{"metadata": {"ExecuteTime": {"end_time": "2024-05-31T07:44:12.265317Z", "start_time": "2024-05-31T07:44:12.001567Z"}}, "cell_type": "code", "source": ["import os\n", "\n", "import pandas as pd\n", "\n", "os.chdir('/home/<USER>/Dropbox/hnd/csv_source')\n", "df = pd.read_csv('dkkd.csv', dtype=str)\n", "df.fillna('', inplace=True)\n"], "id": "9ba5ac0b8be9f4e8", "execution_count": 1, "outputs": []}, {"metadata": {"ExecuteTime": {"end_time": "2024-05-31T07:44:29.410832Z", "start_time": "2024-05-31T07:44:29.374836Z"}}, "cell_type": "code", "source": "df.to_csv('test.csv', index=False)", "id": "616e8de5ffba3b", "execution_count": 2, "outputs": []}, {"metadata": {}, "cell_type": "code", "execution_count": null, "source": "", "id": "77f26df599a849a4", "outputs": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "/home/<USER>/python313/bin/python", "name": "/home/<USER>/python313/bin/python"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "/home/<USER>/python313/bin/python", "nbconvert_exporter": "/home/<USER>/python313/bin/python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}