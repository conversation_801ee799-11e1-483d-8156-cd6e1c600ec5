from csv_load_and_export import CsvLoaderFactory
from god_class import fzf_get_dict_from_column, TextProcess
import shutil
import os

# Tải dữ liệu
df = CsvLoaderFactory.create_fillna_loader().load_df("co_so_ban_le", "id_column")
mask = df["ve_so_do"] != "1"
df = df[mask]
row_dict = fzf_get_dict_from_column(df)
row_dict["ten qt-nt full"] = row_dict["loai hinh"] + " " + row_dict["ten qt-nt"].title()
text = TextProcess("/hs_mission_pyqt/ban_ve")

text.format_text(row_dict)
shutil.copy(
    "/home/<USER>/Dropbox/hnd/latexall/mylatex/mylatex.tex",
    "/home/<USER>/Dropbox/hnd/latexall/mylatex/ban_ve.tex",
)
os.system(
    "wmctrl -xa Terminator && tmux new-window -n ve_so_do nvim /home/<USER>/Dropbox/hnd/latexall/mylatex/ban_ve.tex"
)
