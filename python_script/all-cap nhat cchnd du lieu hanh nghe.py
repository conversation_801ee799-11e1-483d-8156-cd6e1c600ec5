from god import *
from god_class import show_message

os.chdir("/home/<USER>/Dropbox/hnd/csv_source")
path = "dshn_duoc.csv"


def get_excel_values(index_val, excel, index_col):
    df = pd.read_csv(excel, dtype=str)
    df.set_index(index_col, inplace=True)
    df.fillna("", inplace=True)
    try:
        selected_values = df.loc[df.index == index_val].tail(1)
        return selected_values.to_dict(orient="records")[0], df
    except:
        empty_dict = dict.fromkeys(df.columns, "")
        return empty_dict, df


def get_last_loc(df, index, col_name):
    result = df.loc[index, col_name]
    if isinstance(result, pd.Series):
        result = result.iloc[-1]
    else:
        result = df.at[index, col_name]
    return result


df = pd.read_csv(path, dtype=str, index_col="so cchnd")


exclude = [
    "so cchnd",
    "ma ho so",
    "loai cap",
    "so qd",
    "ngay",
    "thang",
    "nam",
    "ngay qd",
    "HẠN XỬ LÝ",
    "pham vi hanh nghe cu",
    "dang ky pham vi",
    "so cchnd cu",
    "ngay cchnd cu",
    "cong ty xac nhan",
    "ten cong ty",
    "noi dung thuc hanh",
    "ngay bat dau th",
    "ngay ket thuc th",
    "dia chi co so",
    "ngay het han gpp",
    "co quan chu quan",
    "dia chi co quan chu quan",
    "ngay han",
    "Unnamed: 2",
    "CHỮ KÝ VÀ TÊN TRONG DKKD",
    "THUYẾT MINH KHI CÓ NGHIỆN, HƯỚNG THẦN",
    "noi dung dieu chinh",
    "ngay ket thuc th.1",
    "thu tuc",
    "ĐÃ CÓ CẬP NHẬT KT CHƯA",
    "trinh do tat",
    "da nhan",
    "CHỜ TRẢ",
    "ten nguoi ptcm",
    "ngay td",
    "so gpp",
    "noi tot nghiep",
    "ngay tot nghiep",
    "ly do CẤP LẠI",
    "Unnamed: 8",
    "SỐ GCN ĐĐKKDD CƠ SỞ ĐĂNG KÝ HN",
    "ten nhan vien",
    "so dt chu hs",
    "trinh do cm",
    "vi tri hanh nghe",
    "loai hinh",
    "cmnd",
    "noi cap cmnd",
    "ngay cap cmnd",
    "ngay sinh",
    "gioi tinh",
    "dia chi thuong tru",
    "thoi gian dang ky lam viec",
    "vi tri chuyen mon",
    "NGÀY VÀO",
    "LẦN",
    "GHI CHÚ",
    "Đã có CNKT chưa?",
]
exclude.extend(
    [
        "so dkkd cu",
        "ngay dkkd cu",
        "so gpp CŨ",
        "ngay gpp cu",
        "pham vi kd",
        "co nhan vien khong",
        "CÓ truc thuoc KHÔNG",
        "bao quan lanh",
        "van de hs",
        "noi nhan",
    ]
)

# excel_values, df = get_excel_values(index_val, path, "so cchnd")
# TODO 3. SETUP CÁC NHÓM

list_ngay = ["ngay cap cchnd", "ngay qd CẤP"]

list_phone = []

list_upper = ["ten nguoi ptcm"]

default_values = {"noi cap cchnd": "Sở Y tế tỉnh Phú Thọ"}

multiline = ""

layout = [
    [
        sg.Text("so cchnd", size=(20, 1)),
        sg.Input(default_text="", key="input_cchnd", size=(35, 1)),
    ]
]

fields = [col for col in df.columns.tolist() if col not in exclude]
for col in fields:
    layout.append([sg.Text(col, size=(20, 1)), sg.Input("", key=col, size=(35, 1))])
layout.append([sg.Button("OK", size=(10, 3)), sg.Button("Cancel", size=(10, 3))])
window = sg.Window(
    f"NHẬP THÔNG TIN", layout, location=(1450, 100), keep_on_top=True, finalize=True
)
window["input_cchnd"].bind("<Return>", "_Enter")
window.bind("<F1>", "F1")
while True:
    event, values = window.read()
    if event == sg.WINDOW_CLOSED or event == "Cancel":
        break
    if event == "input_cchnd" + "_Enter":
        excel_values, _ = get_excel_values(values["input_cchnd"], path, "so cchnd")
        if excel_values.get("ten co so dang ky hn") != "":
            show_message(
                "THÔNG BÁO",
                f"ĐÃ LÀ NHÂN VIÊN {excel_values.get('ten co so dang ky hn')}",
            )
        for column in fields:
            if column != "ten co so dang ky hn":
                # TODO 4.1 append các giá trị mặc định nếu nó rỗng
                if column in default_values and excel_values.get(column) == "":
                    window[column].update(value=default_values[column])
                else:
                    if column not in exclude:
                        window[column].update(value=excel_values.get(column))

    if event == "OK" or event == "F1":
        values["so cchnd"] = values["input_cchnd"]
        for key in list_ngay:
            values[key] = convert_ngay(values[key])
        for key in phone:
            values[key] = phone_format(values[key])
        for key in upper:
            values[key] = values[key].upper()

        # TODO 6. ghi vào 3 file excel
        def add_update(excel_file, label):
            df_temp = pd.DataFrame(values, index=[values[label]])
            df = pd.read_csv(excel_file, dtype=str, index_col=label)
            df.update(df_temp)
            df.to_csv(excel_file, index_label=label)

        def add_loc(excel_file, label):
            df2 = pd.read_csv(excel_file, dtype=str, index_col=label)
            df2.loc[values[label]] = values
            df2.to_csv(excel_file, index_label=label)

        if excel_values["ten nguoi ptcm"] == "":
            add_loc("dshn_duoc.csv", "so cchnd")
        else:
            add_update("dshn_duoc.csv", "so cchnd")

        for column in fields:
            if column != "ten co so dang ky hn":  # TODO CÁ THỂ HOÁ
                window[column].update(value="")
        window["input_cchnd"].update(value="")
        window["input_cchnd"].set_focus()
