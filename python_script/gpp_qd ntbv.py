import datetime as dt
import os
import pandas as pd
from PyQt5.QtWidgets import QInputDialog, QApplication
from god_class import auto_text_to_ioffice
from top_most_get_text import input_dialog


def convert_ngay(ngay):
    if len(ngay) > 1 and "/" not in ngay:
        a = ngay[2:4] if ngay[2:4] in {"01", "02", "10", "11", "12"} else ngay[3:4]
        return f"{ngay[:2]}/{a}/{ngay[-4:]}"
    else:
        return ""


os.chdir("/home/<USER>/Dropbox/hnd/csv_source")
app = QApplication([])
df = pd.read_csv("name.csv", dtype="str", index_col="ma ho so")
dsfulldk = pd.read_csv("dkkd.csv", dtype="str", index_col="ma ho so")
split_date, ok = QInputDialog.getText(
    None,
    "Input Dialog",
    "<PERSON>hậ<PERSON> ng<PERSON><PERSON> định (viết tắt): (dkkd: so qd-null và van de hs-gpp)",
    text=dt.date.today().strftime("%d%m%Y"),
)

co_quan_chu_quan = input_dialog("NHẬP CQCQ", "TÊN CQCQ", "", 800, 500, 400, 400)


# Tạo hàm kiểm tra
def check_string(input_string, dictionary):
    for key in dictionary.keys():
        if key in input_string:
            return dictionary[key]
    return None  # Trả về None nếu không tìm thấy key nào


if ok:
    split_date = convert_ngay(split_date)
    mask1 = dsfulldk["so qd"].isnull()
    mask2 = dsfulldk["van de hs"].str.lower() == "gpp"
    dsqd = dsfulldk[mask1 & mask2]
    df.loc[df["van de hs"] == "đạt", "CHỜ TRẢ"] = "1"
    df.to_csv("name.csv", index=True, index_label="ma ho so")
    dsqd["ngay het han gpp"] = split_date[:-4] + str(int(split_date[-4:]) + 3)
    for index, row in dsqd.iterrows():
        dsfulldk.at[index, "ngay het han gpp"] = split_date[:-4] + str(
            int(split_date[-4:]) + 3
        )
        dsfulldk.at[index, "ngay qd"] = split_date
        dsfulldk.at[index, "ngay"] = split_date.split("/")[0]
        dsfulldk.at[index, "thang"] = split_date.split("/")[1]
        dsfulldk.at[index, "nam"] = split_date.split("/")[2]
    dsfulldk.to_csv("dkkd.csv", index=True, index_label="ma ho so")
    dsqd.drop(columns=["stt"], inplace=True, errors="ignore")
    stt = [i + 1 for i in range(len(dsqd))]
    dsqd.insert(0, "stt", stt)
    dsqd["ten nguoi ptcm"] = dsqd["ten nguoi ptcm"].str.title()
    dsqd["ten qt-nt"] = (
        dsqd["ten qt-nt"]
        .str.title()
        .str.replace("Thuốc", "thuốc")
        .str.replace("Tế Xã", "tế xã")
        .str.replace("Tế Phường", "tế phường")
        .str.replace("Tế Thị Trấn", "tế thị trấn")
        .str.replace("Bệnh Viện", "bệnh viện")
    )
    dsqd["thu tuc"] = dsqd["thu tuc"].str.replace("CAP GPP VA DKKD", "Cấp lần đầu GPP")
    dsqd["so cchnd"] = "{" + dsqd["so cchnd"].str.replace("/CCHND-", r"\\/CCHND-") + "}"
    dsqd["truc thuoc"] = dsqd["ten qt-nt"]
    dsqd["thu tuc"] = dsqd["thu tuc"].str.replace(
        "DANH GIA DAP UNG GPP NTBV", "Cấp lần đầu GPP"
    )
    dsqd["ngay qd"] = split_date
    dsqd["gioi tinh"] = dsqd["gioi tinh"].str.replace("1", "Nam").str.replace("0", "Nữ")
    dsqd = dsqd[
        [
            "stt",
            "ten qt-nt",
            "dia chi co so",
            "ten nguoi ptcm",
            "cmnd",
            "so cchnd",
            "ngay cap cchnd",
            "noi cap cchnd",
            "trinh do cm",
            "so gpp",
            "ngay qd",
            "ngay het han gpp",
            "thu tuc",
        ]
    ]
    dsqd.fillna("", inplace=True)
    ds = dsqd.astype(str).apply("&".join, axis=1)
    clip = ds.str.cat(sep=r"\\" + "\n") + r"\\"
    ngay = split_date.split("/")[0]
    thang = split_date.split("/")[1]
    nam = split_date.split("/")[2]
    socs = str(len(dsqd)).zfill(2)
    soqd = r"\hspace{1.5cm}"
    text = rf"""\documentclass{{article}}       
       \usepackage[T5]{{fontenc}}
       \usepackage{{parskip}}
       \nonstopmode       
       \usepackage[fontsize=14pt]{{scrextend}}
       \usepackage[a4paper,vmargin=2cm,right=2cm,left=3cm,]{{geometry}}
       \usepackage{{pdflscape}}
       \usepackage{{mathptmx}}
       \usepackage{{ulem}}
       \renewcommand{{\ULdepth}}{{7pt}}
        \renewcommand{{\ULthickness}}{{0.5pt}}
       \usepackage{{setspace}}
       \usepackage{{tabularx}}
       \usepackage{{tabularray}}       
       \usepackage{{indentfirst}}       
       \setlength{{\parindent}}{{1.27cm}}             
       \setlength{{\parskip}}{{6pt}}
       \thispagestyle{{empty}}
       \usepackage{{fancyhdr}}
       \fancyhf{{}}
       \fancyhead[C]{{\small\thepage}}
       \renewcommand{{\headrulewidth}}{{0pt}}
       \pagestyle{{fancy}}

       \begin{{document}}


       \newcolumntype{{b}}{{>{{\centering\arraybackslash}}X}} 
       \newcolumntype{{s}}{{>{{\centering\arraybackslash\hsize=0.6\hsize}}X}} 

       \noindent 
       \begin{{tabularx}}{{1.1\textwidth}}{{sb}} 
       \fontsize{{13pt}}{{0pt}}\selectfont UBND TỈNH VĨNH PHÚC & \textbf{{\fontsize{{13pt}}{{0pt}}\selectfont CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM}} \\ 
       \textbf{{\fontsize{{13pt}}{{0pt}}\selectfont SỞ Y TẾ}}

       \rule[0.71\baselineskip]{{.09\linewidth}}{{.5pt}} & \textbf{{\fontsize{{14pt}}{{0pt}}\selectfont Độc lập - Tự do - Hạnh phúc}}

       \rule[0.6\baselineskip]{{0.62\linewidth}}{{.5pt}} \\[10pt] 
       \fontsize{{14pt}}{{0pt}}\selectfont Số: {soqd}/QĐ-SYT & \fontsize{{14pt}}{{0pt}}\selectfont \textit{{Phú Thọ, ngày {ngay} tháng {thang} năm {nam}}} \end{{tabularx}} \vspace{{10pt}}

       \setstretch{{1}}

       \begin{{center}}

       \textbf{{QUYẾT ĐỊNH\\Về việc cấp Giấy chứng nhận đạt Thực hành tốt cơ sở bán lẻ thuốc cho\\{dsqd["ten qt-nt"].iloc[0]}\\}}

       \rule[0.5\baselineskip]{{.4\linewidth}}{{.5pt}}

       \textbf{{GIÁM ĐỐC SỞ Y TẾ}}

       \end{{center}}\par
       \vspace{{-0.2cm}}
       \setstretch{{1.2}}

       \textit{{Căn cứ Luật Dược năm 2016; 

Căn cứ Luật sửa đổi, bổ sung một số điều của Luật Dược 2024;}}

       \textit{{Căn cứ Nghị định số 54/2017/NĐ-CP ngày 08/5/2017 của Chính phủ quy định chi tiết một số điều và biện pháp 
       thi hành Luật Dược;}}

       \textit{{Căn cứ Nghị định số 155/2018/NĐ-CP ngày 12/11/2018 của Chính phủ sửa đổi, bổ sung một số quy định liên 
       quan đến điều kiện đầu tư kinh doanh thuộc phạm vi quản lý nhà nước của Bộ trưởng Bộ Y tế;}}
       
       \textit{{Căn cứ Thông tư số 15/2011/TT-BYT ngày 19/4/2011 của Bộ trưởng Bộ Y tế quy định về tổ chức và hoạt động của cơ sở bán lẻ thuốc trong bệnh viện;}}   

       \textit{{Căn cứ Thông tư số 02/2018/TT-BYT ngày 22/01/2018 của Bộ trưởng Bộ Y tế quy định về Thực hành tốt cơ sở bán lẻ thuốc;}}

       \textit{{Căn cứ Thông tư số 12/2020/TT-BYT ngày 22/6/2020 của Bộ trưởng Bộ Y tế sửa đổi, bổ sung một số điều của Thông tư số 
       02/2018/TT-BYT ngày 22/01/2018 của Bộ trưởng Bộ Y tế quy định về Thực hành tốt cơ sở bán lẻ thuốc;}}
       
       

       \textit{{Căn cứ Quyết định số 50/2022/QĐ-UBND ngày 23/12/2022 của UBND tỉnh Vĩnh Phúc quy định vị trí, chức năng, 
       nhiệm vụ, quyền hạn và cơ cấu tổ chức của Sở Y tế tỉnh Phú Thọ;}}

       \textit{{Theo đề nghị của Trưởng phòng Nghiệp vụ dược Sở Y tế.}}

       \begin{{center}}
       \textbf{{QUYẾT ĐỊNH:}}
       \end{{center}}




       \textbf{{Điều 1.}} Cấp lần đầu Giấy chứng nhận đạt Thực hành tốt cơ sở bán lẻ thuốc cho {dsqd["ten qt-nt"].iloc[0]}, chi tiết theo danh sách đính kèm Quyết định.

       Giấy chứng nhận đạt Thực hành tốt cơ sở bán lẻ thuốc có giá trị 03 năm kể từ ngày ký.

       \textbf{{Điều 2.}} Cơ sở có tên tại Điều 1 phải chấp hành đúng quy định của 
       pháp luật trong quá trình hoạt động kinh doanh dược và đảm bảo duy trì các điều kiện Thực hành tốt cơ sở bán lẻ 
       thuốc.

       \textbf{{Điều 3.}} Quyết định này có hiệu lực kể từ ngày ký.

       Chánh Văn phòng, Trưởng các phòng chức năng Sở Y tế, Thủ trưởng các đơn vị có liên quan, 
       Giám đốc {co_quan_chu_quan}  căn cứ Quyết định thực hiện./.


       \vspace{{10pt}}

       \setstretch{{1}}
       \noindent
       \begin{{minipage}}[t]{{0.5\textwidth}}
       \singlespacing
       \fontsize{{12pt}}{{14pt}}\selectfont \textbf{{\textit{{Nơi nhận:}}}}\\
        \fontsize{{11pt}}{{13pt}}\selectfont - Như Điều 3;\\
        - Giám đốc, các PGĐ Sở;\\
        - UBND các huyện, thành phố;\\
        - TT KSBT (đăng tải Website Sở);\\
        - Lưu: VT, NVD.\\
       \end{{minipage}}
       \begin{{minipage}}[t]{{0.5\textwidth}}\singlespacing
       \begin{{center}}
\textbf{{KT. GIÁM ĐỐC}}\\
\textbf{{PHÓ GIÁM ĐỐC}}\\
\vspace{{3.5cm}}
\textbf{{Nguyễn Đắc Ca}}\\
       \end{{center}}
       \end{{minipage}}

       \newpage

       \pagenumbering{{gobble}}
       \newgeometry{{margin=1cm}}
       \begin{{landscape}}


       \begin{{center}}
       \fontsize{{14pt}}{{0pt}}\selectfont \textbf{{DANH SÁCH CẤP GIẤY CHỨNG NHẬN ĐẠT THỰC HÀNH TỐT CƠ SỞ BÁN LẺ THUỐC (GPP)\\}}

       \textit{{(Ban hành kèm theo Quyết định số {soqd}/QĐ-SYT ngày {ngaybanhanh} của Sở Y tế tỉnh Phú Thọ)}}

       \end{{center}}

       \vspace{{-0.5cm}}

       \DefTblrTemplate{{contfoot-text}}{{default}}{{}} 
       \DefTblrTemplate{{conthead-text}}{{default}}{{}} 
       \DefTblrTemplate{{caption}}{{default}}{{}} 
       \DefTblrTemplate{{conthead}}{{default}}{{}} 
       \DefTblrTemplate{{capcont}}{{default}}{{}} 
       \SetTblrInner{{rowsep=0pt}} 

       \noindent 
      
        \begin{{longtblr}}{{width=1\linewidth,rowhead=1,rowhead=2,rowhead=3,hlines,vlines,
       colspec={{X[0.6,c]X[3,c] X[4,c]X[2.2,c]X[1.8,c] X[1.2,c]X[1.3,c] X[1.4,c] X[1.2,c] X[0.8,c] X[1.1,c] X[1.1,c]X[2,c]}},
      colsep=1pt,
      rowsep=1pt,
      rows={{font=\scriptsize,m,c}},
      row{{1,2,3}}={{font=\scriptsize\bfseries}}}}
      \SetCell[r=2]{{c}} STT & \SetCell[r=2]{{c}} Tên cơ sở    &  \SetCell[r=2]{{c}} Địa điểm kinh doanh                                                                                          & \SetCell[r=2]{{c}} Người phụ trách chuyên môn & \SetCell[r=2]{{c}} cmnd/CCCD người phụ trách chuyên môn & \SetCell[c=3]{{c}} {{Chứng chỉ hành nghề dược của                                                               \\người phụ trách chuyên môn}}&& &\SetCell[r=2]{{c}} Trình độ chuyên môn & \SetCell[c=3]{{c}} {{GPP}} &&&\SetCell[r=2]{{c}} loai cap \\
                           &                                                                &                                                                                                                       &                                             &                                                       & Số                                             & Ngày cấp & Nơi cấp &    & Số & Ngày cấp & Ngày hết hạn &    \\
      1                    & 2                             & 3                                      & 4                                                                                                                     & 5                                           & 6                                                     & 7                                              & 8        & 9       & 10 & 11 & 12       & 13           \\

        {clip} 
        \end{{longtblr}}

       \vspace{{-0.5cm}}

       \fontsize{{10pt}}{{0pt}}\selectfont Danh sách này có {socs} cơ sở

       \end{{landscape}}
       \end{{document}}"""
tieude = "QUYẾT ĐỊNH: Về việc cấp Giấy chứng nhận đạt Thực hành tốt cơ sở bán lẻ thuốc cho Nhà thuốc Bệnh viện đa khoa tỉnh Vĩnh Phúc"

auto_text_to_ioffice(tieude, "QĐ", 0, text)
