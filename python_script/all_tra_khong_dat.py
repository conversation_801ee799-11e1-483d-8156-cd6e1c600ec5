import os
import sys
import time

import pandas as pd
from god_class import (
    insert_stt,
    is_khong_dat,
    show_message,
    upload_file_dialog,
)
from playwright_class import DichVuCong
from god_class import send_notification
from loguru import logger

os.chdir("/home/<USER>/Dropbox/hnd/csv_source")
global DF_NAME
DF_NAME = pd.read_csv("name.csv", dtype=str)


def tao_tu_dien_ma_ho_so_va_duong_dan_pdf(df, thu_muc_pdf):
    # Chuyển đổi cột 'van de hs' thành chuỗi và loại bỏ các giá trị NaN
    df["van de hs"] = df["van de hs"].astype(str).replace("nan", "")

    # Tạo từ điển với key là 'ma ho so' và value là 'van de hs'
    tu_dien_ma_ho_so_van_de = dict(zip(df["ma ho so"], df["van de hs"]))

    # Lấy danh sách tên tất cả các file PDF trong thư mục (không bao gồm đường dẫn)
    danh_sach_pdf = [f for f in os.listdir(thu_muc_pdf) if f.endswith(".pdf")]

    # Tạo từ điển cho 'THẨM ĐỊNH' và 'THẨM ĐỊNH THỰC TẾ'
    tu_dien_tham_dinh = {}
    tu_dien_tham_dinh_thuc_te = {}

    for ma_ho_so, van_de in tu_dien_ma_ho_so_van_de.items():
        if not van_de:
            continue
        for pdf_file in danh_sach_pdf:
            if van_de.replace("0-", "") in pdf_file and van_de.startswith("0-"):
                if (
                    df.loc[df["ma ho so"] == ma_ho_so, "trang thai"].iloc[0]
                    == "THẨM ĐỊNH"
                ):
                    tu_dien_tham_dinh[ma_ho_so] = pdf_file
                elif (
                    df.loc[df["ma ho so"] == ma_ho_so, "trang thai"].iloc[0]
                    == "THẨM ĐỊNH THỰC TẾ"
                ):
                    tu_dien_tham_dinh_thuc_te[ma_ho_so] = pdf_file
                break  # Tìm thấy file phù hợp, chuyển sang mã hồ sơ tiếp theo

    return tu_dien_tham_dinh, tu_dien_tham_dinh_thuc_te


# Sử dụng hàm
thu_muc_pdf = "/home/<USER>/Pcloud_ssd/Pcloud"
tu_dien_tham_dinh, tu_dien_tham_dinh_thuc_te = tao_tu_dien_ma_ho_so_va_duong_dan_pdf(
    DF_NAME, thu_muc_pdf
)
# Kiểm tra xem từ điển có rỗng không
if not tu_dien_tham_dinh and not tu_dien_tham_dinh_thuc_te:
    show_message(
        "THONG BAO",
        "không tìm thấy file pdf tb không đạt, xem lại iso number\n\nHOẶC ĐÃ TRẢ HẾT KQ KHÔNG ĐẠT",
    )
    sys.exit()
if tu_dien_tham_dinh:
    ds_chuhoso = ""
    for ma_ho_so, file_tb in tu_dien_tham_dinh.items():
        chuhoso = DF_NAME.loc[DF_NAME["ma ho so"] == ma_ho_so, "ten nguoi ptcm"].iloc[0]
        ds_chuhoso += f"{ma_ho_so} - {chuhoso}\n"
    send_notification(
        f"chuẩn bị trả không đạt {len(tu_dien_tham_dinh)} hồ sơ TD\n: {ds_chuhoso}"
    )
if tu_dien_tham_dinh_thuc_te:
    ds_chuhoso = ""
    for ma_ho_so, file_tb in tu_dien_tham_dinh_thuc_te.items():
        chuhoso = DF_NAME.loc[DF_NAME["ma ho so"] == ma_ho_so, "ten qt-nt"].iloc[0]
        ds_chuhoso += f"{ma_ho_so} - {chuhoso}\n"
    send_notification(
        f"chuẩn bị trả không đạt {len(tu_dien_tham_dinh_thuc_te)} hồ sơ TDTT\n: {ds_chuhoso}",
    )


# In kết quả để kiểm tra


class TraKhongDat(DichVuCong):
    DF_NAME.fillna("", inplace=True)

    def __init__(self, user):
        super().__init__(user)
        self.user = user
        self.df_hs_td = None
        self.df_hs_tdtt = None
        self.hs_ko_dat = DF_NAME.loc[is_khong_dat(DF_NAME)]
        if self.hs_ko_dat.empty:
            show_message("THONG BAO", "Tất cả hồ sơ đã được trả kết quả")
            sys.exit()
        self.df_da_tra = pd.read_csv("da_tra_kq.csv", dtype="str")
        self.list_pdf_files = None

    def tra_kq_khong_dat_single_hs(self, ma_ho_so, file_tb):
        self.tick_chon_single_hs(ma_ho_so)
        self.page.evaluate("window.scrollTo(0, 0)")
        self.page.once("dialog", lambda dialog: dialog.accept())
        self.page.click("span[title='Chọn hồ sơ cần thực hiện từ chối']")
        frame = self.page.frame_locator("iframe").first
        frame.locator("input#_fcuploaderykxl").click(force=True)
        time.sleep(1)
        upload_file_dialog(file_tb, Pcloud=True)
        frame.locator("div.row div:nth-child(2) div:nth-child(1) ins").click()
        frame.locator("a:has-text('Chuyên viên Văn Phòng Sở Y Tế')").last.click()
        frame_content = frame.frame_locator("iframe").first
        frame_content.locator("b:has-text('Văn thư Sở Y tế')").click()
        frame_content.locator("button#btn-canbo").click(force=True)
        frame.locator("//input[@name='update']").click()
        self.wait_for_load_done()

    def add_to_da_tra(self):
        self.df_da_tra.drop(columns=["stt"], inplace=True)
        self.hs_ko_dat = self.hs_ko_dat[self.df_da_tra.columns.tolist()]

        self.df_da_tra = pd.concat([self.df_da_tra, self.hs_ko_dat])
        insert_stt(self.df_da_tra)
        self.df_da_tra.to_csv("da_tra_kq.csv", index=False)
        DF_NAME.loc[
            DF_NAME["ma ho so"].isin(self.df_da_tra["ma ho so"].tolist()), "van de hs"
        ] = "Đã trả"
        DF_NAME.to_csv("name.csv", index=False)

    @logger.catch
    def run(self):
        super().setup()
        super().login_dvc_by_user("tungson")
        if tu_dien_tham_dinh:
            self.go_to_trangthai("TD")
            self.expand_ds()
            send_notification(
                f"chuẩn bị trả không đạt {len(tu_dien_tham_dinh)} hồ sơ TD"
            )
            tu_dien_items = list(tu_dien_tham_dinh.items())
            for index, (ma_ho_so, file_tb) in enumerate(tu_dien_items):
                self.tra_kq_khong_dat_single_hs(ma_ho_so, file_tb)
                # Không chạy expand_ds() cho item cuối cùng
                if index < len(tu_dien_items) - 1:
                    self.expand_ds()
        if tu_dien_tham_dinh_thuc_te:
            self.go_to_trangthai("TDTT")
            self.expand_ds()
            send_notification(
                f"chuẩn bị trả không đạt {len(tu_dien_tham_dinh_thuc_te)} hồ sơ TDTT"
            )
            tu_dien_items = list(tu_dien_tham_dinh_thuc_te.items())
            for index, (ma_ho_so, file_tb) in enumerate(tu_dien_items):
                self.tra_kq_khong_dat_single_hs(ma_ho_so, file_tb)
                # Không chạy expand_ds() cho item cuối cùng
                if index < len(tu_dien_items) - 1:
                    self.expand_ds()
        self.add_to_da_tra()
        self.cleanup()
        send_notification("đã trả xong hs không đạt")


dvc = TraKhongDat("tungson")
dvc.run()
