import datetime
import os
import shutil
import subprocess

import PySimpleGUI as Sg

os.chdir("/home/<USER>/Dropbox/hnd/csv_source")
now = datetime.datetime.now()
ngay = str(now.day).zfill(2)
thang = str(now.month).zfill(2) if now.month < 3 else str(now.month)
nam = now.year
layout = [
    [
        Sg.Text("loai hinh:"),
        Sg.Listbox(["Quầy thuốc", "Nhà thuốc"], key="lh", size=(15, 2)),
    ],
    [Sg.Text("NHẬP TÊN CƠ SỞ (thường):"), Sg.InputText("", key="tencs")],
    [Sg.Text("NHẬP ten nguoi ptcm (thường)"), Sg.InputText("", key="tenptcm")],
    [Sg.Text("NHẬP ngay td (tắt)"), Sg.InputText("", key="ngaytd")],
    [
        Sg.Text("NHẬP NỘI DUNG KHẮC PHỤC"),
        Sg.Multiline(size=(80, 10), autoscroll=False, key="ndkp"),
    ],
    [Sg.Text("NHẬP NGÀY"), Sg.InputText(ngay, key="ngay")],
    [Sg.Text("NHẬP thang"), Sg.InputText(thang, key="thang")],
    [Sg.Text("NHẬP nam"), Sg.InputText(nam, key="nam")],
    [Sg.Button("OK"), Sg.Button("Cancel")],
]

window = Sg.Window("Nhập liệu", layout, keep_on_top=True)

while True:
    event, values = window.read()
    if event in (None, "Cancel"):
        break
    elif event == "OK":
        break


def convert_ngay(ngay):
    if len(ngay) > 1 and "/" not in ngay:
        a = ngay[2:4] if ngay[2:4] in {"01", "02", "10", "11", "12"} else ngay[3:4]
        return f"{ngay[:2]}/{a}/{ngay[-4:]}"
    else:
        return ""


window.close()
ten_cs = values["lh"][0] + " " + values["tencs"].title()
nguoit_ptcm = values["tenptcm"].title()
ngay_td = convert_ngay(values["ngaytd"])
join = values["ndkp"].split("\n")
noi_dung_kp = ""
for index, val in enumerate(join):
    noi_dung_kp = noi_dung_kp + f"{index + 1}. {val[0].upper() + val[1:]}.\n\n"
ngay = values["ngay"]
thang = values["thang"]
nam = values["nam"]
text = rf"""\documentclass[a4paper]{{article}}

\usepackage[T5]{{fontenc}}
\nonstopmode
\usepackage{{amsmath}}
\usepackage{{graphicx}}
\usepackage[right=2cm,left=3cm,vmargin=2cm]{{geometry}}
\usepackage[fontsize=14pt]{{scrextend}}
\usepackage{{times}}
\usepackage{{parskip}}
\usepackage{{microtype}}
\setlength{{\parskip}}{{6pt}}
\usepackage{{tabularx}}
\usepackage{{tabularray}}

\usepackage{{parskip}}
\setlength{{\parskip}}{{6pt}}
\usepackage{{ulem}}
\renewcommand{{\ULdepth}}{{7pt}}
 \renewcommand{{\ULthickness}}{{0.5pt}}
\setlength{{\parindent}}{{1.27cm}}
\renewcommand{{\baselinestretch}}{{1.2}}
\begin{{document}}

\pagestyle{{empty}}

\begin{{center}}

   \bfseries

   CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM

   \uline{{Độc lập - Tự do - Hạnh phúc}}

\end{{center}}

\vspace{{0.5cm}}

\newcommand\fillin[1][3cm]{{\makebox[#1]{{\dotfill}}}}

{{\centering    \textbf{{BÁO CÁO KHẮC PHỤC}}\par}}

\vspace{{0.5cm}}

Kính gửi: Sở Y tế tỉnh Phú Thọ

Tên cơ sở bán lẻ: {ten_cs}

Tên người phụ trách chuyên môn dược: {nguoit_ptcm}

Căn cứ Biên bản đánh giá "Thực hành tốt cơ sở bán lẻ thuốc" ngày {ngay_td} của Đoàn thẩm định, Sở Y tế tỉnh Phú Thọ, cơ sở bán lẻ thuốc đã tiến hành khắc phục, sửa chữa những tồn tại ghi trong biên bản. Cụ thể:

{noi_dung_kp}

Tôi kính đề nghị Sở Y tế tỉnh Phú Thọ kiểm tra, đánh giá lại và xem xét việc công nhận cơ sở bán lẻ thuốc đạt tiêu chuẩn \textbf{{"Thực hành tốt cơ sở bán lẻ thuốc"}}

\vspace{{1cm}}




\hfill\begin{{minipage}}{{0.7\textwidth}}\singlespacing
   \begin{{center}}
      \textit{{Phú Thọ, ngày {ngay} tháng {thang} năm {nam}}}\\
      \textbf{{NGƯỜI PHỤ TRÁCH CHUYÊN MÔN}}\\
      \vspace{{3cm}}
   \end{{center}}
\end{{minipage}}

\end{{document}}"""
f = open("mylatex.tex", "w", encoding="utf-8")
f.write(text)
f.close()
try:
    subprocess.check_call(
        ["pdflatex", "mylatex.tex"]
    )
except:
    pass
subprocess.Popen(["/home/<USER>/Dropbox/hnd/other_script/open_pdf.sh", "../../STUFF/mylatex.pdf"])
shutil.copy("../../STUFF/mylatex.pdf", "/home/<USER>/Dropbox/bckp.pdf")
