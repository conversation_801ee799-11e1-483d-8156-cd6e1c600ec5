import os
import subprocess
import logging

# <PERSON><PERSON><PERSON><PERSON> lập logging
logging.basicConfig(
    filename="/home/<USER>/Dropbox/sang_debug.log",
    level=logging.INFO,
    format="%(asctime)s - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)


def remove_all_files(dir_path):
    for root, _, files in os.walk(dir_path):
        for file in files:
            file_path = os.path.join(root, file)
            os.remove(file_path)


remove_all_files("/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pdf_documents/kq")


def run_script(script_path):
    try:
        subprocess.run(
            [
                "/home/<USER>/python313/bin/python",
                script_path,
            ],
            check=True,
        )
        logging.info(f"Đã hoàn thành {script_path}")
    except subprocess.CalledProcessError as e:
        logging.error(f"Lỗi khi chạy {script_path}: {e}")


def main():
    logging.info("<PERSON><PERSON><PERSON> đ<PERSON><PERSON> chạy script...")

    scripts = [
        # "/home/<USER>/Dropbox/hnd/python_script/all_vanban_da_phathanh.py",
        "/home/<USER>/Dropbox/hnd/python_script/all-cucquanlyduoc.py",
        # "/home/<USER>/Dropbox/hnd/python_script/all-lich-hop.py",
        "/home/<USER>/Dropbox/hnd/python_script/walk.py",
        # "/home/<USER>/Dropbox/hnd/python_script/all_chuyen_hs.py",
        # "/home/<USER>/Dropbox/hnd/python_script/get_van_ban_di.py",
        # "/home/<USER>/Dropbox/hnd/python_script/all_check_finished_hs.py",
        # "/home/<USER>/Dropbox/hnd/python_script/get_van_ban_den.py",
        "/home/<USER>/Dropbox/hnd/python_script/move_old_hs.py",
        # "/home/<USER>/Dropbox/hnd/python_script/all_get_all_hs.py",
    ]

    for script in scripts:
        run_script(script)

    logging.info("Tất cả các script đã chạy xong.")


if __name__ == "__main__":
    main()
