import os
import pandas as pd
from date_solid import get_current_date, get_ngay_thang_nam_now
from god_class import TextProcess, captalize_first_char, send_notification, change_xa
import pd_solid_filter as ft
import last_100_convert as lcv
from abc import ABC, abstractmethod
from csv_load_and_export import CsvLoaderFactory
from loguru import logger
from dataclasses import dataclass
from typing import List


class IUpdate(ABC):
    @abstractmethod
    def update(self, df: pd.DataFrame) -> pd.DataFrame:
        pass


# Implement class cụ thể
class DkkdNewUpdater(IUpdate):
    def __init__(self, start_value_dkkd: int, start_value_gpp: int, loai_hinh: str):
        self.start_value_dkkd = start_value_dkkd
        self.start_value_gpp = start_value_gpp
        self.loai_hinh = loai_hinh

    def update(self, df: pd.DataFrame) -> pd.DataFrame:
        mask = self._create_mask(df)
        count = mask.sum()
        df.loc[mask, "so dkkd"] = range(
            self.start_value_dkkd, self.start_value_dkkd + count
        )
        df.loc[mask, "so gpp"] = range(
            self.start_value_gpp, self.start_value_gpp + count
        )
        return df

    def _create_mask(self, df: pd.DataFrame) -> pd.Series:
        return (
            (df["thu tuc"] == "CAP GPP VA DKKD")
            & (df["loai hinh"].str.contains(self.loai_hinh))
            & (df["so dkkd"] == "")
        )


class DkkdTdtUpdater(IUpdate):
    def update(self, df: pd.DataFrame) -> pd.DataFrame:
        mask = self._create_mask(df)
        df.loc[mask, "so dkkd"] = df.loc[mask, "so dkkd cu"].astype(int)
        return df

    def _create_mask(self, df: pd.DataFrame) -> pd.Series:
        return (df["thu tuc"].str.contains("DANH GIA DUY TRI DAP UNG")) & (
            df["so dkkd cu"] != ""
        )


class DkkdSorter(IUpdate):
    def update(self, df: pd.DataFrame) -> pd.DataFrame:
        df["so dkkd"] = df["so dkkd"].astype(str)
        df = df.sort_values(by=["so dkkd"])
        return df


class BasePharmacyFormatter(IUpdate):
    def __init__(self, loai_hinh: str, prefix: str):
        self.loai_hinh = loai_hinh
        self.prefix = prefix

    def update(self, df: pd.DataFrame) -> pd.DataFrame:
        mask = df["loai hinh"].str.contains(self.loai_hinh) & ~df[
            "so dkkd"
        ].str.startswith("0")
        df.loc[mask, "so dkkd"] = self.prefix + df.loc[mask, "so dkkd"]
        return df


class NhaThuocFormatter(BasePharmacyFormatter):
    def __init__(self):
        super().__init__(loai_hinh="Nhà thuốc", prefix="")


class QuayThuocFormatter(BasePharmacyFormatter):
    def __init__(self):
        super().__init__(loai_hinh="Quầy thuốc", prefix="")


# class GppUpdater(IUpdate):
#     def update(self, df: pd.DataFrame) -> pd.DataFrame:
#         df["so gpp"] = df["so dkkd"]
#         return df


class GppDkkdFormatter(IUpdate):
    def __init__(self):
        self.formatters = [
            DkkdSorter(),
            NhaThuocFormatter(),
            QuayThuocFormatter(),
        ]

    def update(self, df: pd.DataFrame) -> pd.DataFrame:
        for formatter in self.formatters:
            df = formatter.update(df)
        return df


def replace_hanh_chinh(df_dsqd, col):
    return (
        df_dsqd[col]
        .str.replace("huyện", "")
        .str.replace("tỉnh", "")
        .str.replace("thành phố", "")
    )


class DateUpdater(IUpdate):
    def update(self, df: pd.DataFrame) -> pd.DataFrame:
        df["ngay qd"] = get_current_date()
        df["ngay cap gpp"] = get_current_date()
        ngay, thang, nam = get_ngay_thang_nam_now()
        df["ngay"] = ngay
        df["thang"] = thang
        df["nam"] = nam
        df["ngay het han gpp"] = f"{ngay}/{thang}/{int(nam) + 3}"
        return df


class PersonNameFormatter(IUpdate):
    def update(self, df: pd.DataFrame) -> pd.DataFrame:
        df["ten nguoi ptcm"] = df["ten nguoi ptcm"].str.title()
        return df


class PharmacyNameFormatter(IUpdate):
    def update(self, df: pd.DataFrame) -> pd.DataFrame:
        df["ten qt-nt"] = (
            df["ten qt-nt"]
            .str.title()
            .str.replace("Nhà Thuốc", "Nhà thuốc")
            .str.replace("Quầy Thuốc", "Quầy thuốc")
            .str.replace("Ii", "II")
            .str.replace("Htc", "HTC")
        )
        return df


class DataConvertBeforeUpdated(IUpdate):
    def __init__(self):
        self.formatters = [
            DateUpdater(),
            PersonNameFormatter(),
            PharmacyNameFormatter(),
        ]

    def update(self, df: pd.DataFrame) -> pd.DataFrame:
        for formatter in self.formatters:
            df = formatter.update(df)
        return df


class AddressFormatter(IUpdate):
    def update(self, df: pd.DataFrame) -> pd.DataFrame:
        # df["dia chi co so"] = replace_hanh_chinh(df, "dia chi co so")
        df["dia chi co so"] = df["dia chi co so"].apply(
            lambda x: captalize_first_char(x) if x else x
        )
        df["dia chi new"] = df["dia chi co so"].apply(change_xa)
        df["dia chi co so"] = (
            df["dia chi new"] + " (trước đây là: " + df["dia chi co so"] + ")"
        )

        df["noi cap cchnd"] = df["noi cap cchnd"].str.replace(
            "Sở Y tế tỉnh Phú Thọ", "Sở Y tế tỉnh Phú Thọ"
        )
        return df


class CertificateNumberFormatter(IUpdate):
    def update(self, df: pd.DataFrame) -> pd.DataFrame:
        year_now = get_ngay_thang_nam_now()[2][2:4]
        # Chuyển đổi số thành chuỗi trước khi cộng
        df["so gpp"] = df["so gpp"].astype(str) + f"-{year_now}" r"\par/GPP"
        df["so dkkd"] = df["so dkkd"].astype(str) + r"\par/ĐKKDD-PT"
        return df


class CapGppFormatter(IUpdate):
    def update(self, df: pd.DataFrame) -> pd.DataFrame:
        mask = df["thu tuc"] == "Cấp GPP"
        df.loc[mask, "so dkkd"] = ""
        df.loc[mask, "ngay qd"] = ""
        return df


class GenderFormatter(IUpdate):
    def update(self, df: pd.DataFrame) -> pd.DataFrame:
        df["gioi tinh"] = df["gioi tinh"].str.replace("1", "Nam").str.replace("0", "Nữ")
        return df


class ThuTucFormatter(IUpdate):
    def __init__(self):
        self.replacement_dict = {
            "CAP GPP VA DKKD": "Cấp GPP và GCN ĐĐKKDD",
            "CAP LAI GCN ĐĐKKDD LẦN 1 - LỖI SAI CỦA CƠ QUAN CẤP": "Cấp lại GPP và GCN ĐĐKKDD",
            "DANH GIA DUY TRI DAP UNG GPP": "Cấp GPP",
            "DANH GIA DAP UNG GPP NTBV": "Cấp GPP",
            "CAP DIEU CHINH": "Cấp GPP",
        }

    def update(self, df: pd.DataFrame) -> pd.DataFrame:
        # Tạo bản sao để tránh ảnh hưởng đến df gốc
        df = df.copy()
        # Duyệt qua từng key trong dict
        for old_text, new_text in self.replacement_dict.items():
            # Sử dụng str.contains() để tìm các hàng chứa key
            mask = df["thu tuc"].str.contains(old_text, case=True, na=False)
            # Thay thế giá trị cho các hàng thỏa mãn
            df.loc[mask, "thu tuc"] = new_text
        return df


class CchndFormatter(IUpdate):
    def update(self, df: pd.DataFrame) -> pd.DataFrame:
        df["so cchnd"] = df["so cchnd"].apply(
            lambda x: x + "/CCHND-SYT-VP" if "-" not in x else x
        )
        df["so cchnd"] = "{" + df["so cchnd"].str.replace("/CCHN", r"\\/CCHN") + "}"
        return df


class DataFormatterBase(IUpdate):
    def __init__(self, formatters: List[IUpdate]):
        self.formatters = formatters

    def update(self, df: pd.DataFrame) -> pd.DataFrame:
        for formatter in self.formatters:
            df = formatter.update(df)
        return df


class DataSorter(IUpdate):
    def update(self, df: pd.DataFrame) -> pd.DataFrame:
        df = df.copy()
        df["ngay qd"] = pd.to_datetime(
            df["ngay qd"], format="%d/%m/%Y", errors="coerce"
        )
        df["is_na"] = df["ngay qd"].isna()

        # Sắp xếp DataFrame
        df.sort_values(by=["is_na", "ngay qd"], ascending=[False, True], inplace=True)

        df["ngay qd"] = df["ngay qd"].dt.strftime("%d/%m/%Y")
        df.drop(columns=["is_na"], inplace=True)
        return df


@dataclass
class TableConfig:
    columns: list
    template_type: str


class TableConfigFactory:
    @staticmethod
    def create_config(condition: bool) -> TableConfig:
        base_columns = [
            "stt",
            "ten qt-nt",
            "ten nguoi ptcm",
            "trinh do cm",
            "so cchnd",
            "ngay cap cchnd",
            "noi cap cchnd",
            "dia chi co so",
            "so dkkd",
            "so gpp",
            "pham vi kd",
        ]

        if condition:
            columns = base_columns + ["thu tuc"]
            table_header = r"""    \begin{longtblr}{
                width = 1\linewidth,
                rowhead = 1,
                rowhead = 2,
                rowhead = 3,
                hlines,
                vlines,
                colspec = {
                    X[0.6,c] X[1.4,c] X[1.4,c] X[0.8,c] X[1,c] X[1,c] X[1,c] 
                    X[2.5,c] X[1.1,c] X[1,c] X[0.8,c] X[1,c] X[1,c] X[1.2,c]
                },
                colsep = 1pt,
                rowsep = 1pt,
                rows = {font=\scriptsize,m,c},
                row{1,2,3} = {font=\scriptsize\bfseries}
            }
            \SetCell[r=2]{c} STT & 
            \SetCell[r=2]{c} Tên cơ sở & 
            \SetCell[r=2]{c} Người chịu trách nhiệm chuyên môn dược & 
            \SetCell[r=2]{c} Trình độ chuyên môn & 
            \SetCell[c=3]{c} Chứng chỉ hành nghề dược & & & 
            \SetCell[r=2]{c} Địa điểm kinh doanh & 
            \SetCell[c=2]{c} {GCN ĐĐKKD} & & 
            \SetCell[c=3]{c} {GPP} & & & 
            \SetCell[r=2]{c} Ghi chú \\
            & & & & Số & Ngày cấp & Nơi cấp & & Số & Ngày cấp & Số & Ngày cấp & Ngày hết hạn &\\
            1 & 2 & 3 & 4 & 5 & 6 & 7 & 8 & 9 & 10 & 11 & 12 & 13 & 14 \\
            """
        else:
            columns = base_columns
            table_header = r"""
    \begin{longtblr}{
        width = 1\linewidth,
        rowhead = 1,
        rowhead = 2,
        hlines,
        vlines,
        colspec = {
        X[0.3,c] X[0.8,l] X[0.8,l] X[0.8,l] X[0.7,c] X[0.7,c] X[0.8,l]
        X[1.5,l] X[0.9,c] X[0.6,c] X[3.2,l]
        },
        colsep = 2pt,
        rowsep = 1pt,
        rows = {font=\scriptsize,m},
        row{1,2} = {font=\scriptsize\bfseries,m,c}
        }
        \SetCell[r=2]{c} STT &
        \SetCell[r=2]{c} Tên cơ sở &
        \SetCell[r=2]{c} Người chịu trách nhiệm chuyên môn về dược của cơ sở &
        \SetCell[r=2]{c} Trình độ chuyên môn &
        \SetCell[c=3]{c} Chứng chỉ hành nghề dược & & &
        \SetCell[r=2]{c} Địa điểm kinh doanh &
        \SetCell[r=2]{c} {GCN ĐĐKKD} &
        \SetCell[r=2]{c} {GPP} & \SetCell[r=2]{c} Phạm vi kinh doanh \\
        & & & & Số & Ngày cấp & Nơi cấp & & & & \\
            """
        return TableConfig(columns=columns, template_type=table_header)


class ModifyTableColumn(IUpdate):
    def __init__(self):
        self.config = None

    def update(self, df: pd.DataFrame) -> pd.DataFrame:
        from god_class import insert_stt

        insert_stt(df)

        # ten la ten sau khi da replace o dong truoc
        condition = (df["thu tuc"] == "Cấp GPP").any()

        self.config = TableConfigFactory.create_config(condition)
        df["ngay gpp"] = df["ngay qd"]
        return df[self.config.columns]

    def get_values(self) -> str:
        if self.config is None:
            raise ValueError("Phải gọi update() trước khi lấy template_type")
        return self.config.template_type


class IExtractor(ABC):
    @abstractmethod
    def extract_values(self, df: pd.DataFrame):
        pass


class ListExtractor(IExtractor):
    def extract_values(self, df: pd.DataFrame) -> pd.DataFrame:
        try:
            if df.empty:
                logger.warning("DataFrame trống, không có dữ liệu để trích xuất")
                return ""
            ds = df.astype(str).apply("&".join, axis=1)
            clip = ds.str.cat(sep=r"\\" + "\n") + r"\\"
            return clip
        except Exception as e:
            logger.error(f"Lỗi khi trích xuất giá trị từ DataFrame: {e}")
            return ""


class DictExtractor(IExtractor):
    def extract_values(self, df: pd.DataFrame) -> dict:
        values = {}
        for col in df.columns.unique():
            series = (
                df[col].iloc[:, 0] if isinstance(df[col], pd.DataFrame) else df[col]
            )
            if series.nunique() == 1:
                values[col] = series.iloc[0]
        return values


# TODO From here


class GetDsqdAndInsertDkkd:
    def __init__(self):
        self.data_loader = CsvLoaderFactory.create_fillna_loader()

    def load_dk_and_filter_dsqd(self):
        df_dkkd = self.data_loader.load_df("dkkd")
        filter_conditions = [ft.DatHoSoCondition(), ft.EmptySoQdCondition()]
        filter = ft.FilterImpl(filter_conditions)
        return df_dkkd, filter.filter_df(df_dkkd)


class SetSoDkkd:
    def set_so_dkkd(self, df_dkkd, dsqd):
        max_dkkd = 3297
        max_gpp = 464
        updaters = [
            DkkdNewUpdater(max_dkkd + 1, max_gpp + 1, "thuốc"),
            DkkdTdtUpdater(),
            GppDkkdFormatter(),
        ]

        for updater in updaters:
            dsqd = updater.update(dsqd)

        return dsqd


class DataFormatter:
    def format_data(self, df_dkkd, dsqd):
        converter = DataConvertBeforeUpdated()
        dsqd = converter.update(dsqd)

        df_dkkd.update(dsqd)
        sorter = DataSorter()
        df_dkkd = sorter.update(df_dkkd)
        df_dkkd.to_csv("dkkd.csv", index=False)

        formatter = DataFormatterBase(
            [
                AddressFormatter(),
                CertificateNumberFormatter(),
                GenderFormatter(),
                ThuTucFormatter(),
                CchndFormatter(),
            ]
        )
        return formatter.update(dsqd)


class ProcessBeforeCompile:
    def format_before_compile_lt(self, dsqd):
        # Format trước khi modify columns
        table = ModifyTableColumn()
        dsqd = table.update(dsqd)
        header = table.get_values()
        if "thu tuc" in dsqd.columns:
            format_before_compiler = DataFormatterBase([CapGppFormatter()])
            dsqd = format_before_compiler.update(dsqd)

        # Sau đó mới modify columns

        return dsqd, header


class ValuesUpdate:
    def update_values(self, dsqd, header):
        try:
            if dsqd.empty:
                logger.warning("DataFrame dsqd trống, không có dữ liệu để xử lý")
                # Trả về các giá trị mặc định khi không có dữ liệu
                values = {
                    "socs": "00",
                    "soqd": r"\hspace{1.5cm}",
                    "header": header,
                    "clip": "",
                }
                values["ngay"], values["thang"], values["nam"] = (
                    get_ngay_thang_nam_now()
                )
                return values

            values_extractor = DictExtractor()
            values = values_extractor.extract_values(dsqd)

            values.update(
                {
                    "socs": str(len(dsqd)).zfill(2),
                    "soqd": r"\hspace{1.5cm}",
                    "header": header,
                }
            )

            values["ngay"], values["thang"], values["nam"] = get_ngay_thang_nam_now()

            list_extractor = ListExtractor()
            values["clip"] = list_extractor.extract_values(dsqd)

            return values
        except Exception as e:
            logger.error(f"Lỗi khi cập nhật giá trị từ DataFrame: {e}")
            # Trả về các giá trị mặc định khi có lỗi
            values = {
                "socs": "00",
                "soqd": r"\hspace{1.5cm}",
                "header": header,
                "clip": "",
            }
            values["ngay"], values["thang"], values["nam"] = get_ngay_thang_nam_now()
            return values


class LatexProcess:
    def process_latex(self, values):
        text_processor = TextProcess(os.path.basename(__file__).replace(".py", ""))
        text_processor.format_text(values)
        text_processor.auto_day_van_ban(
            rf"QUYẾT ĐỊNH Về việc cấp Giấy chứng nhận đạt Thực hành tốt cơ sở bán lẻ thuốc và Giấy chứng nhận đủ điều kiện kinh doanh dược ngày {values['ngay']}/{values['thang']}/{values['nam']}",
            "QĐ",
            0,
        )


@logger.catch
def main():
    try:
        get_dsqd_and_insert = GetDsqdAndInsertDkkd()
        set_so_dkkd = SetSoDkkd()
        data_formatter = DataFormatter()
        process_before_compile = ProcessBeforeCompile()
        value_extractor = ValuesUpdate()
        latex_process = LatexProcess()

        # Thực thi quy trình
        df_dkkd, dsqd = get_dsqd_and_insert.load_dk_and_filter_dsqd()

        if dsqd.empty:
            send_notification(
                "Không có dữ liệu thỏa mãn các điều kiện lọc. Vui lòng kiểm tra lại dữ liệu đầu vào.",
                True,
            )
            logger.warning(
                "Không có dữ liệu thỏa mãn các điều kiện lọc. Vui lòng kiểm tra lại dữ liệu đầu vào."
            )

            return

        dsqd = set_so_dkkd.set_so_dkkd(df_dkkd, dsqd)
        dsqd = data_formatter.format_data(df_dkkd, dsqd)

        dsqd, header = process_before_compile.format_before_compile_lt(dsqd)
        values = value_extractor.update_values(dsqd, header)
        latex_process.process_latex(values)

        logger.success("Quá trình xử lý hoàn tất thành công!")
    except Exception as e:
        logger.error(f"Lỗi trong quá trình xử lý: {e}")
        raise


if __name__ == "__main__":
    main()
