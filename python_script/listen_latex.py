import pynput
import subprocess
import os
from god_class import send_notification
import re
from pynput import keyboard

last_keys = []
c_commands = re.compile(r"^c(\d*[wW]|iw|\d*j|\d*k|G|gg|c)$")
ct_cf_command = re.compile(r"^c[tf].$")
d_commands = re.compile(r"^d([tf].|(\d*[wW]|iw|\d*j|\d*k|G|gg|d))$")


def on_press(key):
    global last_keys
    try:
        if key.char.lower() in ["i", "a", "o", "s"]:
            print(f"Đã nhấn phím {key.char}!")
            execute_command()
            last_keys = []
        elif key.char.lower() in ["c", "d"]:
            last_keys.append(key.char.lower())
            if len(last_keys) == 2 and last_keys == ["c", "c"]:
                print("da nhan lệnh cc!")
                execute_command()
                last_keys = []
        elif last_keys and last_keys[0] in ["c", "d"]:
            last_keys.append(key.char)
            command = "".join(last_keys)
            if (
                last_keys[0] == "c"
                and (c_commands.match(command) or ct_cf_command.match(command))
            ) or (last_keys[0] == "d" and d_commands.match(command)):
                print(f"da nhan lệnh {command}!")
                execute_command()
                last_keys = []
            elif len(last_keys) > 10:  # Giới hạn độ dài để tránh tràn bộ nhớ
                last_keys = []
        else:
            last_keys.append(key.char.lower())
            if len(last_keys) > 4:
                last_keys = last_keys[-4:]
            if "".join(last_keys) == "lklk":
                print("da nhan lệnh dừng script!")
                return False  # Dừng listener
    except AttributeError:
        last_keys = []
    return True


def execute_command():
    # Thực hiện lệnh chuyển đổi IME
    subprocess.run(["/home/<USER>/Dropbox/hnd/other_script/switch_ime.sh"], shell=True)


def listen_for_keys():
    # Lưu PID vào file
    with open("/tmp/listen_latex.pid", "w") as f:
        f.write(str(os.getpid()))

    send_notification("Bắt đầu lắng nghe phím...")

    with keyboard.Listener(on_press=on_press) as listener:
        try:
            listener.join()
        except KeyboardInterrupt:
            pass
        finally:
            # Xóa file PID khi kết thúc
            if os.path.exists("/tmp/listen_latex.pid"):
                os.remove("/tmp/listen_latex.pid")
            send_notification("Đã dừng lắng nghe phím.")


if __name__ == "__main__":
    listen_for_keys()
