# type: ignore
from datetime import datetime, timedelta
import os
import re
import sys

import pandas as pd
from PyQt5 import QtCore, QtGui, QtWidgets
from PyQt5.QtGui import QBrush, QColor
from PyQt5.QtWidgets import QAbstractItemView, QTableWidgetItem, QInputDialog
from loguru import logger

from all_pickle import save_last_dict
from god_class import (
    TextProcess,
    convert_unix_style,
    export_name_csv,
    lower_first_char,
    get_dict_from_index_df,
    run_python_313,
    send_notification,
    show_message,
    change_xa,
    convert_ngay,
    close_pyqt_window,
    get_current_date,
    insert_stt,
    get_lastest_file,
)

from top_most_get_text import input_dialog


close_pyqt_window("PPPP-QLHS-PPPP")
os.chdir("/home/<USER>/Dropbox/hnd/csv_source")


def convert_dia_chi_thuong_tru(value):
    return value[::-1].split(",")[2][::-1]


def replace_dkkd(value):
    if "VP-" not in value:
        return "{" + value.replace(r"/ĐKKDD-VP", "") + r"\\/ĐKKDD-VP}"
    else:
        return value


def replace_patterns(text, pattern_mapping):
    for pattern, replacement in pattern_mapping.items():
        text = re.sub(pattern, replacement, text)
    return text


def custom_title(s):
    words = s.split()
    result = []
    for word in words:
        if not word.isupper():
            result.append(word.capitalize())
        else:
            result.append(word)
    return " ".join(result)


def all_or_nothing(string, var):
    if var == "00":
        texts = ""
    else:
        texts = string.format(var)
    return texts


def create_font(family, point_size, bold, weight):
    font = QtGui.QFont()
    font.setFamily(family)
    font.setPointSize(point_size)
    font.setBold(bold)
    font.setWeight(weight)
    return font


class Final(QtWidgets.QMainWindow):
    SMALL_FONT = create_font("Tahoma", 11, True, 75)
    BIG_FONT = create_font("Tahoma", 14, True, 75)

    @logger.catch
    def __init__(self):
        super().__init__()
        # Thêm shortcut cho F1
        self.shortcut_f1 = QtWidgets.QShortcut(QtGui.QKeySequence("F1"), self)
        self.shortcut_f1.activated.connect(self.reload_interface)

        self.showMaximized()
        self.center_widget = QtWidgets.QWidget(self)

        self.horizontal_widget = QtWidgets.QWidget(self.center_widget)
        self.horizontal_widget.setGeometry(QtCore.QRect(10, 20, 1870, 200))

        self.horizontal_layout = QtWidgets.QHBoxLayout(self.horizontal_widget)
        self.horizontal_layout.setContentsMargins(0, 0, 0, 0)

        self.tabs = QtWidgets.QTabWidget(self.horizontal_widget)
        self.horizontal_layout.addWidget(self.tabs)

        self.tab_active_hs = QtWidgets.QWidget()
        self.tab_thu_hoi_gpp = QtWidgets.QWidget()

        self.find = QtWidgets.QLineEdit(self.tab_active_hs)
        self.label = QtWidgets.QLabel(self.tab_active_hs)

        self.add_main_button()
        self.add_hs_search()

        self.find2 = QtWidgets.QLineEdit(self.tab_thu_hoi_gpp)
        self.label2 = QtWidgets.QLabel(self.tab_thu_hoi_gpp)
        # Cho phép label2 nhận focus và sự kiện keyboard
        self.label2.setFocusPolicy(QtCore.Qt.StrongFocus)
        # Kết nối sự kiện keyPressEvent với hàm xử lý
        self.label2.keyPressEvent = self.on_label2_key_press

        self.add_thu_hoi_button()

        self.tabs.addTab(self.tab_active_hs, "")
        self.tabs.addTab(self.tab_thu_hoi_gpp, "")
        self.tabs.setTabText(self.tabs.indexOf(self.tab_active_hs), "DANH SÁCH HS")
        self.tabs.setTabText(self.tabs.indexOf(self.tab_thu_hoi_gpp), "THU HỒI GPP")

        self.add_thuhoi_search()

        self.horizontal_layout.addWidget(self.tabs)

        self.verticalLayoutWidget = QtWidgets.QWidget(self.center_widget)
        self.verticalLayoutWidget.setGeometry(QtCore.QRect(9, 109, 1870, 900))
        self.verticalLayout = QtWidgets.QVBoxLayout(self.verticalLayoutWidget)
        self.verticalLayout.setContentsMargins(0, 0, 0, 0)

        self.tableWidget = QtWidgets.QTableWidget(self.verticalLayoutWidget)
        self.tableWidget.setSelectionMode(QAbstractItemView.SingleSelection)
        self.tableWidget.setSortingEnabled(True)
        self.tableWidget.setColumnCount(0)
        self.tableWidget.setRowCount(0)

        self.verticalLayout.addWidget(self.tableWidget)

        self.setCentralWidget(self.center_widget)
        self.tabs.setCurrentIndex(0)
        QtCore.QMetaObject.connectSlotsByName(self)
        self.load_csv()

    def set_columns_width(self):
        column_widths = {
            "ma ho so": 175,
            "dia chi thuong tru": 300,
            "noi dung thuc hanh": 200,
            "cong ty xac nhan": 200,
            "noi cap cmnd": 300,
            "noi tot nghiep": 300,
            "dia chi co so": 250,
            "ten nguoi ptcm": 150,
            "van de hs": 150,
            "thu tuc": 100,
            "ten qt-nt": 180,
            "dang ky pham vi": 200,
            "noi dung dieu chinh": 200,
            "co quan chu quan": 220,
            "dia chi co quan chu quan": 250,
        }

        for column_name, width in column_widths.items():
            self.tableWidget.setColumnWidth(
                self.name.columns.get_loc(column_name), width
            )

    def reload_interface(self):
        """Reload lại giao diện khi bấm F1"""
        self.load_csv()
        send_notification("Reload Final UI!")

    def tinh_ngay_lam_viec(self, row):
        start_date = row["ngay tiep nhan"]
        end_date = pd.Timestamp.now()

        # Tạo chuỗi ngày và lọc ngày làm việc
        date_range = pd.date_range(start=start_date, end=end_date, freq="D")
        working_days = date_range[date_range.dayofweek < 5]  # 0-4 = Thứ 2 đến Thứ 6

        return len(working_days)

    def creat_col_to_check(self):
        self.name["days_from_receipt"] = self.name.apply(
            self.tinh_ngay_lam_viec, axis=1
        )

    def set_day_to_gq(self):
        # cột bổ sung rỗng
        # Điều kiện 1: Hồ sơ có vấn đề (không rỗng)
        mask_co_van_de = self.name["van de hs"] != ""
        # Điều kiện 2: Không chứa ký tự "_"
        mask_khong_chua_gach_duoi = ~self.name["van de hs"].str.contains("_")
        # Điều kiện 3: Không phải là "đạt"
        mask_khong_phai_dat = self.name["van de hs"] != "đạt"
        # Điều kiện 4: Không chứa "ĐÃ YÊU CẦU BỔ SUNG" hoặc "đã ycbs"
        mask_khong_chua_tu_da = ~self.name["van de hs"].isin(
            ["ĐÃ YÊU CẦU BỔ SUNG", "đã ycbs"]
        ) & (self.name["trang thai"] != "ĐANG TẠM DỪNG")
        mask_khong_phai_gq_sau = self.name["van de hs"] != "sau"
        mask_not_urgen = self.name["bo sung"] == ""

        # Kết hợp các điều kiện
        mask_chua_gq = (
            mask_co_van_de
            & mask_khong_chua_gach_duoi
            & mask_khong_phai_dat
            & mask_khong_chua_tu_da
            & mask_khong_phai_gq_sau
            & mask_not_urgen
        )
        mask_chua_nhan = (self.name["da nhan"] == "") & (mask_not_urgen)
        mask = mask_chua_gq | mask_chua_nhan

        # Test conditions for specific ma ho so
        self.name.loc[mask, "bo sung"] = self.name.loc[mask, "days_from_receipt"].apply(
            lambda x: (
                f"{2 - x:02d} NGAY"
                if x <= 2
                else (
                    "PHAI XU LY"
                    if x < 5
                    else ("HAN CUOI 5 NGAY" if x == 5 else "QUA HAN 5 NGAY XL")
                )
            )
        )
        self.name = self.name.drop(columns=["days_from_receipt"])

    def free_done_hs(self):
        """
        mục đích để xóa bỏ thông báo các hồ sơ trong 5 ngày phải xử lý, nhưng vẫn giữ nguyên thông báo của các hồ sơ đến hạn giải quyết
        """
        # Tạo các biến mask riêng biệt
        mask1 = (self.name["van de hs"] == "") & (self.name["da nhan"] == "1")
        mask2 = self.name["van de hs"] == "đạt"
        mask3 = self.name["van de hs"] == "Đã trả"
        mask4 = (
            self.name["van de hs"]
            .str.replace("_", "")
            .str.replace("-", "")
            .str.isdigit()
        )
        mask5 = "NGAY" in self.name["bo sung"]
        mask6 = "PHAI XU LY" in self.name["bo sung"]

        # Kết hợp các mask
        mask_da_giai_quyet = (mask1 | mask2 | mask3 | mask4) & (mask5 | mask6)

        # Hàm kiểm tra xem một mã hồ sơ có thỏa mãn các điều kiện mask1-4 hay không
        def kiem_tra_ma_ho_so_da_giai_quyet(self, ma_ho_so: str) -> bool:
            """
            Kiểm tra xem một mã hồ sơ cụ thể có thỏa mãn các điều kiện đã giải quyết hay không.

            Parameters
            ----------
            ma_ho_so : str
                Mã hồ sơ cần kiểm tra

            Returns
            -------
            bool
                True nếu mã hồ sơ thỏa mãn ít nhất một trong các điều kiện đã giải quyết,
                False nếu không thỏa mãn điều kiện nào
            """
            # Lấy dòng tương ứng với mã hồ sơ
            row = self.name[self.name["ma ho so"] == ma_ho_so]

            if row.empty:
                print(f"Không tìm thấy mã hồ sơ: {ma_ho_so}")
                return False

            # Kiểm tra từng điều kiện
            dieu_kien_1 = (row["van de hs"].values[0] == "") and (
                row["da nhan"].values[0] == "1"
            )
            dieu_kien_2 = row["van de hs"].values[0] == "đạt"
            dieu_kien_3 = row["van de hs"].values[0] == "Đã trả"
            dieu_kien_4 = (
                row["van de hs"]
                .str.replace("_", "")
                .str.replace("-", "")
                .str.isdigit()
                .values[0]
            )
            dieu_kien_5 = "NGAY" in row["bo sung"].values[0]
            dieu_kien_6 = "PHAI XU LY" in row["bo sung"].values[0]

            print(f"Mã hồ sơ: {ma_ho_so}")
            print(f"Điều kiện 1: {dieu_kien_1}")
            print(f"Điều kiện 2: {dieu_kien_2}")
            print(f"Điều kiện 3: {dieu_kien_3}")
            print(f"Điều kiện 4: {dieu_kien_4}")
            print(f"Điều kiện 5: {dieu_kien_5}")
            print(f"Điều kiện 6: {dieu_kien_6}")

            # Trả về True nếu thỏa mãn ít nhất một điều kiện
            ket_qua = (dieu_kien_1 or dieu_kien_2 or dieu_kien_3 or dieu_kien_4) and (
                dieu_kien_5 or dieu_kien_6
            )
            print(f"Kết quả kiểm tra: {ket_qua}")
            return ket_qua

        # kiem_tra_ma_ho_so_da_giai_quyet(self, "H62.17-250317-0017")

        # Áp dụng mask để cập nhật giá trị
        self.name.loc[mask_da_giai_quyet, "bo sung"] = ""

    def convert_df_name(self):
        self.set_urgen_value()
        so_td = self.name["trang thai"].value_counts()
        so_urgen = self.name["bo sung"].value_counts()
        send_notification(
            f"có tổng số {len(self.name)} hồ sơ, bao gồm:\n{so_td}\n{so_urgen}"
        )

        self.name["han"] = pd.to_datetime(self.name["han"], format="%d/%m/%Y %H:%M:%S")
        self.name["ngay tiep nhan"] = pd.to_datetime(
            self.name["ngay tiep nhan"], format="%d/%m/%Y %H:%M:%S"
        )
        self.creat_col_to_check()
        self.set_day_to_gq()
        self.free_done_hs()
        self.name = self.name.sort_values(
            by=["bo sung", "da nhan", "han", "ngay tiep nhan"],
            ascending=[False, True, True, True],
            na_position="first",
        ).copy()  # Tạo bản copy mới để tránh warning
        self.name["han"] = self.name["han"].dt.strftime("%d/%m/%Y %H:%M:%S")
        self.name["ngay tiep nhan"] = self.name["ngay tiep nhan"].dt.strftime(
            "%d/%m/%Y %H:%M:%S"
        )
        self.name = self.name.reset_index(drop=True)

    def set_urgen_value(self):
        today = datetime.now().date()

        for index, row in self.name.iterrows():
            han_date = datetime.strptime(row["han"], "%d/%m/%Y %H:%M:%S").date()
            if row["trang thai"] != "ĐANG TẠM DỪNG" and "BS" not in row["trang thai"]:
                if han_date == today:
                    self.name.at[index, "bo sung"] = "TODAY"
                elif han_date == today - timedelta(days=1):
                    self.name.at[index, "bo sung"] = "QUÁ 1 NGÀY"
                elif han_date <= today - timedelta(days=2):
                    self.name.at[index, "bo sung"] = "LÀM GẤP"
        # Đếm số lượng hồ sơ cho mỗi trang thai

    def show_thong_bao(self):
        if self.thong_bao:
            show_message("THÔNG BÁO", self.thong_bao)

    def on_label2_key_press(self, event):
        if event.key() == QtCore.Qt.Key_Return or event.key() == QtCore.Qt.Key_Enter:
            self.import_cd()
        else:
            QtWidgets.QLabel.keyPressEvent(self.label2, event)

    def set_cell_value_df(self, mhs, column, value):
        self.name.loc[self.name["ma ho so"] == mhs, column] = value

    def get_index_by_mhs(self, mhs):
        # Kiểm tra xem mhs có tồn tại trong DataFrame không
        matches = self.name[self.name["ma ho so"] == mhs]
        if matches.empty:
            send_notification(
                f"Hồ sơ đã bị trả kết quả (không tìm thấy mã hồ sơ): {mhs}"
            )
            return None  # hoặc raise Exception(f"Không tìm thấy mã hồ sơ: {mhs}"
        return matches.index[0]

    def get_num_row(self):
        return self.tableWidget.rowCount()

    def get_values_at_mhs_df_name(self, mhs, column):
        return self.name[self.name["ma ho so"] == mhs][column].values[0]

    def set_global_cell_colour(self):
        for row in range(self.get_num_row()):
            for column in range(self.get_num_col()):
                item = self.tableWidget.item(row, column)
                item.setBackground(QBrush(QColor(0, 0, 0)))  # Black background
                item.setForeground(QBrush(QColor(255, 255, 0)))  #

    def set_column_colours(self):
        for intd in range(self.get_num_row()):
            if self.get_cell_text(intd) == "ĐANG TẠM DỪNG":
                self.set_cell_colour(intd, 255, 0, 0, "trang thai")
            elif self.get_cell_text(intd, "da di") == "1":
                self.set_cell_colour(intd, 128, 0, 128, "dia chi co so")
            elif self.get_cell_text(intd, "CHỜ TRẢ") == "1":
                self.set_cell_colour(intd, 133, 4, 10, "thu tuc")
            if (
                "YCBS" not in self.get_cell_text(intd, "bo sung")
                and self.get_cell_text(intd, "bo sung") != ""
            ):
                self.set_cell_colour(intd, 0, 255, 0, "bo sung", 10, 10, 10)
            if self.get_cell_text(intd, "da nhan") != "1":
                self.set_cell_colour(intd, 183, 0, 33, "ten nguoi ptcm")
            if self.get_cell_text(intd, "doi tuong") == "DN":
                self.set_cell_colour(intd, 50, 168, 162, "doi tuong")
            if self.get_cell_text(intd, "bo sung") in [
                "TODAY",
                "QUÁ 1 NGÀY",
                "LÀM GẤP",
            ]:
                self.set_cell_colour(intd, 255, 25, 0, "bo sung")

    def get_cell_text(self, intd, column_name="trang thai"):
        return self.tableWidget.item(intd, self.get_column_index(column_name)).text()

    def set_cell_colour(
        self,
        intd,
        colour1,
        colour2,
        colour3,
        column="han",
        colour4=None,
        colour5=None,
        colour6=None,
    ):
        item = self.tableWidget.item(intd, self.name.columns.get_loc(column))
        if item:
            color = QColor(colour1, colour2, colour3)
            item.setBackground(QBrush(color))
        # self.tableWidget.item(intd, self.name.columns.get_loc(column)).setBackground(QBrush(QColor(colour1,colour2,colour3)))
        if colour4 and colour5 and colour6:
            color = QColor(colour4, colour5, colour6)
            item.setForeground(QBrush(color))

    def get_column_index(self, column_name="trang thai"):
        return self.name.columns.get_loc(column_name)

    def add_thuhoi_search(self):
        self.find2.setGeometry(QtCore.QRect(210, 0, 160, 30))
        self.find2.returnPressed.connect(self.import_cd)
        self.label2.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        self.find2.textChanged.connect(self.filter_dkkd_thuhoi)

    def add_thu_hoi_button(self):
        button_dict = {"LOAD": self.load_gpp_thuhoi, "IMPORT": self.import_cd}
        self.create_buttons(button_dict, 51, self.tab_thu_hoi_gpp)

    def add_hs_search(self):
        self.find.setGeometry(QtCore.QRect(1380, 0, 150, 30))
        self.label.setAlignment(QtCore.Qt.AlignmentFlag.AlignCenter)
        self.label.setFont(Final.SMALL_FONT)
        self.label.setGeometry(QtCore.QRect(1550, 0, 300, 60))
        self.find.textChanged.connect(self.filter_by_textchange)

    @staticmethod
    def create_buttons(button_dict, offset_x, tab):
        for i, (name, method) in enumerate(button_dict.items()):
            button = QtWidgets.QPushButton(tab)
            button.setGeometry(QtCore.QRect(75 * i + offset_x, 0, 75, 51))
            button.setText(name)
            button.clicked.connect(method)

    def add_main_button(self):
        button_dict = {
            "ALL": self.filter_show_all,
            "CCHND": (
                lambda _,
                filter_text="CCHND",
                col="thu tuc",
                note="CẤP CCHND": self.all_filter(filter_text, col, note)
            ),
            "CCHND\nDAT": self.cchnd_dat,
            "da di": (
                lambda _, filter_text="1", col="da di", note="da di": self.all_filter(
                    filter_text, col, note
                )
            ),
            "CHƯA ĐI": self.chua_di_td,
            "ĐÃ TĐ\nĐẠT": self.da_td_dat,
            "CHỜ TRẢ": (
                lambda _,
                filter_text="1",
                col="CHỜ TRẢ",
                note="CHỜ TRẢ": self.all_filter(filter_text, col, note)
            ),
            "VẤN ĐỀ": self.filter_van_de,
            "CHƯA\nNHẬN": (
                lambda _,
                filter_text="1",
                col="da nhan",
                note="CHƯA NHẬN": self.all_filter_not(filter_text, col, note)
            ),
            "DA GUI\nVB": self.da_gui_vb,
            "VĐ\nSAU TĐ": self.vande_sautd,
            "ADD\nCHỜ TRẢ": self.them_chotra,
            "ADD\nda nhan": self.them_danhan,
            "SỬA VĐ": self.vande_sua,
            "KHÔNG\nĐẠT": self.tb_khong_dat,
            "bo sung\nHS": self.tb_ycbs_hs,
            "ĐI THÊM": self.di_them,
        }
        self.create_buttons(button_dict, 10, self.tab_active_hs)

    def filter_show_all(self):
        for i in range(self.get_num_row()):
            self.set_row_unhidden(i)

    def update_pyqt_item(self, row_number, column_name, new_value):
        column_index = self.name.columns.get_loc(column_name)
        self.tableWidget.setItem(
            row_number, column_index, QTableWidgetItem(str(new_value))
        )

    def load_csv(self):
        self.name = pd.read_csv(
            "/home/<USER>/Dropbox/hnd/csv_source/name.csv", dtype="str"
        )
        self.name.fillna("", inplace=True)
        self.tableWidget.setSortingEnabled(False)
        self.convert_df_name()

        self.tableWidget.setRowCount(self.name.shape[0])
        self.tableWidget.setColumnCount(self.name.shape[1])
        self.tableWidget.setHorizontalHeaderLabels(self.name.columns)

        # Load data theo thứ tự đã sort
        for idx, row in self.name.iterrows():
            for col_index, value in enumerate(row):
                table_item = QTableWidgetItem(str(value))
                self.tableWidget.setItem(idx, col_index, table_item)

        # Bật lại sorting sau khi load xong
        self.tableWidget.setSortingEnabled(True)
        self.label.setFont(Final.SMALL_FONT)
        self.set_columns_width()
        self.tableWidget.setFont(Final.SMALL_FONT)
        self.tableWidget.resizeRowsToContents()
        self.set_global_cell_colour()
        self.set_column_colours()

    def all_filter(self, filter_text, col_name, note):
        self.hide_all()
        count = 0
        for i in range(self.get_num_row()):
            item = self.get_cell_text(i, col_name)
            if filter_text.lower() in item.lower():
                self.set_row_unhidden(i)
                count += 1
        self.label.setText("Có {} hồ sơ {}!".format(count, note))

    def chua_di_td(self):
        self.hide_all()
        count = 0
        for i in range(self.get_num_row()):
            dadi = self.get_cell_text(i, "da di")
            thutuc = self.get_cell_text(i, "thu tuc")
            if dadi != "1" and "GPP" in thutuc:
                self.set_row_unhidden(i)
                count += 1
        self.label.setText("Có {} hồ sơ {}!".format(count, "chưa đi tđ"))

    def set_row_unhidden(self, i):
        self.tableWidget.setRowHidden(i, False)

    def set_row_hidden(self, i):
        self.tableWidget.setRowHidden(i, True)

    def cchnd_dat(self):
        self.hide_all()
        count = 0
        for i in range(self.get_num_row()):
            vande = self.get_cell_text(i, "van de hs")
            thutuc = self.get_cell_text(i, "thu tuc")
            if vande == "" and "CCHND" in thutuc:
                self.set_row_unhidden(i)
                count += 1
            else:
                self.set_row_hidden(i)
        self.label.setText("Có {} hồ sơ {}!".format(count, "cchnd đạt"))

    def da_td_dat(self):
        self.hide_all()
        count = 0
        for i in range(self.get_num_row()):
            vande = self.get_cell_text(i, "van de hs")
            thutuc = self.get_cell_text(i, "thu tuc")
            dadi = self.get_cell_text(i, "da di")
            if vande == "đạt" and "GPP" in thutuc and dadi == "1":
                self.set_row_unhidden(i)
                count += 1
            else:
                self.set_row_hidden(i)
        self.label.setText("Có {} hồ sơ {}!".format(count, "đã tđ đạt"))

    def all_filter_not(self, text, col_name, note):
        self.hide_all()
        count = 0
        for i in range(self.get_num_row()):
            item_text = self.get_cell_text(i, col_name)
            if text.lower() not in item_text.lower():
                self.set_row_unhidden(i)
                count += 1
            self.label.setText("Có {} hồ sơ {}!".format(count, note))

    def hide_all(self):
        for i in range(self.get_num_row()):
            self.tableWidget.setRowHidden(i, True)

    def filter_by_textchange(self, textchange):
        count = 0
        self.hide_all()
        for i in range(self.get_num_row()):
            for j in range(self.get_num_col()):
                item = self.tableWidget.item(i, j)
                if textchange.lower() in item.text().lower():
                    self.set_row_unhidden(i)
                    count += 1
                    break
        self.label.setText("Có {} hồ sơ!".format(count))

    def get_num_col(self):
        return self.tableWidget.columnCount()

    def filter_dkkd_thuhoi(self, textchange):
        self.hide_all()
        for i in range(self.get_num_row()):
            item = self.tableWidget.item(
                i, int(dsall.columns.get_loc("so dkkd"))
            ).text()
            if item.lower() == textchange.lower():
                self.set_row_unhidden(i)

    def filter_van_de(self):
        self.hide_all()
        count = 0
        for i in range(self.get_num_row()):
            if (
                self.get_cell_text(i, "van de hs")
                not in ["đạt", "Đã trả", "ĐÃ YÊU CẦU BỔ SUNG"]
                and self.get_cell_text(i, "van de hs") != ""
            ):
                self.set_row_unhidden(i)
                count += 1
        self.label.setText("Có {} hồ sơ có vấn đề!".format(count))

    def da_gui_vb(self):
        self.hide_all()
        count = 0
        for i in range(self.get_num_row()):
            if (
                self.get_cell_text(i, "van de hs")
                .replace("_", "")
                .replace(" -", "")
                .isdigit()
            ):
                self.set_row_unhidden(i)
                count += 1
        self.label.setText("Có {} hồ sơ đã gửi văn bản!".format(count))

    def vande_sua(self):
        current_row = self.get_current_row_index()
        if "GPP" in self.name.at[current_row, "thu tuc"]:
            default_text = "đạt"
        else:
            default_text = ""
        vande = input_dialog("Title", "Nhập vấn đề sửa: ", default_text)
        if not vande:
            vande = ""
        mahs = self.get_current_mhs(current_row)
        self.name.at[current_row, "van de hs"] = vande
        self.update_pyqt_item(current_row, "van de hs", vande)
        self.update_vande_csv(current_row, mahs, vande)

    def update_vande_csv(self, current_row, mahs, vande):
        if "GPP" in self.name.at[current_row, "thu tuc"]:
            df_dkkd = pd.read_csv("dkkd.csv", dtype="str", index_col="ma ho so")
            df_dkkd.at[mahs, "van de hs"] = vande
            df_dkkd.to_csv("dkkd.csv", index_label="ma ho so")
        elif "CCHND" in self.name.at[current_row, "thu tuc"]:
            df_cchnd = pd.read_csv("cchnd.csv", dtype="str", index_col="ma ho so")
            df_cchnd.at[mahs, "van de hs"] = vande
            df_cchnd.to_csv("cchnd.csv", index_label="ma ho so")
        export_name_csv(self.name)

    def get_current_mhs(self, current_row):
        return self.name.at[current_row, "ma ho so"]

    def get_current_row_index(self):
        return self.tableWidget.currentRow()

    def them_danhan(self):
        self.add("da nhan")

    def _prepare_values_dict(self, loai_vb):
        """Helper function to prepare values dictionary with common processing"""
        current_row = self.get_current_row_index()
        mhs = self.name.at[current_row, "ma ho so"]
        df_name = self.name.copy()
        df_name.set_index("ma ho so", inplace=True)

        values = get_dict_from_index_df(df_name, mhs)
        values.update(
            {
                "ong-ba": "ông" if values["gioi tinh"] == "1" else "bà",
                "ten qt-nt": values["ten qt-nt"].title().replace("Thuốc", "thuốc"),
                "ten nguoi ptcm": values["ten nguoi ptcm"].title(),
                "ma ho so": mhs,
                "thu tuc": convert_unix_style(values["thu tuc"]),
                "loai vb": loai_vb,
            }
        )

        if not values["dia chi co so"].startswith("TDP"):
            values["dia chi co so"] = (
                values["dia chi co so"][0:1].lower() + values["dia chi co so"][1:]
            )
        values["trinh do cm"] = values["trinh do cm"].lower()

        values["so cchnd"] = (
            values["so cchnd"] + "/CCHND-SYT-VP"
            if values["so cchnd"].isdigit()
            else values["so cchnd"]
        )

        return values

    def tb_khong_dat(self):
        values = self._prepare_values_dict("tkd")
        save_last_dict(values, "values_vdhs.pk")
        save_last_dict(
            ["THÔNG BÁO KẾT QUẢ", "", "TB"],
            "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pickle_file/tieude.pk",
        )
        run_python_313("project/main")

    def tb_ycbs_hs(self):
        values = self._prepare_values_dict("ycbs")
        save_last_dict(
            ["THÔNG BÁO", "", "TB"],
            "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pickle_file/tieude.pk",
        )
        save_last_dict(values, "values_vdhs.pk")
        run_python_313("project/main")

    def add(self, col_name):
        current_row = int(self.get_current_row_index())
        value = self.name.at[current_row, col_name]

        action = "ĐÃ THÊM THÀNH" if value != "1" else "ĐÃ XOÁ"
        title = f"{action} {col_name}"
        send_notification(title)
        new_value = "1" if action == "ĐÃ THÊM THÀNH" else ""
        self.name.at[current_row, col_name] = new_value
        self.update_pyqt_item(current_row, col_name, new_value)
        export_name_csv(self.name)

    def them_chotra(self):
        self.add("CHỜ TRẢ")

    def di_them(self):
        from datetime import datetime

        ngay_hien_tai = datetime.now().strftime("%d%m%Y")
        ngaytd = input_dialog("ĐI THÊM", "Nhập ngày đi thêm (TẮT)", ngay_hien_tai)
        if not ngaytd:
            return
        current_row = self.get_current_row_index()
        mahs = self.name.at[current_row, "ma ho so"]
        df_append = self.name[self.name["ma ho so"] == mahs]
        df_append["ngay td"] = convert_ngay(ngaytd)
        df_append = df_append[
            [
                "ten qt-nt",
                "ten nguoi ptcm",
                "dia chi co so",
                "ngay td",
                "ma ho so",
                "so dt chu hs",
                "ngay sinh",
            ]
        ]
        dstd = pd.read_csv("lichthamdinh.csv", dtype="str")
        dstd = pd.concat([dstd, df_append])
        dstd.drop(labels=["stt"], axis="columns", inplace=True)
        insert_stt(dstd)
        dstd.to_csv("lichthamdinh.csv", index=False)

    def vande_sautd(self):
        df_dkkd = pd.read_csv("dkkd.csv", dtype="str", index_col="ma ho so")
        df_lich_td = pd.read_csv("lichthamdinh.csv", dtype="str", index_col="ma ho so")
        ngaytd = convert_ngay(input_dialog("Title", "NHẬP NGÀY", get_current_date()[3]))
        if not ngaytd or ngaytd not in df_lich_td["ngay td"].values:
            return
        df_lich_td = df_lich_td[df_lich_td["ngay td"] == ngaytd]
        list_mhs = df_lich_td.index.tolist()

        try:
            # Kiểm tra các mã hồ sơ không tồn tại trong df_dkkd
            missing_mhs = [mhs for mhs in list_mhs if mhs not in df_dkkd.index]
            if missing_mhs:
                error_msg = f"Các mã hồ sơ sau không tồn tại trong danh sách ĐKKD:\n{', '.join(missing_mhs)}"
                send_notification(error_msg, True)
                return

            # Chỉ lấy các mã hồ sơ có trong cả hai DataFrame
            list_mhs = [mhs for mhs in list_mhs if mhs in df_dkkd.index]
            if not list_mhs:
                send_notification("Không tìm thấy hồ sơ nào trong danh sách ĐKKD!")
                return
            df_process = df_dkkd.loc[list_mhs]
        except Exception as e:
            send_notification(f"Lỗi khi xử lý dữ liệu: {str(e)}")
            return

        for index, values in df_process.iterrows():
            vande = input_dialog(
                "VẤN ĐỀ SAU THẨM ĐỊNH",
                "VẤN ĐỀ "
                + str(values["ten qt-nt"])
                + " "
                + str(values["dia chi co so"]),
                "đạt",
            )
            if not vande:
                return
            if vande == "cd":
                self.set_cell_value_df(index, "da di", "")
                self.update_pyqt_item(self.get_index_by_mhs(index), "da di", "")
                continue
            if vande == "kp":
                self.update_pyqt_item(self.get_index_by_mhs(index), "da di", "")
                continue

            self.set_cell_value_df(index, "da di", "1")
            self.update_pyqt_item(self.get_index_by_mhs(index), "da di", "1")
            values["ten qt-nt"] = values["ten qt-nt"].title().replace("Thuốc", "thuốc")
            values["ten nguoi ptcm"] = values["ten nguoi ptcm"].title()

            values["ngay td"] = ngaytd
            values["mhs"] = index

            values["dia chi co so"] = lower_first_char(values["dia chi co so"])
            values["dia chi co so"] = (
                change_xa(values["dia chi co so"])
                + " (trước đây là: "
                + values["dia chi co so"]
                + ")"
            )

            self.set_cell_value_df(index, "van de hs", vande)
            self.update_pyqt_item(self.get_index_by_mhs(index), "van de hs", vande)
            self.set_cell_value_df(index, "ngay td", ngaytd)
            self.update_pyqt_item(self.get_index_by_mhs(index), "ngay td", ngaytd)
            df_dkkd.loc[index, "van de hs"] = vande
            df_dkkd.loc[index, "ngay td"] = ngaytd
            self.set_cell_value_df(index, "da di", "1")
            self.update_pyqt_item(self.get_index_by_mhs(index), "da di", "1")
            values["ngay td"] = ngaytd
            if vande == "0":
                # TODO lấy ds các lỗi trượt.
                ds = pd.read_csv(
                    "/home/<USER>/Dropbox/hnd/csv_source/tbkd.csv", dtype="str"
                )
                ds.index += 1
                ds["STT"] = ds["Lý do"].str.split("--").str[0]
                ds["Lý do"] = ds["Lý do"].str.split("--").str[1]

                # Generate string of numbered reasons
                values["string"] = "\n".join(
                    f"{row['STT']}. {row['Lý do']}" for _, row in ds.iterrows()
                )
                values["ngay"], values["thang"], values["nam"], _ = get_current_date()

                values["sott"] = [
                    int(num)
                    for num in input_dialog(
                        "NHẬP SỐ THỨ TỰ", values["string"], ""
                    ).split(" ")
                ]
                df_temp = ds.loc[values["sott"]]
                insert_stt(df_temp)
                df_temp = df_temp[["stt", "Lý do"]]

                if len(df_temp) == 1:
                    values["lydo_full"] = r"""

                        \textbf{Lý do: }""" + df_temp.at[values["sott"][0], "Lý do"]
                    values["lydo_full"] = (
                        lower_first_char(
                            values["lydo_full"]
                            .replace("&", "")
                            .replace(r"\\", ")")
                            .replace("Khoản", " (căn cứ Khoản")
                            .replace("Điểm", "(điểm")
                        )
                        + "./."
                    )
                else:
                    values["lydo_full"] = (
                        "Lý do:\n\n"
                        + "\n\n".join(
                            f"- {row['Lý do']}" for _, row in df_temp.iterrows()
                        ).rstrip(".")
                        + "./."
                    )
                if 7 in values["sott"]:
                    values["diemso"] = QInputDialog.getText(
                        None,
                        f"{values['ten qt-nt']}",
                        values["ten qt-nt"] + " - Nhập điểm số",
                    )
                    values["lydo_full"] = values["lydo_full"].replace(
                        "diemso", values["diemso"][0]
                    )

                if values["loai hinh"] == "Nhà thuốc":
                    values["lydo_full"] = values["lydo_full"].replace("2b", "2a")

                values["mahs"] = index
                fm = TextProcess("/van_de_sau_td/khong_dat")
                name = f"THÔNG BÁO KẾT QUẢ Thẩm định điều kiện cấp Giấy chứng nhận đủ điều kiện kinh doanh dược: {values['ten nguoi ptcm']}"
                fm.format_text(values)
                fm.auto_day_van_ban(name, "TB", values["mahs"])
            elif vande == "kp":
                values["tenqtnt"] = (
                    values["ten qt-nt"].title().replace("Thuốc", "thuốc")
                )
                values["ngayht"] = convert_ngay(datetime.now().strftime("%d%m%Y"))
                values["ngay"] = values["ngayht"].split("/")[0]
                values["thang"] = values["ngayht"].split("/")[1]
                values["nam"] = values["ngayht"].split("/")[2]
                values["so cchnd"] = (
                    values["so cchnd"] + "/CCHND-SYT-VP"
                    if values["so cchnd"].isdigit()
                    else values["so cchnd"]
                )
                values["mhs"] = index
                name = f"Yêu cầu khắc phục, sửa chữa tồn tại sau thẩm định thực tế ngày {ngaytd}: {values['ten nguoi ptcm']}"
                fm = TextProcess("/van_de_sau_td/ycbs_sau_td")
                fm.format_text(values)
                fm.auto_day_van_ban_sure(name, "TB", values["mhs"], 0)
                lastest_word = get_lastest_file("/home/<USER>/Desktop", ".docx")
                number_string = lastest_word.split("-")[3]
                self.set_cell_value_df(index, "van de hs", number_string)
        df_dkkd.to_csv("dkkd.csv", index=True, index_label="ma ho so")
        export_name_csv(self.name)

    def load_gpp_thuhoi(self):
        global dsall
        dsall = pd.read_csv("du_lieu_gpp_all.csv", dtype="str")
        self.tableWidget.setRowCount(dsall.shape[0])
        self.tableWidget.setColumnCount(dsall.shape[1])
        self.tableWidget.setHorizontalHeaderLabels(dsall.columns)
        # returns pandas array object
        for row in dsall.iterrows():
            values = row[1]
            for col_index, value in enumerate(values):
                # if isinstance(value, (float, int)):
                #     value = '{0:0,.0f}'.format(value)
                table_item = QTableWidgetItem(str(value))
                self.tableWidget.setItem(row[0], col_index, table_item)
        self.tableWidget.setColumnWidth(1, 200)
        self.tableWidget.setColumnWidth(0, 100)
        self.tableWidget.setColumnWidth(3, 300)
        self.tableWidget.resizeRowsToContents()

        # từ stt cột và dòng lấy ra giá trị =>lấy tên cột theo stt cột và áp vào pandas để lấy ra cột=> cho nó vào
        # một dataframe với tên theo đó bằng loc xuất dataframe ra
        self.set_global_cell_colour()
        self.find2.setFocus()

    def import_cd(self):
        # TODO lấy  Dataframe theo dsthuhoigpp.xslx đồng thời sort lại thứ tự chuẩn
        dsfinal = pd.read_csv("dsthuhoigpp.csv", dtype="str")
        # TODO lấy ra dòng đầu tiên hiển thị trên pyqt thành dạng dict với key là tên cột (values)
        scroll_bar = self.tableWidget.verticalScrollBar()
        scroll_bar_value = scroll_bar.value()
        first_visible_row = self.tableWidget.rowAt(scroll_bar_value)
        values = {}
        for i in range(self.get_num_col()):
            column_name = self.tableWidget.horizontalHeaderItem(i).text()
            item = self.tableWidget.item(first_visible_row, i)
            if item and item.text():
                values[column_name] = item.text()
            else:
                values[column_name] = ""
        values["ly do"] = "Cơ sở xin chấm dứt hoạt động kinh doanh dược"
        values["ngay cap gpp"] = values["ngay qd"]
        # TODO ghi giá trị vào  Dataframe dsth
        dsfinal.loc[values["so dkkd"]] = values
        dsfinal.to_csv("dsthuhoigpp.csv", index=False)
        self.find2.clear()
        self.find2.setFocus()


if __name__ == "__main__":
    # Kiểm tra xem cửa sổ final.py đã tồn tại chưa
    import subprocess

    check_window = subprocess.run(
        "wmctrl -lx | awk '{print $3}' | sort | uniq",
        shell=True,
        capture_output=True,
        text=True,
    )

    if "final.py" in check_window.stdout:
        # Nếu cửa sổ đã tồn tại, focus vào nó
        subprocess.run("wmctrl -a final.py", shell=True)
    else:
        # Nếu chưa tồn tại, tạo cửa sổ mới
        send_notification("new final")
        app = QtWidgets.QApplication(sys.argv)
        ui = Final()
        ui.show()
        sys.exit(app.exec_())
