import requests

import pyperclip

BOT_API_TOKEN = pyperclip.paste()
response = requests.get(
    f"https://cool-butterfly-9ded.tungson92dkh.workers.dev//bot{BOT_API_TOKEN}/getUpdates"
)
updates = response.json()

from god_class import send_notification

seen_ids = set()
for update in updates["result"]:
    if "message" in update:
        chat_id = update["message"]["chat"]["id"]
        chat_type = update["message"]["chat"]["type"]
        if chat_id not in seen_ids:
            print(f"ID: {chat_id} (Loại: {chat_type})")
            send_notification(f"ID: {chat_id} (Loại: {chat_type})", True)
            seen_ids.add(chat_id)
    elif "channel_post" in update:
        chat_id = update["channel_post"]["chat"]["id"]
        chat_type = update["channel_post"]["chat"]["type"]
        if chat_id not in seen_ids:
            print(f"ID kênh: {chat_id} (Loại: {chat_type})")
            send_notification(f"ID kênh: {chat_id} (Loại: {chat_type})", True)
            seen_ids.add(chat_id)
