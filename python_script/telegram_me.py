import pickle
import socket
import subprocess
import time
import requests
import telepot


def save_last_dict(trichyeu, file):
    with open(file, "wb") as f:
        pickle.dump(trichyeu, f)


def load_last_dict(file):
    try:
        with open(file, "rb") as f:
            return pickle.load(f)
    except FileNotFoundError:
        return None


def send_hs_message(message):
    token = "5301034041:AAF3VQT11V-NH8Wo-KdAbCxRIzfrpxKqE4M"
    chat_id = "-4054028033"
    all_send_warp(token, chat_id, message)


def send_message_warp(token, chat_id, message):
    bot = telepot.Bot(token)
    bot.sendMessage(chat_id, message)


def send_hnd_message(message):
    token = "2143046655:AAE5iwz9KY8ofLZ_Vm3xhBrjpEyILDYzRy8"  # telegram token
    chat_id = "-1001512252982"
    send_message_warp(token, chat_id, message)


#
def send_file_normal(chat_id, bot_token, file_path):
    url = f"https://cool-butterfly-9ded.tungson92dkh.workers.dev//bot{bot_token}/sendDocument"
    files = {"document": open(file_path, "rb")}
    data = {"chat_id": chat_id}
    response = requests.post(url, files=files, data=data)
    return response.json()


def check_connection(host="api.telegram.org", port=443, timeout=5):
    try:
        socket.setdefaulttimeout(timeout)
        socket.socket(socket.AF_INET, socket.SOCK_STREAM).connect((host, port))
        return True
    except socket.error as ex:
        print(f"Network error: {ex}")
        return False


def all_send_warp(chat_id, bot_token, file_path):
    try:
        # Tắt Cloudflare Warp
        subprocess.run(["warp-cli", "disconnect"], check=True)
        print("Warp đã được tắt.")
        time.sleep(2)
        # Kiểm tra kết nối mạng
        if not check_connection():
            print("Không thể kết nối tới Telegram API. Vui lòng kiểm tra kết nối mạng.")
            return
        # Gửi file qua Telegram
        url = f"https://cool-butterfly-9ded.tungson92dkh.workers.dev//bot{bot_token}/sendDocument"
        with open(file_path, "rb") as file:
            files = {"document": file}
            data = {"chat_id": chat_id}
            response = requests.post(url, files=files, data=data)
            return response.json()
    except subprocess.CalledProcessError as e:
        print(f"Error when trying to disconnect Warp: {e}")
    finally:
        # Bật lại Cloudflare Warp
        try:
            subprocess.run(["warp-cli", "connect"], check=True)
            print("Warp đã được bật lại.")
        except subprocess.CalledProcessError as e:
            print(f"Error when trying to connect Warp: {e}")


# Ví dụ sử dụng hàm
def send_file_hnd(file_path):
    token = "2143046655:AAE5iwz9KY8ofLZ_Vm3xhBrjpEyILDYzRy8"  # telegram token
    receiver_id = "-1001512252982"
    all_send_warp(receiver_id, token, file_path)


def send_file_nowarp(file_path):
    token = "5517353700:AAGbzj1BW7Gg11wTBrXIUQEUXmLez_NodcA"  # telegram token
    receiver_id = "-1002238974030"
    send_file_normal(receiver_id, token, file_path)


def send_message_filehs(message):
    token = "5517353700:AAGbzj1BW7Gg11wTBrXIUQEUXmLez_NodcA"  # telegram token
    chat_id = "-1002238974030"
    send_message_warp(token, chat_id, message)


def send_file_lan(file_path):
    token = "5301034041:AAF3VQT11V-NH8Wo-KdAbCxRIzfrpxKqE4M"
    id = "-4054028033"
    all_send_warp(id, token, file_path)


def send_file_qlhn(file_path):
    token = "7488802978:AAEVqjw8XbYRummVLPYZfIW-sK-pnZkwnx8"  # telegram token
    receiver_id = "-1001587612405"
    all_send_warp(receiver_id, token, file_path)
