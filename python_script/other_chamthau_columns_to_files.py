import os
import pandas as pd

os.chdir("/home/<USER>/Dropbox/linux/backup/csv document")
# Tạo Dataframe df giả lập
df_traloi_xm = pd.read_csv("dm-tra-cuu.csv", dtype=str)
# Tạ<PERSON> thư mục tạm
# Tạo các tệp rỗng với tên cột của dataframe từ cột thứ 7 trở đi
for col in df_traloi_xm.columns[11:]:
    col = col.replace("/", "-").replace(" ", "-")  # 6 vì Python đếm từ 0
    os.system(f"touch {col}")