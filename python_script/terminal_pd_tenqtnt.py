import os
import sys
import glob
import pandas as pd
import unidecode

# Mã màu ANSI
YELLOW = "\033[93m"
RESET = "\033[0m"

# Tắt cảnh báo SettingWithCopyWarning
pd.options.mode.chained_assignment = None  # default='warn'

os.chdir("/home/<USER>/Dropbox/hnd/csv_source")


def format_value(key, value, tenqtnt):
    if isinstance(value, str) and unidecode.unidecode(
        tenqtnt.upper()
    ) in unidecode.unidecode(value.upper()):
        return f"'{key}': {YELLOW}'{value}'{RESET}"
    return f"'{key}': '{value}'"


def filter_rows_by_condition(file_path, tenqtnt):
    try:
        # Đọc file CSV
        df = pd.read_csv(file_path, dtype=str)

        # Kiểm tra xem cột 'ten nguoi ptcm' có tồn tại không
        if "ten qt-nt" not in df.columns:
            return

        df.fillna("", inplace=True)
        df["check"] = df["ten qt-nt"].str.upper().apply(unidecode.unidecode)

        # Lọc DataFrame dựa trên điều kiện
        filtered_df = df[
            df["check"].str.contains(unidecode.unidecode(tenqtnt.upper()), na=False)
        ]
        filtered_df.drop(columns=["check"], inplace=True)

        # In kết quả
        if not filtered_df.empty:
            print(f"\033[91m\nKết quả từ file: {file_path}\033[0m")
            for _, row in filtered_df.iterrows():
                formatted_data = [
                    format_value(key, value, tenqtnt) for key, value in row.items()
                ]
                print("{" + ", ".join(formatted_data) + "}")
                print("\033[92m" + "*" * 158 + "\033[0m")
    except Exception as e:
        print(f"Lỗi khi xử lý file {file_path}: {str(e)}")


# Xử lý đối số dòng lệnh
if len(sys.argv) < 2:
    print("Vui lòng cung cấp tên người cần tìm")
    sys.exit(1)

name = sys.argv[1]

if len(sys.argv) > 2:
    # Nếu có đối số thứ hai, tìm kiếm trong file cụ thể
    filter_rows_by_condition(sys.argv[2], name)
else:
    # Nếu không có đối số thứ hai, tìm kiếm trong tất cả các file CSV
    csv_files = glob.glob("*.csv")
    for file in csv_files:
        filter_rows_by_condition(file, name)
