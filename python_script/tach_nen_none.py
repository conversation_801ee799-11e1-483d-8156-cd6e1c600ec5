import os
import shutil
import subprocess
import sys

from all_pickle import load_pickle
from god_class import send_notification

os.chdir("/home/<USER>/anh_cchnd")


def copy_anh(mhs, path):
    image_count = 0
    path_anh = "/home/<USER>/anh_cchnd/"
    for root, _, files in os.walk(path):
        for file in files:
            if file.lower().endswith((".png", ".jpg", ".jpeg")):
                if os.path.exists(f"{mhs}.png"):
                    os.remove(f"{mhs}.png")
                image_count += 1
                input_image_path = os.path.join(root, file)
                break
    if image_count != 1:
        send_notification("co hon 1 anh, xoa di chay lai")
        sys.exit()
    shutil.copy(
        input_image_path, os.path.join(path_anh, f"{mhs}.{file.split('.')[-1]}")
    )
    subprocess.Popen(["feh", input_image_path])


path = load_pickle("/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pickle_file/hs_path.pk")
mhs, thutuc, _ = path.split("/")[-1].split("--")
path_hs_saved = path.replace("filehs", "file_hs_backup")
copy_anh(mhs, path_hs_saved)
send_notification("copy file anh goc")
