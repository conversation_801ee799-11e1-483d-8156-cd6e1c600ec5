from csv_load_and_export import CsvLoaderFactory
from god_class import (
    fzf_get_dict_from_column,
    show_message,
    TelegramSend,
    send_notification,
)
import pikepdf
import zipfile
from unidecode import unidecode
import glob
import shutil
import sys
import os
import subprocess
from pikepdf import Pdf
from typing import List, Optional

# Constants
TELEGRAM_PATH = "/home/<USER>/Downloads/AyuGram Desktop"
OUTPUT_BASE_PATH = (
    "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pdf_documents/file_hs_online"
)
DM_THUOC_NHA_THUOC = (
    "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pdf_documents/dm thuoc nha thuoc.pdf"
)
DM_THUOC_QT = (
    "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pdf_documents/dm thuoc qt.pdf"
)

# <PERSON><PERSON><PERSON> hình xử lý theo số trang
PAGE_CONFIG = {
    10: {
        "dsttb": [4],
        "sop": list(range(5, 11)),
        "don": [2],
        "tailieu_tm": 3,
        "checklist": 1,
        "dsns": None,
    },
    11: {
        "dsttb": [5],
        "sop": list(range(6, 12)),
        "don": [2],
        "tailieu_tm": 3,
        "checklist": 1,
        "dsns": [4],
    },
    22: {
        "dsttb": [14],
        "sop": [12, 10, 8, 6, 4, 2],
        "don": [20],
        "tailieu_tm": 18,  # don[0] - 2
        "checklist": -1,
        "dsns": [16],
    },
}


def page_count(file_pdf):
    with pikepdf.Pdf.open(file_pdf) as pdf:
        so_trang_file_hs = len(pdf.pages)
    return so_trang_file_hs


def extract_pages_from_input_to_output_pdf(input_path, list_page_numbers, output_path):
    with pikepdf.Pdf.open(input_path) as pdf:
        extracted_pages = pikepdf.Pdf.new()
        for page_number in list_page_numbers:
            if page_number == -1:  # nếu nhập trang -1 thì lấy trang cuối cùng
                page_number = len(pdf.pages)
            extracted_pages.pages.append(pdf.pages[page_number - 1])
        extracted_pages.save(output_path)


def zip_files(file_list, output_zip):
    """
    Nén danh sách các file đã cho thành một file zip.

    :param file_list: danh sách các đường dẫn file cần nén
    :param output_zip: đường dẫn file zip đầu ra
    """
    # Tạo một file zip mới và ghi các file vào file zip
    with zipfile.ZipFile(output_zip, "w") as zipf:
        for file in file_list:
            if os.path.isfile(file):  # Kiểm tra xem file có tồn tại không
                zipf.write(file, os.path.basename(file))
            else:
                print(f"File '{file}' không tồn tại và sẽ bị bỏ qua.")
    print(f"Đã tạo file zip '{output_zip}'.")

    # Tên file zip đầu ra/


# Danh sách các file để nén


def validate_file_hs(file_hs: str) -> int:
    """Kiểm tra và trả về số trang của file hồ sơ"""
    so_trang = page_count(file_hs)

    if so_trang == 12:
        show_message("THONG BAO", "Chưa có sơ đồ")
        sys.exit()
    if so_trang < 12:
        show_message("THONG BAO", "FILE HS KHONG TON TAI")
        sys.exit()

    return so_trang


def resize_pdf_to_a4(input_pdf_path: str) -> None:
    """
    Chuyển đổi PDF về cỡ trang A4 sử dụng ghostscript (ghi đè lên file gốc).

    Args:
        input_pdf_path: Đường dẫn file PDF cần chuyển đổi
    """
    temp_pdf_path = input_pdf_path.replace(".pdf", "_temp.pdf")

    try:
        # Sử dụng ghostscript để resize PDF về A4
        cmd = [
            "gs",
            "-sDEVICE=pdfwrite",
            "-sPAPERSIZE=a4",
            "-dFIXEDMEDIA",
            "-dPDFFitPage",
            "-dCompatibilityLevel=1.4",
            "-dNOPAUSE",
            "-dQUIET",
            "-dBATCH",
            f"-sOutputFile={temp_pdf_path}",
            input_pdf_path,
        ]

        result = subprocess.run(cmd, capture_output=True, text=True)

        if result.returncode == 0:
            # Thay thế file gốc bằng file đã resize
            shutil.move(temp_pdf_path, input_pdf_path)
        else:
            print(f"Lỗi khi resize PDF: {result.stderr}")
            # Xóa file tạm nếu có lỗi
            if os.path.exists(temp_pdf_path):
                os.remove(temp_pdf_path)

    except FileNotFoundError:
        print("Ghostscript không được cài đặt, giữ nguyên file gốc")
        # Xóa file tạm nếu có
        if os.path.exists(temp_pdf_path):
            os.remove(temp_pdf_path)


def get_open_pdf_files(software: str, chu_ky=None) -> List[str]:
    """
    Tìm tất cả các file PDF đang được mở bởi mot chuong trinh doc pdf.

    Returns:
        List[str]: Danh sách đường dẫn các file PDF đang được mở
    """
    try:
        # Tìm tất cả các process IDs (PIDs) của mupdf
        result = subprocess.run(["pgrep", software], capture_output=True, text=True)

        if result.returncode != 0:
            return []

        pids = result.stdout.strip().split("\n")
        if not pids or pids == [""]:
            return []

        pdf_files = []

        for pid in pids:
            try:
                # Tìm các file descriptor (fds) của quá trình
                fd_dir = f"/proc/{pid}/fd"
                if not os.path.exists(fd_dir):
                    continue

                for fd_name in os.listdir(fd_dir):
                    fd_path = os.path.join(fd_dir, fd_name)
                    try:
                        file_path = os.readlink(fd_path)
                        if file_path.endswith(".pdf") and os.path.isfile(file_path):
                            if file_path not in pdf_files:
                                pdf_files.append(file_path)
                    except (OSError, FileNotFoundError):
                        # File descriptor có thể không tồn tại hoặc không thể đọc
                        continue
            except (OSError, FileNotFoundError, PermissionError):
                # Process có thể không tồn tại hoặc không có quyền truy cập
                continue

        if not pdf_files:
            show_message("THONG BAO", "CHUA CO FILE CK DANG MO")
            sys.exit()

        if chu_ky:
            valid_ck_files = []
            for file_path in pdf_files:
                try:
                    so_trang_ck = page_count(file_path)
                    if so_trang_ck in [10, 11, 22]:
                        valid_ck_files.append(file_path)
                except Exception as e:
                    print(f"Lỗi khi kiểm tra file {file_path}: {e}")
                    continue

            if not valid_ck_files:
                show_message("THONG BAO", "KHONG CO FILE CK HOP LE DANG MO")
                sys.exit()

            # Sử dụng file đầu tiên tìm thấy (hoặc có thể thêm logic chọn file)
            file_ck = valid_ck_files[0]
        else:
            file_ck = pdf_files[0]

        return file_ck

    except FileNotFoundError:
        print("Lệnh pgrep không tồn tại. Vui lòng cài đặt pgrep.")
        return []
    except Exception as e:
        print(f"Lỗi khi tìm file PDF đang mở: {e}")
        return []


def get_latest_ck_file() -> str:
    """
    Lấy file chữ ký từ các file PDF đang được mở bởi mupdf và chuyển về cỡ A4.

    Returns:
        str: Đường dẫn đến file chữ ký hợp lệ

    Raises:
        SystemExit: Nếu không tìm thấy file hợp lệ
    """
    # Tìm các file PDF đang được mở
    file_ck = get_open_pdf_files("mupdf", True)

    # Chuyển file về cỡ trang A4
    resize_pdf_to_a4(file_ck)

    os.system("pkill -f mupdf")
    os.system("wmctrl -xa ayugram-desktop")

    return file_ck


def get_page_config(num_pages: int):
    """Lấy cấu hình trang theo số trang của file CK"""
    if num_pages in PAGE_CONFIG:
        return PAGE_CONFIG[num_pages]
    else:
        # Default config
        return {
            "dsttb": [14],
            "sop": [12, 10, 8, 6, 4, 2],
            "don": [18],
            "tailieu_tm": 16,  # don[0] - 2
            "checklist": -1,
            "dsns": None,
        }


def extract_checklist_from_hs(file_hs: str, so_trang: int, file_checklist: str):
    """Tách checklist từ file hồ sơ"""
    if so_trang == 14:
        extract_pages_from_input_to_output_pdf(
            file_hs, [8, 9, 10, 11, 12, 13, 14], file_checklist
        )
    elif so_trang == 13:
        extract_pages_from_input_to_output_pdf(
            file_hs, [7, 8, 9, 10, 11, 12, 13], file_checklist
        )


def process_nhan_vien_list(file_ck: str, config):
    """Xử lý danh sách nhân viên nếu có"""
    if config.get("dsns"):
        extract_pages_from_input_to_output_pdf(
            file_ck, config["dsns"], f"{OUTPUT_BASE_PATH}/ds nhan vien.pdf"
        )


def process_tai_lieu_thuyet_minh(file_hs: str, file_ck: str, config):
    """Xử lý tài liệu thuyết minh"""
    # Tách tài liệu thuyết minh từ hồ sơ
    extract_pages_from_input_to_output_pdf(
        file_hs, [2, 3, 4], f"{OUTPUT_BASE_PATH}/TLTM.pdf"
    )

    # Thay thế trang trong file CK
    replace_pages(
        file_ck,
        [config["tailieu_tm"]],
        f"{OUTPUT_BASE_PATH}/TLTM.pdf",
        [3],
        f"{OUTPUT_BASE_PATH}/TAI LIEU THUYET MINH.pdf",
    )
    os.remove(f"{OUTPUT_BASE_PATH}/TLTM.pdf")


def process_don(file_ck: str, config):
    """Xử lý đơn"""
    extract_pages_from_input_to_output_pdf(
        file_ck, config["don"], f"{OUTPUT_BASE_PATH}/DON.pdf"
    )


def process_ds_trang_thiet_bi(file_ck: str, config):
    """Xử lý danh sách trang thiết bị"""
    extract_pages_from_input_to_output_pdf(
        file_ck, config["dsttb"], f"{OUTPUT_BASE_PATH}/ds trang thiet bi.pdf"
    )


def process_so_do(file_hs: str):
    """Xử lý sơ đồ"""
    extract_pages_from_input_to_output_pdf(
        file_hs,
        [6],  # file_hs_numsodo
        f"{OUTPUT_BASE_PATH}/so do.pdf",
    )


def process_sop(file_ck: str, file_sop: str, config):
    """Xử lý SOP"""
    destination_page_numbers = [2, 8, 17, 23, 30, 42]
    replace_pages(
        file_ck,
        config["sop"],
        file_sop,
        destination_page_numbers,
        f"{OUTPUT_BASE_PATH}/SOP.pdf",
    )


def process_checklist(file_ck: str, file_checklist: str, config):
    """Xử lý checklist"""
    replace_pages(
        file_ck,
        [config["checklist"]],
        file_checklist,
        [-1],
        f"{OUTPUT_BASE_PATH}/CHECKLIST.pdf",
    )


def copy_dm_thuoc(loai_hinh: str):
    """Copy danh mục thuốc theo loại hình"""
    source_path = DM_THUOC_NHA_THUOC if loai_hinh == "Nhà thuốc" else DM_THUOC_QT
    destination_path = f"{OUTPUT_BASE_PATH}/dm thuoc.pdf"
    shutil.copy(source_path, destination_path)


def ghep_pdf(values):
    """Hàm chính để ghép PDF - đã được tối ưu"""
    # Khởi tạo tên file
    file_hs = f"hs-{values['id_column']}.pdf"
    file_sop = f"sop-{values['id_column']}.pdf"
    file_checklist = f"check-{values['id_column']}.pdf"

    # Validate file hồ sơ
    so_trang_file_hs = validate_file_hs(file_hs)

    # Tách checklist từ file hồ sơ nếu cần
    extract_checklist_from_hs(file_hs, so_trang_file_hs, file_checklist)

    # Lấy file chữ ký mới nhất
    file_ck = get_latest_ck_file()

    # Lấy cấu hình xử lý theo số trang
    numpage_file_ck = page_count(file_ck)
    config = get_page_config(numpage_file_ck)

    # Xử lý các phần của PDF
    process_nhan_vien_list(file_ck, config)
    process_tai_lieu_thuyet_minh(file_hs, file_ck, config)
    process_don(file_ck, config)
    process_ds_trang_thiet_bi(file_ck, config)
    process_so_do(file_hs)
    process_sop(file_ck, file_sop, config)
    process_checklist(file_ck, file_checklist, config)
    copy_dm_thuoc(values["loai hinh"])


def replace_pages(
    pdf_source,
    list_source_pages,
    pdf_destination,
    list_destination_pages,
    output_pdf_merged,
):
    with (
        pikepdf.Pdf.open(pdf_source) as source_pdf,
        pikepdf.Pdf.open(pdf_destination) as destination_pdf,
    ):
        source_pages = []
        for page_number in list_source_pages:
            if page_number == -1:
                page_number = len(
                    source_pdf.pages
                )  # Set to the last page if -1 is given
            try:
                source_pages.append(source_pdf.pages[page_number - 1])
            except IndexError:
                print(f"Error accessing page {page_number} in source PDF.")
                continue

        for i, destination_pages_number in enumerate(list_destination_pages):
            if destination_pages_number == -1:
                destination_pages_number = len(
                    destination_pdf.pages
                )  # Set to the last page if -1 is given
            try:
                print(
                    "xóa bỏ trang",
                    destination_pages_number - 1,
                    "trong file",
                    pdf_destination,
                )
                del destination_pdf.pages[destination_pages_number - 1]
                destination_pdf.pages.insert(
                    destination_pages_number - 1, source_pages[i]
                )
            except IndexError:
                print(
                    f"Error replacing page {destination_pages_number} in destination PDF."
                )
                continue

        destination_pdf.save(output_pdf_merged)


def send_file_telegram(values):
    hashtag = unidecode(values["ten qt-nt"].split("-")[0]).replace(" ", "").lower()
    output_zip_file = rf"/home/<USER>/Dropbox/{hashtag}.zip"

    files_to_zip = [
        "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pdf_documents/file_hs_online/dm thuoc.pdf",
        "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pdf_documents/file_hs_online/DON.pdf",
        "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pdf_documents/file_hs_online/ds trang thiet bi.pdf",
        "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pdf_documents/file_hs_online/so do.pdf",
        "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pdf_documents/file_hs_online/SOP.pdf",
        "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pdf_documents/file_hs_online/TAI LIEU THUYET MINH.pdf",
        "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pdf_documents/file_hs_online/CHECKLIST.pdf",
    ]

    zip_files(files_to_zip, output_zip_file)

    telegram = TelegramSend("hs")
    telegram.send_file_warp(output_zip_file)
    telegram.send_message_warp(f"#{hashtag}")
    os.system("wmctrl -xa Telegram-Desktop")

    send_notification("ĐÃ XONG")


if __name__ == "__main__":
    send_notification("ghép ck")
    df = CsvLoaderFactory.create_fillna_loader().load_df("co_so_ban_le", "id_column")
    mask = df["da_ghep"] != "1"
    df2 = df[mask]
    values = fzf_get_dict_from_column(df2)
    os.chdir("/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pdf_documents/lam_ho_so/")
    ghep_pdf(values)
    df.loc[values["id_column"], "da_ghep"] = "1"
    if values["noi nhan"] == "LAN":
        send_file_telegram(values)
    df.to_csv(
        "/home/<USER>/Dropbox/hnd/csv_source/co_so_ban_le.csv", index_label="id_column"
    )
