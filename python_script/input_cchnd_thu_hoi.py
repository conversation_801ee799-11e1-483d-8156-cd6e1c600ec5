import pandas as pd
import os
from solid_string_format import DictStringFormatter
from top_most_get_text import input_dialog
from god_class import send_notification, show_message
from god_class import get_dict_from_index_df, update_df_from_dict_by_index
from create_snippets import creat_and_get
import sys
from typing import Dict, Any


def process_single_cchnd(
    so_cchnd: str, df_thu_hoi: pd.DataFrame, df_hn: pd.DataFrame
) -> None:
    """
    Xử lý một số CCHND đơn lẻ.

    Args:
        so_cchnd: Số CCHND cần xử lý
        df_thu_hoi: DataFrame chứa danh sách CCHND đã thu hồi
        df_hn: DataFrame chứa danh sách hành nghề
    """
    if so_cchnd in df_thu_hoi.index:
        show_message("Thông báo", "Số cchnd đã thu hồi")
        return

    dict_thu_hoi = get_dict_from_index_df(df_hn, so_cchnd)
    list_key = [
        "ngay cap cchnd",
        "trinh do cm",
        "ten nguoi ptcm",
        "vi tri hanh nghe",
    ]
    list_ngay = ["ngay cap cchnd"]
    values: Dict[str, Any] = {}
    for key in list_key:
        values[key] = dict_thu_hoi[key]

    dict_thu_hoi = creat_and_get(values)
    values.update(dict_thu_hoi)
    formatter = DictStringFormatter(values)
    values = formatter.apply_date_format(list_ngay).get_result()
    values["so cchnd"] = so_cchnd
    values["ly do thu hoi"] = ""
    values["ngay qd"] = ""
    update_df_from_dict_by_index(df_thu_hoi, "dsthuhoicchnd.csv", values, so_cchnd)
    send_notification("done")


def main() -> None:
    """
    Hàm chính để chạy chương trình.
    """
    os.chdir("/home/<USER>/Dropbox/hnd/csv_source")

    # Đọc dữ liệu
    df_thu_hoi = pd.read_csv("dsthuhoicchnd.csv", dtype=str)
    df_thu_hoi.set_index("so cchnd", inplace=True)
    df_hn = pd.read_csv("dshn_duoc.csv", dtype=str)
    df_hn.set_index("so cchnd", inplace=True)

    # Hỏi số lần lặp
    try:
        so_lan_lap = int(input_dialog("Title", "Nhập số lần lặp lại", ""))
    except ValueError:
        show_message("Lỗi", "Vui lòng nhập một số nguyên")
        sys.exit(1)

    # Lặp lại theo số lần đã nhập
    for i in range(so_lan_lap):
        so_cchnd = input_dialog(
            "Title", f"Nhập số cchnd để thu hồi (lần {i + 1}/{so_lan_lap})", ""
        )
        if not so_cchnd:
            sys.exit()
        process_single_cchnd(so_cchnd, df_thu_hoi, df_hn)


if __name__ == "__main__":
    main()
