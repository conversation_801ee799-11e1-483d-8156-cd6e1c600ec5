import os

from pathlib import Path
import pikepdf
import pandas as pd

os.chdir("/home/<USER>/Dropbox/hnd/csv_source")
df = pd.read_csv("cap_so_tiep_nhan_mp.csv", dtype=str, index_col="ma ho so")
last_day = df["NGÀY CẤP SỐ TIẾP NHẬN"].iloc[-1]
list_mhs = df[df["NGÀY CẤP SỐ TIẾP NHẬN"] == last_day].index.tolist()


def split_pdf(input_pdf_path, output_dir_path, names_list):
    pdf = pikepdf.Pdf.open(input_pdf_path)
    output_dir = Path(output_dir_path)
    output_dir.mkdir(parents=True, exist_ok=True)

    total_pages = len(pdf.pages)
    file_index = 0

    for start_page in range(0, total_pages, 11):
        end_page = start_page + 11
        if end_page > total_pages:
            end_page = total_pages

        pdf_writer = pikepdf.Pdf.new()
        pdf_writer.pages.extend(pdf.pages[start_page:end_page])

        output_filename = output_dir / f"{names_list[file_index]}.pdf"
        pdf_writer.save(output_filename)
        pdf_writer.close()

        file_index += 1
        if file_index >= len(names_list):
            break


split_pdf(
    "/home/<USER>/Dropbox/pcbmp.pdf",
    "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pdf_documents/kq",
    list_mhs,
)
