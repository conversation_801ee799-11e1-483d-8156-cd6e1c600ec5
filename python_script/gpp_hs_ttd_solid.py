import os
import subprocess
import sys
from loguru import logger
import pandas as pd

from all_pickle import load_pickle
from god_class import (
    get_dict_from_index_df,
    phone_format,
    send_notification,
    update_df_name_da_nhan,
    update_df_from_dict_by_index,
    close_app,
)
from god_class import show_message
from top_most_get_text import input_dialog

from create_snippets import creat_and_get
from solid_string_format import DictStringFormatter
from hs_mission_solid import (
    CCHNDDataCollector,
    UpdatePvkd,
    check_dung_ten_and_trung_ten,
    check_and_get_ngay_cap_cchnd,
)

os.chdir("/home/<USER>/Dropbox/hnd/csv_source")
df_dshn = pd.read_csv("dshn_duoc.csv", dtype=str, index_col="so cchnd")
global df_dkkd
df_dkkd = pd.read_csv("dkkd.csv", dtype=str)
df_dkkd.set_index("so cchnd", inplace=True)
df_name = pd.read_csv("name.csv", dtype=str, index_col="ma ho so")
df_cchnd = pd.read_csv("cchnd.csv", dtype=str, index_col="so cchnd")
df_lam_ho_so = pd.read_csv("co_so_ban_le.csv", dtype=str, index_col="so cchnd")

default_dict = {
    "ten qt-nt": "",
    "dia chi co so": "",
    "ten nguoi ptcm": "",
    "gioi tinh": "0",
    "trinh do cm": "Cao đẳng dược",
    "vi tri hanh nghe": "Quầy thuốc",
    "noi cap cchnd": "Sở Y tế tỉnh Phú Thọ",
    "cmnd": "",
    "dia chi thuong tru": "",
    "ngay sinh": "",
    "noi cap cmnd": "",
    "ngay cap cmnd": "",
    "co nhan vien khong": "0",
    "pham vi kd": "default",
    "dieu kien bao quan": "0",
    "Ma 4772": "",
    "CHỮ KÝ VÀ TÊN TRONG DKKD": "",
    "co quan chu quan": "",
    "dia chi co quan chu quan": "",
    "noi tot nghiep": "",
    "ngay tot nghiep": "",
    "van de hs": "",
    "noi nhan": "HIẾU",
    "ten co so dang ky hn": "",
}


class CustomCCHNDDataCollector(CCHNDDataCollector):
    def _create_dict_from_hnd(self, dict_hnd):
        return {
            "so dt chu hs": dict_hnd.get("so dt chu hs", ""),
            "ten qt-nt": dict_hnd.get("ten qt-nt", ""),
            "dia chi co so": dict_hnd.get("dia chi co so", ""),
            "pham vi kd": dict_hnd.get("pham vi kd", "") or "default",
            "dieu kien bao quan": dict_hnd.get("dieu kien bao quan", "") or "0",
            "ten co so dang ky hn": dict_hnd.get("ten co so dang ky hn", ""),
            "co nhan vien khong": dict_hnd.get("co nhan vien khong", "0") or "0",
            "co quan chu quan": dict_hnd.get("co quan chu quan", ""),
            "dia chi co quan chu quan": dict_hnd.get("dia chi co quan chu quan", ""),
            "noi nhan": dict_hnd.get("noi nhan", "") or "HIEU",
            "van de hs": "",
            "Ma 4772": "",
            "Chủ hộ kd có phải ptcm không": "",
        }

    def _create_base_default_dict(self):
        return default_dict


# Sử dụng lớp tùy chỉnh
def collect_data(so_cchnd):
    # collector = CCHNDDataCollector(df_cchnd, df_dshn_duoc)  # Thay thế dòng này
    collector = CustomCCHNDDataCollector(df_cchnd, df_dshn)  # bằng dòng này
    return collector.collect_cchnd_data(so_cchnd)


@logger.catch
def gpp_hs_ttd(mhs, thutuc="DANH GIA DUY TRI DAP UNG GPP"):
    global df_dkkd
    values = {}
    sdt = df_name.loc[mhs, "so dt chu hs"]
    sdt = phone_format(sdt)

    so_cchnd = input_dialog("Title", "NHẬP so cchnd", "")
    if not so_cchnd:
        sys.exit()
    dict_get = get_dict_from_index_df(df_dkkd, so_cchnd)

    values["so cchnd"] = so_cchnd
    values = check_and_get_ngay_cap_cchnd(values)
    values["so dt chu hs"] = sdt

    if so_cchnd in df_dkkd.index:
        values.update({k: v for k, v in dict_get.items() if k in default_dict and v})
        additional_fields = {  # Đổi tên biến để tránh xung đột
            "Mã 4772": "",
            "Chủ hộ kd có phải ptcm không": "",
            "van de hs": "",
            "so dkkd cu": dict_get["so dkkd"],
            "ngay dkkd cu": dict_get["ngay qd"],
            "ngay gpp cu": dict_get["ngay qd"],
            "so gpp cu": dict_get["so gpp"],
        }
        # Nếu pham vi kd không có bảo quản hoặc chưa cập nhật phạm vi kinh doanh thì thêm vào
        additional_fields["pham vi kd"] = "default"
        additional_fields["dieu kien bao quan"] = "0"
        default_dict_local = additional_fields
    else:
        show_message("THÔNG BÁO", "cơ sở bán lẻ chưa có trong dữ liệu")
        sys.exit()

    values_second = creat_and_get(default_dict_local)
    values.update(values_second)
    check_dung_ten_and_trung_ten(values)

    dict_to_update = {"ma ho so": mhs, "thu tuc": thutuc}
    values.update(dict_to_update)
    values["so dkkd"] = values["so dkkd cu"]
    values["so gpp"] = values["so dkkd"]
    if "đại học" in values["trinh do cm"].lower():
        values["loai hinh"] = "Nhà thuốc"
        values["vi tri hanh nghe"] = "Nhà thuốc"
    else:
        values["loai hinh"] = "Quầy thuốc"
        values["vi tri hanh nghe"] = "Quầy thuốc"

    if "thuốc" not in values["ten qt-nt"].lower():
        values["ten qt-nt"] = values["loai hinh"] + " " + values["ten qt-nt"]

    values["trinh do tat"] = (
        values["trinh do cm"]
        .replace("Cao đẳng dược", "cao đẳng")
        .replace("Trung cấp dược", "trung cấp")
        .replace("Đại học dược", "đại học")
    )

    update_pvkd = UpdatePvkd(values)
    values = update_pvkd.update()

    list_ngay = [
        "ngay cap cchnd",
        "ngay tot nghiep",
        "ngay sinh",
        "ngay cap cmnd",
        "ngay dkkd cu",
        "ngay gpp cu",
        "ngay het han gpp",
    ]
    list_upper = ["ten nguoi ptcm", "ten qt-nt"]

    formatter = DictStringFormatter(values)
    values = formatter.apply_date_format(list_ngay).apply_upper(list_upper).get_result()

    update_df_name_da_nhan(df_name, values, mhs)
    df_dkkd = df_dkkd.reset_index(drop=False).set_index(
        "ma ho so"
    )  # Giữ lại cột so cchnd
    update_df_from_dict_by_index(df_dkkd, "dkkd.csv", values, mhs)
    update_df_from_dict_by_index(df_dshn, "dshn_duoc.csv", values, so_cchnd)
    #########################################################
    # Cập nhật ngày qd cho df_cchnd theo ngày cấp chuẩn với các cchnd cấp mà bị lệch ngày
    if so_cchnd in df_cchnd.index:
        values["ngay qd"] = values["ngay cap cchnd"]
        update_df_from_dict_by_index(df_cchnd, "cchnd.csv", values, so_cchnd)

    if "co quan chu quan" in values and len(values["co quan chu quan"]) > 2:
        send_notification(
            "XEM KỸ LẠI CHỮ KÝ VÀ CON DẤU TRONG ĐƠN ĐỀ NGHỊ, TRA CỨU pham vi kd CỦA DOANH NGHIỆP",
        )
    subprocess.run("wmctrl -xa file_hs.file_hs", shell=True)
    send_notification("xong")


if __name__ == "__main__":
    path = load_pickle(
        "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pickle_file/hs_path.pk"
    )
    mhs, _, _ = path.split("/")[-1].split("--")
    gpp_hs_ttd(mhs)
