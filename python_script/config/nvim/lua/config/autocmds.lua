-- N<PERSON><PERSON> nhập điểm, thêm vào cuối dòng
if diem ~= "" then
  local current_datetime = os.date("%d/%m/%Y lúc %H:%M:%S")
  new_line = new_line .. " -- <PERSON><PERSON><PERSON>m " .. diem .. " -- thời gian tạo: " .. current_datetime
end 

-- Autocmd cho file TOML
vim.api.nvim_create_autocmd("FileType", {
  pattern = "toml",
  callback = function()
    -- Đặt phím Enter trong insert mode để lưu và thoát
    vim.keymap.set("i", "<CR>", function()
      vim.cmd("write")
      vim.cmd("stopinsert")
      vim.cmd("q")
    end, { buffer = true, desc = "Lưu file và thoát insert mode" })
  end
})

vim.api.nvim_create_autocmd("FileType", {
  pattern = "markdown",
  callback = function()
    vim.opt_local.spell = false
    
    -- <PERSON><PERSON><PERSON> cập nhật tổng điểm và điểm trong ngày ở đầu file
    local function update_points_summary(point_to_add)
      -- <PERSON><PERSON><PERSON> ngày hiện tại để so sánh với ngày trong dòng được tích
      local today = os.date("%d/%m/%Y")
      
      -- Đọc 2 dòng đầu của file
      local line1 = vim.fn.getline(1)
      local line2 = vim.fn.getline(2)
      
      -- Trích xuất điểm hiện tại
      local current_total = tonumber(line1:match("Tổng điểm:%s*(%d+)") or "0")
      local current_daily = tonumber(line2:match("Điểm trong ngày:%s*(%d+)") or "0")
      
      -- Cập nhật điểm
      local new_total = current_total + point_to_add
      local new_daily = current_daily + point_to_add
      
      -- Cập nhật lại 2 dòng đầu
      local new_line1 = "Tổng điểm: " .. new_total
      local new_line2 = "Điểm trong ngày: " .. new_daily
      
      -- Ghi lại vào file
      vim.api.nvim_buf_set_lines(0, 0, 1, false, { new_line1 })
      vim.api.nvim_buf_set_lines(0, 1, 2, false, { new_line2 })
    end
    
    vim.keymap.set("n", "<leader><space>", function()
      local line = vim.fn.getline(".")
      local current_line = vim.fn.line(".")
      local new_line
      
      if line:match("^⬜%s") then
        -- Khi chuyển từ checkbox trống sang đã check
        new_line = line:gsub("^⬜%s", "✅ ")
        
        -- Trích xuất điểm từ dòng hiện tại nếu có
        local point = tonumber(line:match("-- Point:%s*(%d+)"))
        if point then
          -- Cập nhật tổng điểm và điểm trong ngày
          update_points_summary(point)
        end
      elseif line:match("^✅%s") then
        -- Khi chuyển từ checkbox đã check sang trống, đồng thời xóa phần điểm nếu có
        new_line = line:gsub("^✅%s", "⬜ ")
        
        -- Trích xuất điểm từ dòng hiện tại nếu có để trừ đi
        local point = tonumber(line:match("-- Point:%s*(%d+)"))
        if point then
          -- Trừ điểm khi bỏ tick
          update_points_summary(-point)
        end
      else
        -- Khi tạo mới checkbox, hiển thị hộp nhập điểm
        new_line = "⬜ " .. line
        
        -- Hiển thị hộp nhập điểm số chỉ khi tạo mới checkbox
        local diem = vim.fn.input({
          prompt = "Điểm số: ",
          default = "",
        })
        
        -- Nếu nhập điểm, thêm vào cuối dòng
        if diem ~= "" then
          local current_datetime = os.date("%d/%m/%Y at %H:%M:%S")
          new_line = new_line .. " -- Point: " .. diem .. " -- Time Create: " .. current_datetime
        end
      end
      
      vim.api.nvim_buf_set_lines(0, current_line - 1, current_line, false, { new_line })
    end, { buffer = true, desc = "Toggle checkbox with score" })
  end
}) 