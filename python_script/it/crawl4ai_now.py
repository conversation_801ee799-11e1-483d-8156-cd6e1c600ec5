import asyncio
from crawl4ai import Async<PERSON><PERSON><PERSON>raw<PERSON>, CrawlerRunConfig
from crawl4ai.deep_crawling import BFSDeepCrawlStrategy
import sys
import os

# Thêm thư mục cha vào sys.path để import god_class
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))
from top_most_get_text import input_dialog
from god_class import send_notification


async def main(url: str):
    # Tạo một instance của AsyncWebCrawler
    async with AsyncWebCrawler() as crawler:
        # Cấu hình crawl
        config = CrawlerRunConfig(
            deep_crawl_strategy=BFSDeepCrawlStrategy(
                max_depth=2,  # Độ sâu crawl (ví dụ: 3 cấp)
                include_external=False,  # Chỉ crawl các trang trong cùng domain
                max_pages=500,  # Giới hạn số trang crawl (ví dụ: 100 trang)
            ),
            verbose=True,  # Kích hoạt chế độ verbose để theo dõi quá trình crawl
            exclude_external_links=True,  # Loại bỏ các liên kết ngoại
            exclude_social_media_links=True,  # Loại bỏ các liên kết mạng xã hội
        )

        # Chạy crawl trên URL
        results = await crawler.arun(
            url=url,
            config=config,
        )

        # Kiểm tra kết quả
        if result:
            # Lưu nội dung crawl vào file txt
            with open("output.txt", "w", encoding="utf-8") as file:
                file.write(result.markdown.raw_markdown + "\n")
            send_notification("Crawl thành công và đã lưu vào file output.txt")
        else:
            send_notification("Crawl thất bại")


if __name__ == "__main__":
    # Thêm URL mặc định hoặc lấy từ command line
    url = input_dialog("nhap url", "Nhập Url cần crawl", "")
    asyncio.run(main(url))
