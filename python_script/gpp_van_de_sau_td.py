import os
import subprocess

import pandas as pd

from god_class import convert_ngay, get_current_date, insert_stt, auto_text_to_ioffice_sure, selenium_start, phancong
from god_class import show_message
from top_most_get_text import input_dialog
from loguru import logger




@logger.catch
def vande_sautd():
    df_name = pd.read_csv("name.csv", dtype="str", index_col="ma ho so")
    df_khongdat = pd.read_csv('tbkd.csv', dtype=str)
    df_dkkd = pd.read_csv("dkkd.csv", dtype="str", index_col="ma ho so")
    df_ds_tham_dinh = pd.read_csv("lichthamdinh.csv", dtype="str", index_col="ma ho so")
    ngay, thang, nam, today = get_current_date()
    ngaythamdinh = convert_ngay(input_dialog("NHẬP ngay td", "NHẬP NGÀY", today))
    list_chuyen = []
    if not ngaythamdinh:
        return
    df_ds_tham_dinh = df_ds_tham_dinh[df_ds_tham_dinh["ngay td"] == ngaythamdinh]
    list_mhs_td=df_ds_tham_dinh.index.tolist()
    df_td_final=df_dkkd.loc[list_mhs_td]
    for index, values in df_td_final.iterrows():
        vande = input_dialog("VẤN ĐỀ SAU THẨM ĐỊNH", "VẤN ĐỀ " + values["ten qt-nt"] + " " + values["dia chi co so"],
                             "đạt", )
        if not vande:
            df_name.at[index, "da di"] = ""
        else:
            df_name.at[index, "van de hs"] = vande
            df_name.at[index, "da di"] = "1"
            df_name.at[index, "ngay td"] = ngaythamdinh

            df_dkkd.at[index, "van de hs"] = vande
            df_dkkd.at[index, "ngay td"] = ngaythamdinh

            tenqtnt = values['ten qt-nt'].title().replace('Thuốc', 'thuốc')
            ten_nguoiptm = values['ten nguoi ptcm'].title()
            mahs = index
            qthaynt = values['loai hinh']
            diachi = values['dia chi co so']
            if df_name.at[index, "trang thai"] == "THẨM ĐỊNH":
                list_chuyen.append(index)  # TODO cập nhật thêm các cai da di vào listchuyen.
            if vande == "0":
                subprocess.Popen(["zathura", "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pdf_documentstbkd.pdf"])
                sott = input_dialog("SỐ THỨ TỰ", "SỐ THỨ TỰ", "")
                sott = list(map(lambda x: int(x), sott[0].split("+")))
                df_lydo = df_khongdat.loc[sott]
                insert_stt(df_lydo)
                if len(df_lydo) == 1:
                    lydo_full = (
                            r"\textbf{{Lý do: }}" + df_lydo.at[1, "Lý do"] + "./."
                    )
                    lydo_full = (
                        lydo_full.replace("&", "")
                        .replace(r"\\", ")")
                        .replace("Khoản", " (căn cứ Khoản")
                    )
                else:
                    df_lydo = df_lydo.astype(str).apply("".join, axis=1)
                    lydo = df_lydo.str.cat(sep="\n")
                    lydo_full = rf"""\vspace{{0.5\baselineskip}}                            
                    \setstretch{{1}}
                    \noindent
                    \begin{{minipage}}{{\textwidth}}
\begin{{tblr}}{{width=1\linewidth,hlines,vlines,
                    colspec={{X[0.6,c]X[4,c]X[3,c]}}, 
                    colsep=1pt,
                    rowsep=1pt,
                    rows={{font=\small,m}},row{{1}}={{font=\bfseries,c}}}}
                    STT&Lý do&Căn cứ\\
                    {lydo}
                    \end{{tblr}}
\end{{minipage}}
                    """
                if 7 in sott:
                    diemso = input_dialog("ĐIỂM SỐ", "ĐIỂM SỐ", "", )
                    lydo_full = lydo_full.replace("diemso", diemso[0])
                if qthaynt == "Nhà thuốc":
                    lydo_full = lydo_full.replace("2b", "2a")
                text = rf"""\documentclass{{article}}
\usepackage[T5]{{fontenc}}
\usepackage{{parskip}}
\nonstopmode
\usepackage[fontsize=14pt]{{scrextend}}
\usepackage[a4paper,vmargin=2cm,right=2cm,left=3cm,]{{geometry}}
\usepackage{{mathptmx}}
\usepackage{{ulem}}
\renewcommand{{\ULdepth}}{{7pt}}
\renewcommand{{\ULthickness}}{{0.5pt}}
\usepackage{{setspace}}
\usepackage{{tabularray}}
\usepackage{{indentfirst}}
\setlength{{\parindent}}{{1.27cm}}
\setlength{{\parskip}}{{0pt}}
\thispagestyle{{empty}}
\usepackage{{fancyhdr}}
\fancyhf{{}}
\fancyhead[C]{{\small\thepage}}
\renewcommand{{\headrulewidth}}{{0pt}}
\pagestyle{{fancy}}
\begin{{document}}

\setstretch{{1}}
\noindent
\begin{{minipage}}{{\textwidth}}
\begin{{tblr}}{{width=1\linewidth,
colspec={{X[1,c] X[2,c]}},
colsep=0pt,
rowsep=-3pt,
row{{2}} = {{font=\bfseries}}}}
\fontsize{{13pt}}{{0pt}}\selectfont UBND TỈNH VĨNH PHÚC & \fontsize{{13pt}}{{0pt}}\selectfont \textbf{{CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM}}\\
{{\fontsize{{13pt}}{{0pt}}\selectfont SỞ Y TẾ\\[3pt]
\rule[0.6\baselineskip]{{.14\linewidth}}{{.5pt}}}} & \fontsize{{14pt}}{{0pt}}\selectfont \uline{{Độc lập - Tự do - Hạnh phúc}}\\[12pt]
\fontsize{{14pt}}{{0pt}}\selectfont Số: \hspace{{1.5cm}}/TB-SYT & \fontsize{{14pt}}{{0pt}}\selectfont \textit{{Phú Thọ, ngày {ngay} tháng {thang} năm {nam}}}\\[6pt]
\end{{tblr}}
\end{{minipage}}

\begin{{center}}

\textbf{{THÔNG BÁO KẾT QUẢ\\Thẩm định điều kiện cấp Giấy chứng nhận đủ điều kiện kinh doanh dược}}\\
\rule[0.6\baselineskip]{{.4\linewidth}}{{.5pt}}

\end{{center}}

\noindent
\begin{{minipage}}{{\textwidth}}
\begin{{tblr}}{{width=1\linewidth,
colspec={{X[1,r]X[5,l]}},
rowsep=0pt,
colsep=2pt
}}
Kính gửi: & {tenqtnt}\\
& Địa chỉ: {diachi}.\\
\end{{tblr}}
\end{{minipage}}

\vspace{{0.5\baselineskip}}

Căn cứ Luật Dược năm 2016; 

Căn cứ Luật sửa đổi, bổ sung một số điều của Luật Dược 2024;

Căn cứ Nghị định số 163/2025/NĐ-CP ngày 29/6/2025 của Chính phủ quy định chi tiết một số điều và biện pháp tổ chức, hướng dẫn thi hành Luật Dược;

Căn cứ Thông tư số 02/2018/TT-BYT ngày 22/01/2018 của Bộ trưởng Bộ Y tế quy định về Thực hành tốt cơ sở bán lẻ thuốc;

Căn cứ hồ sơ đề nghị cấp giấy chứng nhận đủ điều kiện kinh doanh dược của {tenqtnt}, mã hồ sơ:{mahs};

Căn cứ biên bản đánh giá Thực hành tốt cơ sở bán lẻ thuốc kèm danh mục kiểm tra Thực hành tốt cơ sở bán lẻ thuốc đối với {qthaynt} ngày {ngaythamdinh} của {tenqtnt},

Sở Y tế tỉnh Phú Thọ thông báo kết quả thẩm định như sau: Không đủ điều kiện cấp Giấy chứng nhận đủ điều kiện kinh doanh dược.


{lydo_full}

\vspace{{-0.5\baselineskip}}

\setstretch{{1}}
\noindent
\begin{{minipage}}[t]{{0.5\textwidth}}
\singlespacing
\fontsize{{12pt}}{{14pt}}\selectfont \textbf{{\textit{{Nơi nhận:}}}}\\
\fontsize{{11pt}}{{13pt}}\selectfont - Như kính gửi;\\
- Giám đốc, các PGĐ Sở;\\
- Các phòng CN Sở Y tế;\\\
- TT KSBT (đăng tải Website Sở);\\
- Lưu: VT, NVD.\\
\end{{minipage}}
\begin{{minipage}}[t]{{0.5\textwidth}}\singlespacing
\begin{{center}}
\textbf{{KT. GIÁM ĐỐC}}\\
\textbf{{PHÓ GIÁM ĐỐC}}\\
\vspace{{2.8cm}}
\textbf{{Nguyễn Đắc Ca}}\\
\end{{center}}
\end{{minipage}}
\end{{document}}
"""
                name = f"THÔNG BÁO KẾT QUẢ Thẩm định điều kiện cấp Giấy chứng nhận đủ điều kiện kinh doanh dược: {tenqtnt}"
                df_name.to_csv("name.csv", index_label="ma ho so")
                auto_text_to_ioffice_sure(name, "TB", mahs, text)
                os.chdir("/home/<USER>/Dropbox/hnd/csv_source")
            elif vande == "kp":
                texts = rf"""\documentclass{{article}}
\usepackage[T5]{{fontenc}}
\usepackage{{parskip}}
\nonstopmode
\usepackage[fontsize=14pt]{{scrextend}}
\usepackage[a4paper,vmargin=2cm,right=2cm,left=3cm,]{{geometry}}
\usepackage{{mathptmx}}
\usepackage[none]{{hyphenat}}
\usepackage{{ulem}}
\renewcommand{{\ULdepth}}{{7pt}}
\renewcommand{{\ULthickness}}{{0.5pt}}
\usepackage{{setspace}}
\usepackage{{tabularray}}
\usepackage{{indentfirst}}
\sloppy
\setlength{{\parindent}}{{1.27cm}}
\setlength{{\parskip}}{{6pt}}
\pagenumbering{{gobble}}
\begin{{document}}

\setstretch{{1}}
\noindent
\begin{{minipage}}{{\textwidth}}
\begin{{tblr}}{{width=1\linewidth,
colspec={{X[1,c] X[2,c]}},
colsep=0pt,
rowsep=-3pt,
row{{2}} = {{font=\bfseries}}}}
\fontsize{{13pt}}{{0pt}}\selectfont UBND TỈNH VĨNH PHÚC & \fontsize{{13pt}}{{0pt}}\selectfont \textbf{{CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM}}\\
{{\fontsize{{13pt}}{{0pt}}\selectfont SỞ Y TẾ\\[3pt]
\rule[0.6\baselineskip]{{.14\linewidth}}{{.5pt}}}} & \fontsize{{14pt}}{{0pt}}\selectfont \uline{{Độc lập - Tự do - Hạnh phúc}}\\[12pt]
\fontsize{{14pt}}{{0pt}}\selectfont Số: \hspace{{1.5cm}}/TB-SYT & \fontsize{{14pt}}{{0pt}}\selectfont \textit{{Phú Thọ, ngày {ngay} tháng {thang} năm {nam}}}\\[6pt]
\end{{tblr}}
\end{{minipage}}

\vspace{{-0.4cm}}

\begin{{center}}

\textbf{{THÔNG BÁO\\Yêu cầu khắc phục, sửa chữa tồn tại sau thẩm định thực tế ngày {ngaythamdinh}}}\\

\rule[0.6\baselineskip]{{.4\linewidth}}{{.5pt}}

\vspace{{-0.2cm}}

Kính gửi: {tenqtnt}
\end{{center}}

\vspace{{-0.2cm}}

\setstretch{{1}}

Căn cứ Thông tư số 02/2018/TT-BYT ngày 22/01/2018 của Bộ trưởng Bộ Y tế quy định về Thực hành tốt cơ sở bán lẻ thuốc;\par
Căn cứ hồ sơ đề nghị cấp giấy chứng nhận đủ điều kiện kinh doanh dược của {tenqtnt}, mã hồ sơ:{index};\par
Căn cứ Biên bản đánh giá Thực hành tốt cơ sở bán lẻ thuốc ngày {ngaythamdinh} của {tenqtnt}, kết quả đánh giá ở mức ``Cơ sở bán lẻ thuốc phải báo cáo khắc phục'' (đính kèm Thông báo),\par
Tên cơ sở bán lẻ: {tenqtnt}; địa chỉ: {diachi};\par
Người phụ trách chuyên môn: {ten_nguoiptm}; số chứng chỉ hành nghề dược: {values['so cchnd']}; nơi cấp: {values['noi cap cchnd']}; ngày cấp: {values['ngay cap cchnd']}.\par
Sở Y tế yêu cầu {tenqtnt} khắc phục, sửa chữa các tồn tại đã nêu trong Biên bản đánh giá Thực hành tốt cơ sở bán lẻ thuốc trong thời hạn 07 ngày làm việc, kể từ ngày ban hành Thông báo.\par
Sau khi hoàn thành việc khắc phục, sửa chữa, {tenqtnt} phải có văn bản báo cáo việc khắc phục, kèm theo các bằng chứng (hồ sơ tài liệu, hình ảnh, video) chứng minh đã hoàn thành việc khắc phục, sửa chữa tồn tại (mẫu văn bản báo cáo khắc phục đính kèm Thông báo).\par
Sở Y tế thông báo và yêu cầu {tenqtnt} thực hiện đúng quy trình khắc phục./.\par

\vspace{{-0.5cm}}

\noindent
\begin{{minipage}}[t]{{0.5\textwidth}}\singlespacing
\fontsize{{12pt}}{{14pt}}\selectfont \textbf{{\textit{{Nơi nhận:}}}}\\
\fontsize{{11pt}}{{13pt}}\selectfont - Như kính gửi;\\
\fontsize{{11pt}}{{13pt}}\selectfont - Giám đốc, các PGĐ sở;\\
\fontsize{{11pt}}{{13pt}}\selectfont - Các phòng CN Sở Y tế;\\\
\fontsize{{11pt}}{{13pt}}\selectfont - TT KSBT (đăng tải Website Sở);\\
\fontsize{{11pt}}{{13pt}}\selectfont - Lưu: VT, NVD.\\
\end{{minipage}}
\begin{{minipage}}[t]{{0.5\textwidth}}\singlespacing
\begin{{center}}
\textbf{{KT. GIÁM ĐỐC}}\\
\textbf{{PHÓ GIÁM ĐỐC}}\\
\vspace{{3cm}}
\textbf{{Nguyễn Đắc Ca}}\\
\end{{center}}
\end{{minipage}}
\newpage

\newgeometry{{margin=1cm}}
\setstretch{{1.1}}

\setlength{{\parskip}}{{6pt}}
\begin{{center}}

\textbf{{	MẪU BÁO CÁO KHẮC PHỤC}}

\textit{{	(Đính kèm Thông báo số \hspace{{1.5cm}}/TB-SYT ngày {today} của Sở Y tế tỉnh Phú Thọ)}}

\bfseries

CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM

\uline{{Độc lập - Tự do - Hạnh phúc}}

\end{{center}}


\newcommand\fillin[1][3cm]{{\makebox[#1]{{\dotfill}}}}

{{\centering    \textbf{{BÁO CÁO KHẮC PHỤC}}\par}}

\vspace{{0.5cm}}

Kính gửi: Sở Y tế tỉnh Phú Thọ

Tên cơ sở bán lẻ: \dotfill

Tên người phụ trách chuyên môn dược: \dotfill

Căn cứ Biên bản đánh giá ``Thực hành tốt cơ sở bán lẻ thuốc'' ngày \fillin[3cm]  của Đoàn thẩm định, Sở Y tế tỉnh Phú Thọ, cơ sở bán lẻ thuốc đã tiến hành khắc phục, sửa chữa những tồn tại ghi trong biên bản. Cụ thể:

\noindent
\begin{{minipage}}{{\textwidth}}
\begin{{tblr}}{{width=1\linewidth,hlines,vlines,
colspec={{X[0.5,c] X[3,c] X[3,c]}},
colsep=3pt,
rowsep=3pt,
rows={{font=\small,m,1.3cm}},
row{{1}}={{font=\bfseries}}}}
STT & Tồn tại nêu trong biên bản & Kết quả khắc phục\\
1\\
2\\
3\\
4\\
5\\
6\\
\end{{tblr}}
\end{{minipage}}

Tôi kính đề nghị Sở Y tế tỉnh Phú Thọ kiểm tra, đánh giá lại và xem xét, công nhận cơ sở bán lẻ thuốc đạt tiêu chuẩn \textbf{{"Thực hành tốt cơ sở bán lẻ thuốc"}}

\vspace{{-0.5cm}}

\hfill\begin{{minipage}}{{0.7\textwidth}}\singlespacing
\begin{{center}}
\textit{{Phú Thọ, ngày \fillin[1.5cm] tháng \fillin[1.5cm] năm \fillin[1.5cm]}}\\
\textbf{{NGƯỜI PHỤ TRÁCH CHUYÊN MÔN}}\\
\textit{{(Ký, ghi rõ họ tên)}}\\
\vspace{{3cm}}
\end{{center}}
\end{{minipage}}
\end{{document}}"""
                number= auto_text_to_ioffice_sure(
                    f"Yêu cầu khắc phục, sửa chữa tồn tại sau thẩm định thực tế ngày {ngaythamdinh}: {tenqtnt}", "TB",
                    0, texts)
                df_name.at[index, 'van de hs'] = number
                df_name.to_csv("name.csv", index_label="ma ho so")
                os.chdir("/home/<USER>/Dropbox/hnd/csv_source")
    df_dkkd.to_csv("dkkd.csv", index=True, index_label="ma ho so")
    df_name.to_csv("name.csv", index=True, index_label="ma ho so")
    if len(list_chuyen) > 0:
        driver = selenium_start('TD')
        phancong(2, list_chuyen, driver)
        show_message('THONG BAO', f'Đã chuyển TĐTT {len(list_chuyen)} hồ sơ')


if __name__ == "__main__":
    vande_sautd()
