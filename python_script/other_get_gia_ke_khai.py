import os
import requests
import json
import demjson3 as demjson

import pandas as pd

os.chdir("/home/<USER>/Dropbox/linux/backup/csv document")

url = "https://dichvucong.dav.gov.vn/api/services/app/quanLyGiaThuoc/GetListCongBoPublicPaging"

rows = []
for count in range(0, 700):
    payload = json.dumps(
        {
            "CongBoGiaThuoc": {},
            "KichHoat": True,
            "skipCount": count,
            "maxResultCount": 100,
            "sorting": None,
        }
    )
    headers = {
        "Content-Type": "application/json",
        "X-XSRF-TOKEN": "SSAdtqWHqmPNbF9SGhWM7xHTvW4TdJz4BP1fJ8LLmo4vUGCvEc_qkSecrmczgObnWrCJWvLHRZid2qvBeHpd9NBVyBQeMh2LNcJVjEB6p7E1",
    }

    response = requests.request("POST", url, headers=headers, data=payload)
    res = demjson.decode(response.text)

    for i in range(len(res["result"]["items"])):
        col_1 = res["result"]["items"][i]["tenThuoc"]
        col_2 = res["result"]["items"][i]["hoatChat"]
        col_3 = res["result"]["items"][i]["donViTinh"]
        col_4 = res["result"]["items"][i]["hamLuong"]
        col_5 = res["result"]["items"][i]["soDangKy"]
        col_6 = res["result"]["items"][i]["quyCachDongGoi"]
        col_7 = res["result"]["items"][i]["giaBanBuonDuKien"]
        col_8 = res["result"]["items"][i]["giaBanLeDuKien"]
        col_9 = res["result"]["items"][i]["phanLoaiThuocEnum"]
        col_10 = res["result"]["items"][i]["doanhNghiepSanXuat"]
        col_11 = res["result"]["items"][i]["ngayKK_KKL"]
        row = [
            col_1,
            col_2,
            col_3,
            col_4,
            col_5,
            col_6,
            col_7,
            col_8,
            col_9,
            col_10,
            col_11,
        ]
        rows.append(row)
    print(f"Dang them trang so {count}")
df = pd.DataFrame(
    rows,
    columns=[
        "Tên Thuốc",
        "Hoạt Chất",
        "Đơn Vị Tính",
        "Hàm Lượng",
        "Số Đăng Ký",
        "Quy Cách Đóng Gói",
        "Giá Bán Buôn Dự Kiến",
        "Giá Bán Lẻ Dự Kiến",
        "Phân Loại Thuốc Enum",
        "Doanh Nghiệp Sản Xuất",
        "Ngày KK/KKL",
    ],
)
df.to_csv("gia_ke_khai.csv", index=False)