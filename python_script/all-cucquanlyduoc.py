import pickle
import os
import requests
from bs4 import BeautifulSoup

from god_class import TelegramSend


os.chdir("/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pickle_file")
tele = TelegramSend("qlhn")


def laythongtin(savefile, url, text: list, token, receiver_id):
    # Lấy nội dung HTML của trang web
    r = requests.get(url, verify=False)
    html_content = r.text
    # Sử dụng BeautifulSoup để trích xuất các thẻ <a> chứa đường link
    soup = BeautifulSoup(html_content, "html.parser")
    a_tags = soup.find_all("a")
    # Lọc ra các đường link của bài đăng
    post_links = []
    for tag in a_tags:
        for string in text:
            if tag.has_attr("href") and string in tag["href"]:
                post_links.append(tag["href"])
    results = post_links[:8]
    with open(savefile, "rb") as file:
        mypicke = pickle.load(file)

    for result in results:
        if result not in mypicke:
            # Thay urllib2.urlopen bằng requests.get
            response = requests.get(result, verify=False)
            html = response.text
            sopa = BeautifulSoup(html, features="lxml")
            current_link = ""
            tele.send_message_normal(result)
            print(result)

            for link in sopa.find_all("a"):
                current_link = link.get("href")
                if current_link.endswith("pdf"):
                    message = "https://dav.gov.vn" + current_link
                    tele.send_message_normal(message)
    with open(savefile, "wb") as file:
        pickle.dump(post_links[:8], file)


laythongtin(
    "thuocthuhoi.pk",
    r"https://dav.gov.vn/cong-van-thu-hoi-thuoc-cn89.html",
    ["san-pham", "thu-hoi-thuoc"],
    "5517353700:AAGbzj1BW7Gg11wTBrXIUQEUXmLez_NodcA",
    "-1001587612405",
)
laythongtin(
    "myphamthuhoi.pk",
    r"https://dav.gov.vn/canh-bao-va-thu-hoi-cn81.html",
    ["thu-hoi"],
    "5517353700:AAGbzj1BW7Gg11wTBrXIUQEUXmLez_NodcA",
    "-1001587612405",
)

laythongtin(
    "thuocgia.pk",
    r"https://dav.gov.vn/cong-van-chung-qlclt-cn88.html",
    ["san-pham", "mau-thuoc", "thuoc-gia"],
    "5517353700:AAGbzj1BW7Gg11wTBrXIUQEUXmLez_NodcA",
    "-1001587612405",
)

#
