import datetime
import os
from builtins import ValueError

import pandas as pd

from god_class import (
    TextProcess,
    get_current_date,
    get_dict_from_index_df,
    update_df_from_dict_by_index,
    update_df_name_cho_tra,
)
from create_snippets import creat_and_get
from all_pickle import load_pickle

os.chdir("/home/<USER>/Dropbox/hnd/csv_source")


def xnqc_mp(mhs):
    values = {}
    dict_cong_ty = {
        "CÔNG TY CỔ PHẦN CÔNG NGHỆ LAVITEC": "Công ty cổ phần công nghệ Lavitec",
        "CÔNG TY TNHH SEACRET": "Công ty TNHH Seacret",
    }
    data_path = "xn_qc_mp.csv"
    df_qc = pd.read_csv(data_path, dtype="str", index_col="ten cong ty")
    df_qc.fillna("", inplace=True)
    df_name = pd.read_csv("name.csv", dtype="str", index_col="ma ho so")
    df_name.fillna("", inplace=True)
    year_now = datetime.datetime.now().year

    mask_tn_nam = df_qc["ngay cong bo"].str.contains(str(year_now))
    list_stn_nam = df_qc[mask_tn_nam]["so tn"].tolist()
    if list_stn_nam:
        max_stn_nam = max(list_stn_nam)
        values["so tn"] = str(int(max_stn_nam) + 1).zfill(2)
    else:
        values["so tn"] = "01"

    # TODO dict name
    dict_name = get_dict_from_index_df(df_name, mhs)
    ten_nguoi_ptcm = dict_name["ten nguoi ptcm"]
    ten_cong_ty = dict_cong_ty.get(ten_nguoi_ptcm, "KHONG CO")

    # TODO dict cong ty
    dict_cong_ty = get_dict_from_index_df(df_qc, ten_cong_ty)

    default_fields = dict.fromkeys(
        [
            "ten san pham",
            "ten cong ty",
            "dia chi",
            "dien thoai",
            "fax",
            "cu the",
            "so phieu cong bo",
            "phuong tien quang cao",
            "chu giai: 1-ko-phai-bao-hinh-bao-noi;2-hoi-thao;3-bao-hinh-bao-noi",
        ]
    )
    default_fields.update(
        {k: dict_cong_ty[k] for k in default_fields if k in dict_cong_ty}
    )
    default_fields["ten cong ty"] = ten_cong_ty
    default_fields["phuong tien quang cao"] = "1"

    values_to_update = creat_and_get(default_fields)
    if values_to_update is None:
        raise Exception(
            "Không thể lấy giá trị từ creat_and_get. Vui lòng kiểm tra lại file TOML và terminal."
        )

    values.update(values_to_update)
    values["phuong tien quang cao"] = (
        values["phuong tien quang cao"]
        .replace(
            "1", "quảng cáo trên các phương tiện quảng cáo không phải báo nói, báo hình"
        )
        .replace("2", "quảng cáo thông qua hội thảo, hội nghị, tổ chức sự kiện")
        .replace("3", "quảng cáo trên báo nói, báo hình")
    )

    values["ngay"], values["thang"], values["nam"], values["ngay cong bo"] = (
        get_current_date()
    )
    if values["fax"]:
        values["fax_modified"] = f"; fax: {values['fax']}"
    iso_time = datetime.datetime.now().isoformat()  # Thêm thời gian ISO
    name = f"{mhs}-{values['ten cong ty']}-{iso_time}"

    xnqc = TextProcess("xn_qc_mp")
    xnqc.format_text(values)
    xnqc.auto_day_van_ban(name, "TB", mhs)
    values["ten san pham"] = values["ten san pham"].upper()
    df_qc["ten cong ty"] = df_qc.index
    df_qc.set_index("ma ho so", inplace=True)

    update_df_from_dict_by_index(df_qc, "xn_qc_mp.csv", values, mhs)
    values["ten qt-nt"] = values["ten san pham"]
    update_df_name_cho_tra(df_name, values, mhs)


if __name__ == "__main__":
    path = load_pickle(
        "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pickle_file/hs_path.pk"
    )
    mhs, _, _ = path.split("/")[-1].split("--")
    xnqc_mp(mhs)
