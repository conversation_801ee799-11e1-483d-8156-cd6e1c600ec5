import os

import pandas as pd

from god_class import TextProcess, insert_stt
from god_class import captalize_first_char

os.chdir("/home/<USER>/Dropbox/hnd/csv_source/")
df_name = pd.read_csv("name.csv", dtype="str")
df_thu_tuc_tdtt = pd.read_csv("thu_tuc_tham_dinh_tt.csv", dtype=str)
list_thutuc = df_thu_tuc_tdtt["TÊN TẮT"].tolist()
is_tdtt = df_name["thu tuc"].isin(list_thutuc)
is_dadi = df_name["da di"] != "1"
not_dang_tamdung = df_name["trang thai"] != "ĐANG TẠM DỪNG"
# is_ten_qtnt = df_name["ten qt-nt"].notnull()
not_vande_gpp = df_name["van de hs"] != "gpp"

condition = is_tdtt & is_dadi & not_dang_tamdung & not_vande_gpp


def tach_dia_chi_den_xa(address):
    # Chia chuỗi theo dấu phảy
    address_parts = address.split(",")
    # Bỏ các ký tự sau dấu phảy thứ 2 tính từ cuối
    address_parts = address_parts[:-2]
    # Ghép lại chuỗi
    new_address = ",".join(address_parts)
    return new_address


df_test = df_name[is_tdtt]

df_name_loc = df_name[condition].copy()

# Chuyển đổi han back_up sang datetime và sắp xếp
df_name_loc["han back_up"] = pd.to_datetime(
    df_name_loc["han back_up"], format="%d/%m/%Y %H:%M:%S"
)
df_name_loc = df_name_loc.sort_values(by="han back_up")

# Chuyển han back_up về định dạng ngày tháng mong muốn
df_name_loc["han back_up"] = df_name_loc["han back_up"].dt.strftime("%d/%m/%Y")

# Các xử lý khác với DataFrame
df_name_loc["dia chi co so"].fillna("CHUA CO,,,", inplace=True)
df_name_loc["HUYỆN"] = (
    df_name_loc["dia chi co so"]
    .apply(lambda x: x[::-1])
    .str.split(",")
    .str.get(1)
    .apply(lambda x: x[::-1])
    .str.replace(" huyện ", "")
    .str.replace(" thành phố ", "TP ")
)

df_name_loc.loc[df_name_loc["ten qt-nt"].isnull(), "ten qt-nt"] = df_name_loc.loc[
    df_name_loc["ten qt-nt"].isnull(), "ten nguoi ptcm"
]

df_name_loc["dia chi co so"] = (
    df_name_loc["dia chi co so"].apply(tach_dia_chi_den_xa).apply(captalize_first_char)
)
df_name_loc["dia chi co so"] = df_name_loc["dia chi co so"].str.replace(
    ", tỉnh Vĩnh Phúc", ""
)

df_name_loc["ten nguoi ptcm"] = df_name_loc["ten nguoi ptcm"].str.replace("_", " ")

df_name_loc = df_name_loc[
    [
        "han back_up",
        "thu tuc",
        "ten qt-nt",
        "so dt chu hs",
        "ten nguoi ptcm",
        "dia chi co so",
        "HUYỆN",
    ]
]
# df_name_loc["van de hs"] = df_name_loc["van de hs"].str.replace("_", r"\_")

df_name_loc.fillna("", inplace=True)

# Xử lý cuối cùng
insert_stt(df_name_loc)
df_name_loc = df_name_loc.astype(str).apply("&".join, axis=1)
values = {}
values["s"] = df_name_loc.str.cat(sep=r"\\" + "\n") + r"\\"
text = TextProcess("len_lich_td")
text.format_text(values)
text.compile_latex()
text.open_pdf_with_okular()
