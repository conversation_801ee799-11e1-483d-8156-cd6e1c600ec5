{"cells": [{"cell_type": "code", "execution_count": 7, "id": "initial_id", "metadata": {"ExecuteTime": {"end_time": "2024-06-02T15:20:15.019863Z", "start_time": "2024-06-02T15:19:33.237975Z"}, "collapsed": true}, "outputs": [], "source": ["import os\n", "import requests\n", "import json\n", "import demjson3 as demjson\n", "\n", "import pandas as pd\n", "\n", "os.chdir(\"/home/<USER>/Dropbox/linux/backup/csv document\")\n", "\n", "url = \"https://dichvucong.dav.gov.vn/api/services/app/quanLyGiaThuoc/GetListCongBoPublicPaging\"\n", "\n", "rows = []\n", "for count in range(0, 700):\n", "    payload = json.dumps({\n", "        \"CongBoGiaThuoc\": {},\n", "        \"KichHoat\": True,\n", "        \"skipCount\": count,\n", "        \"maxResultCount\": 100,\n", "        \"sorting\": None\n", "    })\n", "    headers = {\n", "        'Content-Type': 'application/json',\n", "        'X-XSRF-TOKEN': 'SSAdtqWHqmPNbF9SGhWM7xHTvW4TdJz4BP1fJ8LLmo4vUGCvEc_qkSecrmczgObnWrCJWvLHRZid2qvBeHpd9NBVyBQeMh2LNcJVjEB6p7E1',\n", "    }\n", "\n", "    response = requests.request(\"POST\", url, headers=headers, data=payload)\n", "    res = demjson.decode(response.text)\n", "\n", "    for i in range(len(res['result']['items'])):\n", "        col_1 = res['result']['items'][i]['tenThuoc']\n", "        col_2 = res['result']['items'][i]['hoatChat']\n", "        col_3 = res['result']['items'][i]['donViTinh']\n", "        col_4 = res['result']['items'][i]['hamLuong']\n", "        col_5 = res['result']['items'][i]['soDangKy']\n", "        col_6 = res['result']['items'][i]['quyCachDongGoi']\n", "        col_7 = res['result']['items'][i]['giaBanBuonDuKien']\n", "        col_8 = res['result']['items'][i]['giaBanLeDuKien']\n", "        col_9 = res['result']['items'][i]['phanLoaiThuocEnum']\n", "        col_10 = res['result']['items'][i]['doanhNghiepSanXuat']\n", "        col_11 = res['result']['items'][i]['ngayKK_KKL']\n", "        row = [col_1, col_2, col_3, col_4, col_5, col_6, col_7, col_8, col_9, col_10, col_11]\n", "        rows.append(row)\n", "    print(f'Dang them trang so {count}')\n", "df = pd.DataFrame(rows, columns=[\n", "    '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Đ<PERSON><PERSON>', '<PERSON><PERSON><PERSON>',\n", "    '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>',\n", "    '<PERSON><PERSON><PERSON> Lẻ <PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>',\n", "    'Ngày KK/KKL'\n", "])\n", "df.to_csv('gia_ke_khai_thuoc.csv', index=False)\n", "\n", "\n", "\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "a78bcf7f4af1f6c", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "/home/<USER>/python313/bin/python"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}