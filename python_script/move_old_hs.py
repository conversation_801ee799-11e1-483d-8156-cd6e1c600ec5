import os
import shutil
from datetime import datetime, timedelta


def move_old_folders():
    # Đường dẫn nguồn và đích
    source_dir = (
        "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pdf_documents/file_hs_backup"
    )
    dest_dir = "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pdf_documents/hs_saved"

    # Tính thời điểm 1 tháng trước
    one_month_ago = datetime.now() - timedelta(days=30)

    # Đ<PERSON><PERSON> bảo thư mục đích tồn tại
    if not os.path.exists(dest_dir):
        os.makedirs(dest_dir)

    # Duyệt qua tất cả các mục trong thư mục nguồn
    for item in os.listdir(source_dir):
        item_path = os.path.join(source_dir, item)

        # Kiểm tra nếu là thư mục
        if os.path.isdir(item_path):
            # <PERSON><PERSON><PERSON> thời gian tạ<PERSON> thư mục
            creation_time = datetime.fromtimestamp(os.path.getctime(item_path))

            # So sánh với thời điểm 1 tháng trước
            if creation_time < one_month_ago:
                # Đường dẫn đích cho thư mục
                destination_path = os.path.join(dest_dir, item)

                try:
                    # Di chuyển thư mục
                    shutil.move(item_path, destination_path)
                    print(f"Đã di chuyển thư mục {item} thành công")
                except Exception as e:
                    print(f"Lỗi khi di chuyển thư mục {item}: {str(e)}")


if __name__ == "__main__":
    move_old_folders()
