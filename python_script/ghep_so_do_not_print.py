import os
import sys
import glob
import shutil
import pikepdf
from god_class import (
    fzf_get_dict_from_column,
    send_notification,
    show_message,
)
from csv_load_and_export import CsvLoaderFactory


def on_sodo_clicked(values):
    path = "/home/<USER>/Dropbox/hnd/latexall/mylatex/ban_ve.pdf"
    if not os.path.isfile(path):
        # Lấy file PDF mới nhất từ thư mục kq
        kq_path = "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pdf_documents/kq"
        pdf_files = glob.glob(os.path.join(kq_path, "*.pdf"))
        if pdf_files:
            latest_pdf = max(pdf_files, key=os.path.getctime)
            shutil.copy(latest_pdf, path)
            send_notification("Đã copy lastest file pdf từ thư mục kq")
        else:
            show_message("THONG BAO", "KHONG TIM THAY FILE BAN VE")
            sys.exit()
    file_hs = f"hs-{values['id_column']}.pdf"
    # dm_thuoc = f"dm thuoc-{values['id_column']}.pdf"
    with pikepdf.Pdf.open(file_hs, allow_overwriting_input=True) as pdf:
        number_of_pages = len(pdf.pages)
        if number_of_pages == 5:
            num_sodo = 4
        else:
            num_sodo = 5
        with pikepdf.Pdf.open(os.path.expanduser(path)) as pdf_so_do:
            # Add the first page from 'so_do.pdf' at the position of num_sodo
            position_to_insert = num_sodo
            pdf.pages.insert(position_to_insert, pdf_so_do.pages[0])

        # Save the modified 'file_hs'
        pdf.save(file_hs)


if __name__ == "__main__":
    send_notification("ghép so do nhung khong in")
    df = CsvLoaderFactory.create_fillna_loader().load_df("co_so_ban_le", "id_column")
    mask = df["ve_so_do"] == "0"
    df2 = df[mask]
    values = fzf_get_dict_from_column(df2)
    os.chdir("/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pdf_documents/lam_ho_so/")
    on_sodo_clicked(values)
    df.loc[values["id_column"], "ve_so_do"] = "1"
    df.to_csv(
        "/home/<USER>/Dropbox/hnd/csv_source/co_so_ban_le.csv", index_label="id_column"
    )
    send_notification("ĐÃ XONG")
