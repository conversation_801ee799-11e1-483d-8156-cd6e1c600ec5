{"cells": [{"metadata": {"ExecuteTime": {"end_time": "2024-05-28T08:11:38.517743Z", "start_time": "2024-05-28T08:11:38.462761Z"}}, "cell_type": "code", "source": ["import os\n", "\n", "import pandas as pd\n", "os.chdir('/home/<USER>/Dropbox/hnd/csv_source')\n", "df = pd.read_csv('danh-sach-thieu-nhi-syt-2024.csv', dtype=str)\n", "phong = df['PHÒNG'].unique().tolist()"], "id": "3ad23c643a0a8cc4", "execution_count": 46, "outputs": []}, {"metadata": {"ExecuteTime": {"end_time": "2024-05-28T08:11:39.391946Z", "start_time": "2024-05-28T08:11:39.283718Z"}}, "cell_type": "code", "source": ["for name in phong:\n", "    df_name = df[df['PHÒNG'] == name]\n", "    df_name.reset_index(inplace=True, drop=True)\n", "    df_name.index += 1\n", "    df_name = df_name.drop(columns=['SỐ TIỀN'])\n", "    df_name.to_excel(f'{name}.xlsx', index_label='stt')\n", "    df_name.to_csv(f'{name}.csv', index_label='stt')"], "id": "200c5adf24a35ce6", "execution_count": 47, "outputs": []}, {"metadata": {}, "cell_type": "code", "execution_count": null, "source": "", "id": "aaa8ce511bf48453", "outputs": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "/home/<USER>/python313/bin/python", "name": "/home/<USER>/python313/bin/python"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "/home/<USER>/python313/bin/python", "nbconvert_exporter": "/home/<USER>/python313/bin/python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}