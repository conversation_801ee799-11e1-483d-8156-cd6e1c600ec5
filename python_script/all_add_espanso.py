import sys

import yaml
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont
from PyQt5.QtWidgets import (
    QApplication,
    QWidget,
    QVBoxLayout,
    QLabel,
    QLineEdit,
    QPushButton,
)


def save_data(description, shortcut):
    file_path = "/home/<USER>/.config/espanso/match/all.yml"

    # Đọc file YAML hiện tại
    with open(file_path, "r") as file:
        data = yaml.safe_load(file)

    # Kiểm tra xem khóa 'matches' đã tồn tại trong file chưa, nếu chưa, tạo mới
    if "matches" not in data:
        data["matches"] = []

    # Thêm nội dung mới vào danh sách 'matches'
    new_match = {"trigger": shortcut, "replace": description}
    data["matches"].append(new_match)

    # Ghi lại vào file, giữ nguyên cấu trúc và căn lề
    with open(file_path, "w") as file:
        yaml.dump(
            data, file, sort_keys=False, default_flow_style=False, allow_unicode=True
        )


class App(QWidget):
    def __init__(self):
        super().__init__()
        self.initUI()

    def initUI(self):
        layout = QVBoxLayout()
        font = QFont()
        font.setPointSize(15)

        # Dòng 1
        self.descriptionLabel = QLabel("NHẬP TỪ CẦN TẠO PHÍM TẮT")
        self.descriptionLabel.setFont(font)
        self.descriptionInput = QLineEdit()
        self.descriptionInput.setFont(font)
        layout.addWidget(self.descriptionLabel)
        layout.addWidget(self.descriptionInput)

        # Dòng 2
        self.shortcutLabel = QLabel("PHÍM TẮT")
        self.shortcutLabel.setFont(font)
        self.shortcutInput = QLineEdit()
        self.shortcutInput.setFont(font)
        layout.addWidget(self.shortcutLabel)
        layout.addWidget(self.shortcutInput)

        # Nút lưu
        self.saveButton = QPushButton("Lưu")
        self.saveButton.setFont(font)
        self.saveButton.clicked.connect(self.onSave)
        layout.addWidget(self.saveButton)

        self.setLayout(layout)
        self.setWindowTitle("Tạo Phím Tắt")
        self.resize(700, 200)  # Thay đổi kích thước cửa sổ
        self.show()

    def keyPressEvent(self, event):
        if event.key() in (Qt.Key_Return, Qt.Key_Enter):
            self.onSave()

    def onSave(self):
        description = self.descriptionInput.text()
        shortcut = self.shortcutInput.text()
        save_data(description, shortcut)
        QApplication.quit()


if __name__ == "__main__":
    app = QApplication(sys.argv)
    ex = App()
    sys.exit(app.exec_())
