import pandas as pd
import os
from solid_string_format import DictStringFormatter
from top_most_get_text import input_dialog
from god_class import send_notification, show_message
from god_class import get_dict_from_index_df, update_df_from_dict_by_index
from create_snippets import creat_and_get
import sys
from typing import Dict, Any


def process_single_dkkd(
    so_dkkd: str,
    df_thu_hoi: pd.DataFrame,
    df_hn: pd.DataFrame,
    df_chamdut: pd.DataFrame,
) -> None:
    """
    Xử lý một số dkkd đơn lẻ.

    Args:
        so_dkkd: Số dkkd cần xử lý
        df_thu_hoi: DataFrame chứa danh sách dkkd đã thu hồi
        df_hn: DataFrame chứa danh sách gcn_dkkd
    """
    if so_dkkd in df_thu_hoi.index:
        show_message("Thông báo", "Số dkkd vừa mới nhập rồi")
        return

    dict_thu_hoi = get_dict_from_index_df(df_hn, so_dkkd)
    list_key = ["ten qt-nt", "dia chi co so", "ten nguoi ptcm", "so cchnd", "ngay qd"]
    list_ngay = ["ngay qd"]
    values: Dict[str, Any] = {}
    for key in list_key:
        values[key] = dict_thu_hoi[key]

    dict_thu_hoi = creat_and_get(values)

    formatter = DictStringFormatter(values)
    values.update(dict_thu_hoi)
    values = formatter.apply_date_format(list_ngay).get_result()
    values["so dkkd"] = so_dkkd
    values["ly do"] = "Cơ sở xin chấm dứt hoạt động kinh doanh dược"
    update_df_from_dict_by_index(df_thu_hoi, "dsthuhoigpp.csv", values, so_dkkd)
    send_notification("done")


def main() -> None:
    """
    Hàm chính để chạy chương trình.
    """
    os.chdir("/home/<USER>/Dropbox/hnd/csv_source")

    # Đọc dữ liệu
    df_thu_hoi = pd.read_csv("dsthuhoigpp.csv", dtype=str)
    df_thu_hoi.set_index("so dkkd", inplace=True)
    df_hn = pd.read_csv("du_lieu_gpp_all.csv", dtype=str)
    df_hn.set_index("so dkkd", inplace=True)
    df_chamdut = pd.read_csv("ds_da_cham_dut.csv", dtype=str)
    df_chamdut.set_index("so dkkd", inplace=True)
    # Hỏi số lần lặp
    try:
        so_lan_lap = int(input_dialog("Title", "Nhập số lần lặp lại", ""))
    except ValueError:
        show_message("Lỗi", "Vui lòng nhập một số nguyên")
        sys.exit(1)

    # Lặp lại theo số lần đã nhập
    for i in range(so_lan_lap):
        so_dkkd = input_dialog(
            "Title", f"Nhập số gcn_dkkd để thu hồi (lần {i + 1}/{so_lan_lap})", ""
        )
        if not so_dkkd:
            sys.exit()
        process_single_dkkd(so_dkkd, df_thu_hoi, df_hn, df_chamdut)


if __name__ == "__main__":
    main()
