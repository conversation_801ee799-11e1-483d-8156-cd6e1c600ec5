import os
import pandas as pd
import unidecode
from create_snippets import creat_and_get

from god_class import (
    auto_text_to_ioffice_pd,
    get_current_date,
    send_notification,
    update_df_from_dict_by_index,
    show_message,
)
from loguru import logger
from solid_string_format import DictStringFormatter
from top_most_get_text import input_dialog

os.chdir("/home/<USER>/Dropbox/hnd/csv_source")


@logger.catch
def run_main():
    path = "tra_loi_cv_xacminh.csv"
    df_traloi_xm = pd.read_csv(path, dtype=str, index_col="cmnd")
    df_dshn = pd.read_csv("dshn_duoc.csv", dtype=str)
    df_dshn.set_index("cmnd", inplace=True)
    df1 = {
        "noi nhan": "ha noi",
        "so cong van den": "",
        "ngay cong van den": "",
    }

    values = creat_and_get(df1)
    df_traloi_xm["noi nhan decode"] = df_traloi_xm["noi nhan"].apply(
        lambda x: unidecode.unidecode(x).lower()
    )
    df_filter = df_traloi_xm.loc[
        df_traloi_xm["noi nhan decode"].str.contains(values["noi nhan"])
    ]
    if not df_filter.empty:
        values["noi nhan"] = df_filter["noi nhan"].iloc[0]
        list_ve_viec = df_filter["ve viec"].unique().tolist()
        list_ve_viec_str = []
        for index, value in enumerate(list_ve_viec):
            list_ve_viec_str.append(f"{index + 1}. {value}")
        ve_viec = "\n".join(list_ve_viec_str)
        send_notification(ve_viec, True)
    else:
        values["noi nhan"] = input_dialog(
            "nhap", "Nhập tên đầy đủ của SYT cần trả lời", ""
        )

    df2 = {
        "ten nguoi ptcm": "",
        "cmnd": "",
        "ngay cap cmnd": "",
        "noi cap cmnd": "Cục Cảnh sát Quản lý hành chính về trật tự xã hội",
        "ngay sinh": "",
        "dia chi thuong tru": "",
        "trinh do cm": "Trung cấp dược",
        "noi xac nhan thuc hanh": "",
        "so cchnd": "",
        "ngay cap cchnd": "",
        "ong-ba": "1",
        "ve viec": "",
    }
    values2 = creat_and_get(df2)
    values.update(values2)

    if values["ve viec"].isdigit():
        values["ve viec"] = list_ve_viec[int(values["ve viec"]) - 1]

    list_ngay = [
        "ngay cap cmnd",
        "ngay tra loi",
        "ngay sinh",
        "ngay cap cchnd",
        "ngay cong van den",
    ]
    list_title = ["ten nguoi ptcm"]

    formatter = DictStringFormatter(values)
    values = formatter.apply_title(list_title).apply_date_format(list_ngay).get_result()

    # format string
    values["so cong van den"] = values["so cong van den"].replace("&", r"\&")
    values["ong-ba"] = values["ong-ba"].replace("1", "ông").replace("0", "bà")

    values["ngay"], values["thang"], values["nam"], values["ngay tra loi"] = (
        get_current_date()
    )

    if values["cmnd"] in df_dshn.index:
        show_message("THÔNG BÁO", f"Có thông tin hành nghề dược")

    from god_class import TextProcess

    text1 = TextProcess("all_tra_loi_xac_minh/khong_cchnd")
    if values["so cchnd"] == "":
        update_df_from_dict_by_index(
            df_traloi_xm, "tra_loi_cv_xacminh.csv", values, values["cmnd"]
        )
        text1.format_text(values)
        name = f"V/v trả lời Công văn {values['ve viec']} của {values['noi nhan']}: {values['ten nguoi ptcm']}"
        text1.auto_day_van_ban_pd(name, values["so cong van den"], "CV")
    else:
        update_df_from_dict_by_index(
            df_traloi_xm, "tra_loi_cv_xacminh.csv", values, values["cmnd"]
        )
        values["ten co so dang ky hn"] = (
            values["noi nhan"]
            + " "
            + values["so cong van den"]
            + " "
            + values["ngay cong van den"]
        )
        name = f"V/v trả lời Công văn {values['ve viec']} của {values['noi nhan']}: {values['ten nguoi ptcm']}"
        auto_text_to_ioffice_pd(name, values["so cong van den"], "CV")


if __name__ == "__main__":
    run_main()
