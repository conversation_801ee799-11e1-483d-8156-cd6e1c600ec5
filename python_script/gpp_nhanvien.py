import datetime

import pandas as pd

from god import hs_solution

global_counter = 0


def dict_to_latex(values):
    global global_counter
    global_counter += 1
    latex_line = ""
    latex_line += str(global_counter) + " & "
    for key in [
        "ten nhan vien",
        "trinh do cm",
        "dia chi thuong tru",
        "so cchnd",
        "vi tri hanh nghe",
        "thoi gian dang ky lam viec",
        "vi tri chuyen mon",
    ]:
        value = values[key]
        # Nếu giá trị là None, thay thế bằng chuỗi rỗng
        if value is None:
            value = ""
        if key == "so cchnd":
            value = "{" + value.replace("/", r"\\/") + "}"
        latex_line += value + " & "
    latex_line = latex_line.rstrip(" &") + r"\\"
    return latex_line


def nhap_nhan_vien(tencs, diachi, num):
    latex_col = ""
    df = pd.read_csv(
        "/home/<USER>/Dropbox/hnd/latexall/nguoi_hanh_nghe_co_chung_chi.csv", dtype="str"
    )
    for i in range(num):
        # TODO 1. DANH SÁCH CÁC DÒNG CHỌN LIST
        list_columns = {
            "trinh do cm": ["Đại học dược", "Cao đẳng dược", "Trung cấp dược"]
        }
        # TODO 2. DANH SÁCH CÁC GIÁ TRỊ MẶC ĐỊNH
        default_values = {
            "noi cap cchnd": "Sở Y tế tỉnh Phú Thọ",
            "vi tri chuyen mon": "Nhân viên bán thuốc",
            "NGÀY VÀO": datetime.datetime.now().strftime("%d/%m/%Y"),
            "thoi gian dang ky lam viec": "Từ 7h đến 18h hằng ngày",
            "vi tri hanh nghe": "Chưa có CCHND",
        }
        # TODO 3. DANH SÁCH CÁC DÒNG TRONG LIST MÀ ẢNH HƯỞNG TỚI GIÁ TRỊ MẶC ĐỊNH KHÁC (PHỤ THUỘC
        list_phuthuoc = ["trinh do cm"]
        # TODO 4. CÁC GIÁ TRỊ BỊ PHỤ THUỘC THEO KEY LỚN NHẤT LÀ VALUES CỦA LIST_PHUTHUOC
        values_phuthuoc = {
            "trinh do cm": {
                "Đại học dược": {"vi tri hanh nghe": "Nhà thuốc"},
                "Cao đẳn dược": {"vi tri hanh nghe": "Quầy thuốc"},
                "Trung cấp dược": {"vi tri hanh nghe": "Quầy thuốc"},
            }
        }
        # TODO 5. CÁC GIÁ TRỊ NGÀY CẦN CHUYỂN ĐỔI convert ngay
        list_ngay = ["ngay cap cchnd"]
        # TODO 6. CÁC GIÁ TRỊ PHONE CẦN CHUYỂN ĐỔI
        list_list_phone = []
        # TODO 7. CÁC GIÁ TRỊ TITLE VÀ UPPER CẦN CHUYỂN ĐỔI
        list_title = ["ten nhan vien"]
        list_upper = []
        # TODO 7.1 CÁC CỘT KHÔNG MUỐN CHO VÀO
        excepts = [
            "ten co so dang ky hn",
            "SỐ GCN ĐĐKKDD CƠ SỞ ĐĂNG KÝ HN",
            "dia chi co so",
            "so cchnd",
        ]
        # TODO 7.2 NHẬP TIÊU ĐỀ
        tieude = f"Nhập thông tin nhân viên {i}"
        # TODO 7.3 multil line
        multil = ""
        _, values = hs_solution(
            "/home/<USER>/Dropbox/hnd/latexall\nguoi_hanh_nghe_co_chung_chi.csv",
            "ten co so dang ky hn",
            list_columns,
            default_values,
            list_phuthuoc,
            values_phuthuoc,
            list_ngay,
            list_list_phone,
            list_title,
            list_upper,
            excepts,
            multil,
        )
        values["ten co so dang ky hn"] = tencs
        values["dia chi co so"] = diachi
        values["SỐ GCN ĐĐKKDD CƠ SỞ ĐĂNG KÝ HN"] = ""
        df_row = pd.DataFrame(values, index=[0])
        df = pd.concat([df, df_row], ignore_index=True)
        latex_col += dict_to_latex(values) + "\n"
    df.reset_index(inplace=True, drop=True)
    df.to_csv(
        "/home/<USER>/Dropbox/hnd/latexall/nguoi_hanh_nghe_co_chung_chi.csv",
        index=False,
       
    )
    return latex_col
