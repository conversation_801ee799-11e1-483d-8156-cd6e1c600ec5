# %%
import sys

import demjson3 as demjson
import requests
import telepot
from PyQt5 import QtCore, QtWidgets
from PyQt5.QtCore import QUrl
from PyQt5.QtGui import QFont, QDesktopServices, QBrush, QColor
from PyQt5.QtWidgets import QApp<PERSON>, QMainWindow, QTableWidget, QTableWidgetItem, QAbstractItemView

from god_class import change_workspace


def get_current_year():
    from datetime import datetime
    return datetime.now().year


nam = get_current_year()


def request_post(url, payload):
    headers = {
        'Authorization': 'Bearer acKKZDmXqpUmn7D3vuzwk88RN909S7Soc5YlXQhyV6I9lU6UNYaTHtnVEhzxoo6Pul1K2NroQeS6584FZDucIYETRjjn70alDIbOUQ4V3zt8JarwIvOhqZ+z96uFvFuZjsu03k3l5Ou0DhYlEmlQ/15duefP7Ozfa3bfIo3bASLX3cOwnkpUl/pQoUl8aw6UA7KwPJDjnXYn/NR0nW7uuYcPT7ic/DdhkiE08Us6bbSjqfTbHGdR8fC8Nza/I52dVn4RLZDAGfVFhFgZS6qw2j2VTpQ1hpMelekgyrg23fFO2qhb81RllfgYPTifzG9j',
        'Connection': 'keep-alive', 'Content-Type': 'application/x-www-form-urlencoded', }
    response = requests.post(url, headers=headers, data=payload)
    return demjson.decode(response.text)


res = request_post("https://qlvb-api.vinhphuc.gov.vn/api/van-ban-di/danh-sach-van-ban-di-da-phat-hanh-cua-chuyen-vien",
                   f'ma_ctcb_cv=6075&ma_don_vi_quan_tri=1573&nam={nam}&ngay_di_den_ngay=19%2F06%2F2030&ngay_di_tu_ngay=19%2F05%2F2023&page=1&size=50')

token = '2144388053:AAEzMqn010m3XiLJXXWb45f2hG8POKvjz2M'  # telegram token
receiver_id = '-1001666712476'
bot = telepot.Bot(token)


def format_date(input_date):
    return input_date.split(' ')[0]


# %%


class MainWindow(QMainWindow):
    def them_du_lieu_vao_bang(self, res):
        self.table_widget.setColumnCount(6)
        self.setWindowTitle('Danh sách văn bản đã phát hành')
        self.table_widget.setHorizontalHeaderLabels(
            ['NƠI BAN HÀNH', 'TRÍCH YẾU', 'SỐ KH', 'NGÀY BAN HÀNH', 'NGƯỜI KÝ', 'LINK'])
        self.table_widget.cellClicked.connect(self.open_url)
        self.resize(1700, 900)
        self.table_widget.setFont(QFont('Arial', 12))  # Đặt cỡ chữ
        self.setCentralWidget(self.table_widget)
        for item in res['data']:
            row_position = self.table_widget.rowCount()
            self.table_widget.insertRow(row_position)
            self.table_widget.setItem(row_position, 0, QTableWidgetItem(item['noi_luu_ban_chinh']))
            self.table_widget.setItem(row_position, 1, QTableWidgetItem(item['trich_yeu']))
            self.table_widget.setItem(row_position, 2, QTableWidgetItem(item['so_ky_hieu']))
            self.table_widget.setItem(row_position, 3, QTableWidgetItem(format_date(item['ngay_ban_hanh'])))
            self.table_widget.setItem(row_position, 4, QTableWidgetItem(item['nguoi_ky']))
            self.table_widget.setItem(row_position, 5, QTableWidgetItem(
                f"https://iqlvb.vinhphuc.gov.vn/van-ban-di/xem-van-ban-di-chi-tiet?id={int(item['ma_van_ban_di_kc'])}&t=vb_di_da_phat_hanh_cua_cv&v=cv&xld={int(item['ma_xu_ly_di'])}"))
        self.table_widget.setSelectionMode(QAbstractItemView.SingleSelection)
        self.table_widget.setStyleSheet("QTableWidget::item:selected{ background-color: #771b90; }")

    def open_url(self, row, column):
        if column == 5:
            # Định dạng màu sắc cho toàn bộ dòng
            for col in range(self.table_widget.columnCount()):
                # Lấy ra item hiện tại
                item = self.table_widget.item(row, col)
                # Nếu ô đó chưa được khởi tạo, tạo một QTableWidgetItem mới
                if item is None:
                    item = QTableWidgetItem()
                    self.table_widget.setItem(row, col, item)
                # Đặt màu chữ là đỏ
                item.setForeground(QBrush(QColor('red')))

            # Mở URL khi người dùng click vào cột thứ 5
            url = QUrl(self.table_widget.item(row, column).text())
            QDesktopServices.openUrl(url)

    def thay_doi_kich_thuoc_cot(self):
        self.table_widget.setColumnWidth(0, 200)
        self.table_widget.setColumnWidth(1, 500)
        self.table_widget.setColumnWidth(2, 200)
        self.table_widget.setColumnWidth(3, 110)
        self.table_widget.setColumnWidth(4, 150)
        self.table_widget.setColumnWidth(5, 320)  # Đặt kích thước cột thứ 4

    def __init__(self):
        super().__init__()
        # Tạo QTableWidget
        self.table_widget = QTableWidget()
        self.find = QtWidgets.QLineEdit(self.table_widget)
        self.find.setGeometry(QtCore.QRect(1500, 0, 150, 60))
        self.find.textChanged.connect(self.search)
        self.them_du_lieu_vao_bang(res)
        self.thay_doi_kich_thuoc_cot()
        self.table_widget.resizeRowsToContents()

    def search(self, text):
        count = 0
        for i in range(self.table_widget.rowCount()):
            self.table_widget.setRowHidden(i, True)
        for i in range(self.table_widget.rowCount()):
            for j in range(self.table_widget.columnCount()):
                item = self.table_widget.item(i, j)
                match = text.lower() in item.text().lower()
                if match:
                    self.table_widget.setRowHidden(i, False)
                    count += 1
                    break


def main():
    app = QApplication(sys.argv)
    main_window = MainWindow()
    main_window.show()
    change_workspace('pyqt')
    sys.exit(app.exec())


if __name__ == "__main__":
    main()  # %%