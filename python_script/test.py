import os
import pandas as pd

os.chdir("/home/<USER>/Dropbox/hnd/csv_source")

df_cchnd = pd.read_csv("cchnd.csv", dtype=str)


def hoan_doi_cot_theo_dieu_kien(
    df: pd.<PERSON><PERSON>rame,
    cot_dieu_kien: str,
    ky_tu_kiem_tra: str,
    cac_cap_cot_hoan_doi: dict,
) -> pd.DataFrame:
    """
    Kiểm tra giá trị trong một cột điều kiện, nếu chứa ký tự cụ thể thì hoán đổi giá trị giữa các cặp cột.

    Parameters
    ----------
    df : pd.DataFrame
        DataFrame cần xử lý
    cot_dieu_kien : str
        Tên cột dùng để kiểm tra điều kiện
    ky_tu_kiem_tra : str
        Ký tự cần kiểm tra trong cột điều kiện
    cac_cap_cot_hoan_doi : dict
        Dictionary chứa các cặp cột cần hoán đổi (key: cột thứ nhất, value: cột thứ hai)

    Returns
    -------
    pd.DataFrame
        DataFrame sau khi đã hoán đổi giá trị giữa các cột
    """
    # Tạo bản sao của DataFrame để không ảnh hưởng đến dữ liệu gốc
    df_result = df.copy()

    # Lọc các hàng thỏa mãn điều kiện (cột điều kiện chứa ký tự kiểm tra)
    mask = df_result[cot_dieu_kien].str.contains(ky_tu_kiem_tra, na=False)

    # Thực hiện hoán đổi giá trị giữa các cặp cột
    for cot1, cot2 in cac_cap_cot_hoan_doi.items():
        # Lưu tạm giá trị của cột 1
        temp_values = df_result.loc[mask, cot1].copy()
        # Gán giá trị từ cột 2 sang cột 1
        df_result.loc[mask, cot1] = df_result.loc[mask, cot2]
        # Gán giá trị đã lưu tạm từ cột 1 sang cột 2
        df_result.loc[mask, cot2] = temp_values
    df_result.to_csv("cchnd.csv", index=False)

    return df_result


# Ví dụ sử dụng:
# Kiểm tra nếu cột 'trinh do cm' chứa ký tự '/' thì hoán đổi các cặp cột
cac_cap_can_hoan_doi = {
    "trinh do cm": "thu tuc",
}
df_cchnd = hoan_doi_cot_theo_dieu_kien(
    df_cchnd, "trinh do cm", "/", cac_cap_can_hoan_doi
)
