import glob
import os
import sys
import functools
import tempfile
from loguru import logger
import shlex
import shutil
import socket
import subprocess
import time
import tkinter as tk
import zipfile
from datetime import datetime, timedelta
from tkinter import messagebox
import traceback
import pandas as pd
import pyautogui
import pyperclip
import regex
import requests
from PyPDF2 import PdfMerger, PdfReader
from playwright._impl._errors import TargetClosedError
from typing import Optional
from playwright.sync_api import <PERSON><PERSON>, <PERSON><PERSON><PERSON>, BrowserContext, Page
import unidecode
from all_pickle import save_last_dict
from typing import Callable, TypeVar, ParamSpec
import inspect

LIST_KHONG_BS_HS = []


os.chdir("/home/<USER>/Dropbox/hnd/csv_source")


def is_file_created_today(file_path):
    try:
        # L<PERSON>y thời gian hiện tại
        today = datetime.now().date()

        # Lấy thời gian tạo file
        creation_time = datetime.fromtimestamp(os.path.getctime(file_path)).date()

        # So sánh ngày tạo file với ngày hiện tại
        return today == creation_time
    except FileNotFoundError:
        return False  # File không tồn tại


def get_list_subfolder(path):
    return [
        os.path.join(path, f)
        for f in os.listdir(path)
        if os.path.isdir(os.path.join(path, f))
    ]


def change_xa(dia_chi):
    from csv_load_and_export import CsvLoaderFactory

    df_xa = CsvLoaderFactory.create_basic_loader().load_df("cvxa", "xa cu")
    xa = (
        dia_chi.rsplit(",", 3)[1]
        .replace("xã", "")
        .replace("phường", "")
        .replace("thị trấn", "")
        .strip()
    )
    dc_goc = dia_chi.rsplit(",", 3)[0]
    dc_moi = df_xa.loc[xa, "xa moi"]
    dc_new = dc_goc + ", " + dc_moi + ", tỉnh Phú Thọ"

    return dc_new


def chuyen_doi_noi_dung_file_espanso():
    """
    Hàm này sẽ đọc file /home/<USER>/Dropbox/hnd/dotfiles/config/espanso/match/all.yml,
    chuyển đổi các địa chỉ trong file theo hàm change_xa, và ghi lại file.
    Giả sử mỗi dòng chứa một địa chỉ cần chuyển đổi, hoặc bạn có thể điều chỉnh logic
    cho phù hợp với cấu trúc thực tế của file YAML.
    """
    input_path = "/home/<USER>/Dropbox/hnd/dotfiles/config/espanso/match/all.yml"
    output_lines = []
    with open(input_path, "r", encoding="utf-8") as f:
        for line in f:
            # Giả sử địa chỉ nằm sau dấu ':' (YAML key: value)
            if ":" in line:
                key, value = line.split(":", 1)
                value_strip = value.strip()
                # Nếu value có vẻ là một địa chỉ (có dấu phẩy), thì chuyển đổi
                if "," in value_strip:
                    try:
                        value_new = change_xa(value_strip)
                        output_lines.append(f"{key}: {value_new}\n")
                    except Exception:
                        # Nếu không chuyển đổi được thì giữ nguyên
                        output_lines.append(line)
                else:
                    output_lines.append(line)
            else:
                output_lines.append(line)
    # Ghi lại file
    with open(input_path, "w", encoding="utf-8") as f:
        f.writelines(output_lines)


def open_latex_with_tmux(path, window_name):
    command = f"nvim {path}"
    subprocess.run(["tmux", "new-window", "-t", "hnd:", "-n", window_name, command])
    subprocess.run("wmctrl -xa terminator.Terminator", shell=True)


# def get_ba_cap_dia_chi(text):
#     # Đảo ngược chuỗi và tách theo dấu phẩy
#     parts = text[::-1].split(",")
#
#     # Lấy 3 phần tử cuối (hoặc ít hơn nếu không đủ 3)
#     last_three = parts[:3]
#
#     # Đảo ngược lại từng phần tử và nối chúng lại với dấu phẩy
#     result = ",".join(part[::-1] for part in last_three[::-1])
#
#     return result


def thay_the_xa(ten_xa):
    mapping = {
        # Thành phố Phúc Yên
        "Trưng Trắc": "Hai Bà Trưng",
        "Trưng Nhị": "Hai Bà Trưng",
        # Huyện Vĩnh Tường
        "Việt Xuân": "Sao Đại Việt",
        "Bồ Sao": "Sao Đại Việt",
        "Cao Đại": "Sao Đại Việt",
        "Tân Tiến": "Đại Đồng",
        "Lý Nhân": "An Nhân",
        "An Tường": "An Nhân",
        "Vân Xuân": "Lương Điền",
        "Bình Dương": "Lương Điền",
        "Vĩnh Ninh": "Vĩnh Phú",
        "Phú Đa": "Vĩnh Phú",
        "Vĩnh Sơn": "Thổ Tang",
        "Tam Phúc": "Vĩnh Tường",
        # Huyện Sông Lô
        "Nhạo Sơn": "Tam Sơn",
        "Như Thụy": "Tam Sơn",
        "Bạch Lưu": "Hải Lựu",
        # Huyện Yên Lạc
        "Hồng Phương": "Hồng Châu",
        # Huyện Lập Thạch
        "Đình Chu": "Tây Sơn",
        "Triệu Đề": "Tây Sơn",
        # Huyện Tam Dương
        "Vân Hội": "Hội Thịnh",
        "Hợp Thịnh": "Hội Thịnh",
    }

    for old, new in mapping.items():
        ten_xa = ten_xa.replace(f"{old}", f"{new}")

    return ten_xa


def send_error_to_telegram(func):
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except TargetClosedError:
            # Bỏ qua lỗi này và không gửi thông báo
            print("Trình duyệt đã bị đóng đột ngột. Không gửi thông báo.")
            raise  # Ném lại ngoại lệ này
        except Exception as e:
            if "Target page, context or browser has been closed" not in str(e):
                tele = TelegramSend("hnd")
                # Lấy frame của caller (người gọi hàm)
                caller_frame = inspect.currentframe().f_back
                # Lấy tên file gốc được gọi
                script_name = os.path.basename(caller_frame.f_code.co_filename)
                error_message = f"Lỗi trong file {script_name}, hàm {func.__name__}:\n\n{str(e)}\n\nChi tiết lỗi:\n{traceback.format_exc()}"
                tele.send_message_warp(error_message)
            else:
                print("Trình duyệt đã bị đóng đột ngột. Không gửi thông báo.")
            raise  # Ném lại ngoại lệ sau khi gửi thông báo hoặc in thông báo

    return wrapper


def change_workspace(workspace_name):
    # Lệnh để lấy danh sách các workspaces và tìm chỉ số của workspace có tên bạn muốn
    command = "wmctrl -d"
    workspaces = (
        subprocess.check_output(command, shell=True).decode().strip().splitlines()
    )

    for workspace in workspaces:
        if workspace_name in workspace:
            # Tìm chỉ số của workspace từ output
            workspace_number = workspace.split()[0]
            command = f"wmctrl -s {workspace_number}"
            subprocess.run(command, shell=True, check=True)


class AutoDayVb:
    def __init__(self, string, trichyeu, loai_vb, input_text=None):
        self.playwright: Optional[Playwright] = None
        self.string: str = string
        self.file_vb_word: str = string.split("-@-")[0]
        self.file_dinhkem: Optional[str] = (
            string.split("-@-")[1] if len(string.split("-@-")) > 1 else None
        )
        self.file_vb_pdf = os.path.join(
            "/home/<USER>/Dropbox/hnd/latexall/vb_cho_in",
            os.path.basename(self.file_vb_word).replace(".docx", ".pdf"),
        )
        shutil.copy(self.file_vb_pdf, "/home/<USER>/Desktop")
        self.trichyeu = trichyeu
        self.input_text = input_text
        self.loai_vb = loai_vb
        self.browser: Browser = None
        self.context: BrowserContext = None
        self.page: Page = None
        self.store_file = "qlvb.json"

    def setup(self):
        self.playwright = sync_playwright().start()
        self.browser = self.playwright.chromium.launch(headless=False)

    def login(self):
        self.context = self.browser.new_context()
        self.page = self.context.new_page()
        self.page.goto("https://iqlvb.vinhphuc.gov.vn/")
        self.page.wait_for_load_state("networkidle", timeout=60000)
        self.page.fill("#usernameUserInput", "SonLT5")
        self.page.fill("#password", "vinhphuc@3009")
        # self.page.fill("#usernameUserInput", "anhnd2")
        # self.page.fill("#password", "12345678a@")
        self.page.click("button[role='button']")
        self.page.wait_for_selector("//span[contains(text(),'SỞ Y TẾ TỈNH VĨNH PHÚC')]")

    def day_van_ban(self):
        self.page.click("a[href='van-ban-di/them-moi-van-ban-di']")
        self.page.wait_for_selector("//h3[normalize-space()='THÔNG TIN CHÍNH']")
        self.page.fill("#trich_yeu", self.trichyeu)
        self.page.click("(//span[@role='presentation'])[2]")
        self.page.fill("(//input[@role='textbox'])[2]", self.loai_vb)
        self.page.press("(//input[@role='textbox'])[2]", "Enter")
        self.page.click("(//span[@role='combobox'])[5]")
        self.page.locator("li.select2-results__option").filter(
            has_text="Nguyễn Đắc Ca"
        ).click()
        time.sleep(1)

        # Tạo và cấu hình cửa sổ dialog
        pyautogui.click(648, 678)
        upload_desktop_file("docx")
        # Đợi cho đến khi biểu tượng xem (icon eye) xuất hiện
        try:
            self.page.wait_for_selector(
                "(//a[@class='fa fa-eye icon_eye ng-scope'])[1]",
                timeout=60000,  # Timeout 60 giây
            )
        except Exception as e:
            print(f"Không thể tìm thấy biểu tượng xem (icon eye): {str(e)}")

        pyautogui.click(648, 678)
        upload_desktop_file("pdf")
        # Tạo và cấu hình cửa sổ dialog để thông báo
        time.sleep(2)

    def dinh_kem_phuc_dap(self):
        self.page.click("text=Chọn văn bản liên qu")
        six_months_ago = datetime.now() - timedelta(days=180)
        formatted_date = six_months_ago.strftime("%Y-%m-%d")
        self.page.fill("#start_date_vblq", formatted_date)
        self.page.fill("//input[@ng-model='vblq_new.so_ky_hieu']", self.input_text)
        self.page.press("//input[@ng-model='vblq_new.so_ky_hieu']", "Enter")
        time.sleep(10)
        # self.page.click("input[id^='tb_vbde_phuc_dap_chk_']",timeout=60000)
        self.page.click("input[id^='tb_vb_lien_quan_chk_']", timeout=60000)
        self.page.click("(//button[@id='btn_close'])[3]", timeout=60000)

    def chuyen_lanh_dao_phong(self):
        # self.page.wait_for_selector(
        #     "//td[@class='text-center']//a[@class='fa fa-eye icon_eye ng-scope']",
        #     timeout=160000,
        # ) #doi cho load xong
        self.page.click(
            "//button[@ng-click=\"fn_luu_va_chuyen('cv_chuyen_lanh_dao_phong')\"]"
        )  # bam nut chuyen
        time.sleep(2)
        self.page.wait_for_selector(
            "//span[contains(text(),'Chuyển')]", timeout=10000
        )  # Thêm timeout 10 giây
        self.page.click("//span[contains(text(),'Chuyển')]")

    def cleanup(self):
        if self.browser:
            self.browser.close()
        if self.playwright:
            self.playwright.stop()
        # Xóa tất cả file PDF trong thư mục Desktop
        pdf_files = glob.glob("/home/<USER>/Desktop/*.pdf")
        for file in pdf_files:
            os.remove(file)
        send_notification("Đã gửi văn bản {}".format(self.trichyeu))
        send_notification("Clean up Folder Desktop")

    def run(self):
        self.setup()
        self.login()
        self.day_van_ban()
        if self.input_text:
            self.dinh_kem_phuc_dap()
        self.chuyen_lanh_dao_phong()
        self.cleanup()


def convert_ngay(ngay):
    if not ngay:
        return ""
    if "/" in ngay and len(ngay) < 10:
        return ngay
    ngay = ngay.replace("/", "") if "/" in ngay else ngay
    if str(ngay)[2:4] in ["01", "02", "10", "11", "12"]:
        a = str(ngay)[2:4]
    else:
        a = str(ngay)[3:4]
    ngay = str(ngay)[:2] + "/" + a + "/" + str(ngay)[-4:]
    return ngay


def get_bottom_values(df, index_values, column_name) -> str:
    """
    Lấy giá trị cuối cùng của một cột trong dataframe
    df: dataframe
    index_values: chỉ số của hàng
    column_name: tên cột
    Returns:
        str: Giá trị cuối cùng của cột được chọn, trả về chuỗi rỗng nếu không tìm thấy
    """
    df.fillna("", inplace=True)
    try:
        values = df.at[index_values, column_name]
        if isinstance(values, str):
            return values
        else:
            return df.loc[index_values, column_name].iloc[-1]
    except (KeyError, IndexError):
        return ""


async def handle_download(download, download_path):
    path = f"{download_path}/{download.suggested_filename}"
    await download.save_as(path)
    print(f"Downloaded file saved to {path}")


T = TypeVar("T")  # Khai báo biến kiểu để sử dụng cho decorator
P = ParamSpec("P")  # Kiểu tham số của hàm


def setup_logging(
    log_filename: str,
) -> Callable[[Callable[P, T]], Callable[P, T]]:
    def decorator(func: Callable[P, T]) -> Callable[P, T]:
        @functools.wraps(func)
        def wrapper(*args: P.args, **kwargs: P.kwargs) -> T:
            # Xác định đường dẫn đầy đủ cho file log
            log_dir = "/home/<USER>/Dropbox"
            log_path: str = os.path.join(log_dir, log_filename)

            # Xóa file log cũ nếu tồn tại
            if os.path.exists(log_path):
                os.remove(log_path)

            try:
                # Ghi log bắt đầu thực thi hàm
                with open(log_path, "w", encoding="utf-8") as f:
                    current_time = datetime.now().strftime("%Y-%m-%dT%H:%M:%S.%f%z")
                    f.write(f"Time: {current_time}\n")
                    f.write(f"Level: INFO\n")
                    f.write(f"Message: Bắt đầu thực thi hàm {func.__name__}\n")
                    f.write("Exception: \n\n")

                # Thực thi hàm
                result = func(*args, **kwargs)

                # Ghi log kết quả thành công
                with open(log_path, "a", encoding="utf-8") as f:
                    current_time = datetime.now().strftime("%Y-%m-%dT%H:%M:%S.%f%z")
                    f.write(f"\nTime: {current_time}\n")
                    f.write(f"Level: INFO\n")
                    f.write(
                        f"Message: Hoàn thành thực thi hàm {func.__name__} thành công\n"
                    )
                    f.write("Exception: \n\n")

                return result

            except Exception as e:
                # Ghi log chi tiết lỗi
                with open(log_path, "a", encoding="utf-8") as f:
                    current_time = datetime.now().strftime("%Y-%m-%dT%H:%M:%S.%f%z")
                    f.write(f"\nTime: {current_time}\n")
                    f.write(f"Level: ERROR\n")
                    f.write(f"Message: Lỗi trong hàm {func.__name__}: {str(e)}\n")
                    f.write(f"Exception: {str(e)}\n\n")
                    f.write("Traceback:\n")
                    f.write(traceback.format_exc())

                # Mở file log
                command = f"nvim {log_path}"
                subprocess.run("wmctrl -xa terminator.Terminator", shell=True)
                subprocess.run(["tmux", "new-window", "-n", "log", command])

                # Chờ một chút để đảm bảo file được mở
                time.sleep(1)

                # Thoát chương trình (không qua cleanup)
                os._exit(1)

        return wrapper

    return decorator


def update_df_from_dict_by_index(df, file_csv, values, key):
    if key not in df.index:
        df.loc[key] = values
        df.to_csv(
            f"/home/<USER>/Dropbox/hnd/csv_source/{file_csv}",
            index_label=df.index.name,
        )
    else:
        df_update = pd.DataFrame([values], index=[key])  # cập nhật dataframe df2
        df.update(df_update)
        df.to_csv(
            f"/home/<USER>/Dropbox/hnd/csv_source/{file_csv}",
            index_label=df.index.name,
        )


def get_dict_from_index_df(df, index_values):
    """
    Lấy giá trị cuối cùng của một hàng trong dataframe
    df: dataframe
    index_values: chỉ số của hàng
    return: dictionary
    """
    df.fillna("", inplace=True)
    try:
        selected_values = df.loc[df.index == index_values].tail(1)
        return selected_values.to_dict(orient="records")[0]
    except:
        empty_dict = dict.fromkeys(df.columns, "")
        return empty_dict


def get_dict_from_column_df(df, values, column_name):
    """
    Lấy giá trị cuối cùng của một hàng trong dataframe
    df: dataframe
    index_values: chỉ số của hàng
    return: dictionary
    """
    df.fillna("", inplace=True)
    try:
        selected_values = df.loc[df[column_name] == values].tail(1)
        return selected_values.to_dict(orient="records")[0]
    except:
        empty_dict = dict.fromkeys(df.columns, "")
        return empty_dict


class FileZipManager:
    def __init__(self, path):
        self.path = path
        self.files = self.get_items()
        self.folders = [f.strip(".zip") for f in self.files]
        self.first_file = self.files[0]

    def get_items(self, extension=".zip"):
        return glob.glob(self.path + f"/*{extension}")

    def extract_all_zip_files(self, path=None):
        for file in self.files:
            with zipfile.ZipFile(file, "r") as zip_ref:
                zip_ref.extractall(path if path else file.strip(".zip"))
            os.remove(file)


class FolderManager:
    def __init__(self, path):
        self.path = path
        self.folders = self.get_items()

    def get_items(self, extension=""):
        return glob.glob(self.path + f"/*{extension}")

    def get_dict_mhs_path(self):
        file_dict = {}
        for thu_muc in self.folders:
            if os.path.isdir(thu_muc) and "+" not in thu_muc:
                file_dict[thu_muc.split("/")[-1].split("--")[0]] = thu_muc
        return file_dict

    def delete_done_folder(self, df):
        hs_dict = self.get_dict_mhs_path()
        dsxoa1 = [
            mahs for mahs in hs_dict if mahs not in list(df["ma ho so"])
        ]  # nhứng hồ sơ đã trả
        dsxoa2 = df[
            (df["da nhan"] == "1") & (df["ma ho so"].isin(hs_dict.keys()))
        ]  # những hồ sơ đã nhập thông tin
        dsxoa2 = dsxoa2["ma ho so"].tolist()
        dsxoa = dsxoa1 + dsxoa2
        # TODO xoá bỏ những thư mục da nhan
        for mhs in dsxoa:
            shutil.rmtree(hs_dict[mhs])
            print("ĐÃ XOÁ BỎ THƯ MỤC", hs_dict[mhs].split("\\")[-1])

    def delete_allfiles_and_passed_kp_folder(self, list_mhs):
        # Sử dụng glob để lấy tất cả các file và thư mục trong thư mục
        items = self.get_items()
        for item in items:
            # Kiểm tra nếu là file thì xoá
            if os.path.isfile(item):
                os.remove(item)
            # Kiểm tra nếu là thư mục, nếu rỗng thì xóa luôn, không thì kiểm tra điều kiện
            elif os.path.isdir(item):
                if not os.listdir(item):
                    shutil.rmtree(item)
                elif (is_hs_kp_da_tra(item, list_mhs)) and (
                    "BS" in item or "KP" in item
                ):
                    shutil.rmtree(item)


class PandasCsv:
    def __init__(self, data, index_col=None):
        self.index_col = index_col
        self.data = data
        # Kiểm tra xem 'data' là một string đường dẫn hay một DataFrame
        if isinstance(data, pd.DataFrame):
            self.df = data
            if index_col is not None:
                self.df.set_index(index_col, inplace=True)
        elif isinstance(data, str):
            self.df = pd.read_csv(data, dtype=str, index_col=index_col)
        else:
            raise ValueError(
                "The 'data' parameter must be a pandas DataFrame or a string path to a CSV file."
            )

    def split_col(self, col, sep, index, new_col=None):
        if new_col:
            self.df[new_col] = self.df[col].str.split(sep).str[index]
        else:
            self.df[col] = self.df[col].str.split(sep).str[index]

    def get_filter_by_mask(self, mask, col, df2=None):
        self.df = self.df.loc[mask, col]
        return self.df

    def fillna(self, values, col=None):
        if col:
            self.df[col].fillna(values, inplace=True)
        else:
            self.df.fillna(values, inplace=True)

    def dropna(self, col=None):
        if col:
            self.df.dropna(subset=[col], inplace=True)
        else:
            self.df = self.df[self.df.index.notnull()]

    def reset_index_drop(self):
        self.df = self.df.reset_index(drop=True)

    def insert_stt(self):
        self.df.insert(0, "stt", range(1, 1 + len(self.df)))

    def merge_left(self, df2, on, suffixes):
        if isinstance(df2, PandasCsv):
            df2 = df2.df
            self.df = pd.merge(self.df, df2, on=on, how="left", suffixes=suffixes)
        else:
            self.df = pd.merge(self.df, df2, on=on, how="left", suffixes=suffixes)

    def get_append_df(self, df2, ignore_index=True):
        colist = self.df.columns.tolist()
        if isinstance(df2, PandasCsv):
            df2 = df2.df  # Giả sử 'df' là thuộc tính DataFrame trong class PandasCsv
            df1 = pd.concat([self.df, df2], ignore_index=ignore_index)
        else:
            df1 = pd.concat([self.df, df2], ignore_index=ignore_index)
        df1 = df1[colist]
        return PandasCsv(df1)

    def set_index(self, col):
        self.df.set_index(col, inplace=True)

    def to_datetime(self, col, format_time):
        self.df[col] = pd.to_datetime(self.df[col], format=format_time, dayfirst=True)

    def format_datetime(self, col, format_string):
        self.df[col] = self.df[col].dt.strftime(format_string)

    def upper(self, col):
        self.df[col] = self.df[col].str.upper()

    def get_df_by_mask(self, mask):
        return PandasCsv(self.df.loc[mask])

    def get_values_by_mask(self, mask, col):
        return self.df.loc[mask, col]

    def set_col_to_str(self, col):
        if isinstance(col, list):
            for c in col:
                self.df[c] = self.df[c].astype(str)
        else:
            self.df[col] = self.df[col].astype(str)

    def get_tail_df(self, number):
        return PandasCsv(self.df.tail(number))

    def get_df_between_time(self, start, end, col):
        self.df[col] = pd.to_datetime(
            self.df[col], format="%d/%m/%Y", dayfirst=True, errors="coerce"
        )
        mask = (self.df[col] >= start) & (self.df[col] <= end)
        return PandasCsv(self.df[mask])

    def get_num_contains(
        self, col, value
    ):  # get number of rows that contain value in col
        self.df[col] = self.df[col].str.upper()
        return str(self.df[col].apply(lambda x: value in x).sum()).zfill(2)

    def get_num_contains_2_col(self, col1, val1, col2):
        self.df[col1] = self.df[col1].str.upper()
        return str(
            self.df[
                (self.df[col1].apply(lambda x: val1 in x)) & (self.df[col2].isnull())
            ].shape[0]
        ).zfill(2)

    def get_num_eq(self, col, value):  # get number of rows that equal value in col
        self.df[col] = self.df[col].str.upper()
        return str(self.df[col].apply(lambda x: x == value).sum()).zfill(2)

    def set_column(self, column_list):
        self.df = self.df[column_list]

    def sort_time_by_han(self):
        self.df["han"] = self.df["han"].apply(format_date_trying)
        self.df["is_date"] = self.df["han"].apply(lambda x: isinstance(x, pd.Timestamp))
        # Sắp xếp DataFrame: những hàng không phải ngày giờ lên trên cùng, sau đó sắp xếp theo 'han'
        self.df = self.df.sort_values(by=["is_date", "han"], ascending=[True, True])
        # Bỏ cột tạm thời
        self.df = self.df.drop("is_date", axis=1)
        self.df["han"] = self.df["han"].apply(
            lambda x: (
                x.strftime("%d/%m/%Y %H:%M:%S") if isinstance(x, pd.Timestamp) else x
            )
        )

    def set_loc(self, index_value, value, col_set, col_index=None):
        if col_index:
            self.df.loc[self.df[col_index] == index_value, col_set] = value
        else:
            self.df.loc[index_value, col_set] = value

    def get_at(self, index_value, col_set, col_index=None):
        if col_index:
            return self.df.at[self.df[col_index] == index_value, col_set]
        else:
            return self.df.at[index_value, col_set]

    def get_num_loc(self, number, col_set=None):
        if col_set:
            return self.df[col_set][number]
        else:
            return self.df.index[number]

    def df_update_dict(self, dict_to_map, column_to_map):
        mapped_series = self.df["ma ho so"].map(dict_to_map)
        self.df[column_to_map].update(mapped_series)

    def get_df_list(self, mask, col=None):
        if isinstance(col, list):
            return self.df.loc[mask, col]
        else:
            if not col:
                return self.df.loc[mask]["ma ho so"].tolist()
            else:
                return self.df.loc[mask, col].tolist()

    def update_with_vlookup(
        self, df_source, key_column, value_column, new_column_name=None
    ):
        """
        df_source: DataFrame nguồn chứa dữ liệu cần cập nhật.
        key_column: tên cột dùng để tra cứu (vlookup).
        value_column: tên cột trong df_source mà từ đó bạn muốn "vlookup" giá trị.
        new_column_name: tên cột mới trong df_target mà giá trị từ df_source sẽ được cập nhật vào. Nếu None, cập nhật trực tiếp vào value_column.
        """
        # Tạo một cột mới trong df_target nếu cần
        if new_column_name is None:
            new_column_name = value_column

        # Tạo một bản đồ từ df_source để tra cứu nhanh
        if isinstance(df_source, PandasCsv):
            lookup_dict = df_source.df.set_index(key_column)[value_column].to_dict()
        else:
            lookup_dict = df_source.set_index(key_column)[value_column].to_dict()

        # Áp dụng bản đồ để cập nhật df_target bằng phương thức .map()
        self.df[new_column_name] = self.df[key_column].map(lookup_dict)

    def set_loc_with_mask(self, mask, value, col=None):
        if col:
            self.df.loc[mask, col] = value
        else:
            self.df.loc[mask] = value

    def day_to_weekday(self, week_col, day_col):
        self.df[week_col] = self.df[day_col].apply(get_weekday)

    def to_csv(self, path, index=None):
        if index:
            self.df.to_csv(path, index_label=index)
        else:
            self.df.to_csv(path, index=False)

    def __str__(self):
        return str(self.df)

    def __getitem__(self, col):
        return self.df[col]

    def __setitem__(self, key, value):
        self.df[key] = value

    def update_han_vande_trangthai(self, dict_to_map, van_de_hs, trangthai):
        self.df_update_dict(dict_to_map, "han")
        self.df.loc[self.df["ma ho so"].isin(dict_to_map.keys()), "van de hs"] = (
            van_de_hs
        )
        self.df.loc[self.df["ma ho so"].isin(dict_to_map.keys()), "trang thai"] = (
            trangthai
        )


def open_kq_with_zathura():
    subprocess.Popen(["zathura", TextProcess.PDF_FILE])


def replace_variables(text, values):
    for key, value in values.items():
        placeholder = f"<{key}>"
        text = text.replace(placeholder, str(value))
    return text


class TextProcess:
    LATEX_FILE = "/home/<USER>/Dropbox/hnd/latexall/mylatex/mylatex.tex"
    PDF_FILE = "/home/<USER>/Dropbox/hnd/latexall/mylatex/mylatex.pdf"

    def __init__(self, file_text):
        self.file_name = file_text
        self.file_path = (
            f"/home/<USER>/Dropbox/hnd/latexall/source_latex/{file_text}.tex"
        )

    def get_text(self):
        with open(self.file_path, "r") as file:
            return file.read()

    def format_text(self, values_dict):
        """
        format bằng cách replace <> ghi ra mylatex.tex
        """
        text = self.get_text()
        text_formatted = replace_variables(text, values_dict)
        write_text(text_formatted, TextProcess.LATEX_FILE)

    def format_old_text(self, values_dict):
        """
        format truyền thống (DEPRECATED) ghi ra mylatex.tex
        """
        text = self.get_text()
        text_formatted = text.format(**values_dict)
        write_text(text_formatted, TextProcess.LATEX_FILE)
        return text

    def copy_pdf_to_dropbox(self, new_name=None):
        target_name = new_name or self.file_name
        target_path = f"/home/<USER>/Dropbox/{target_name}.pdf"
        shutil.copy(TextProcess.PDF_FILE, target_path)

    def copy_pdf_to_pcloud(self, new_name=None):
        target_name = new_name or self.file_name
        target_path = f"/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pdf_documents/kq/{target_name}.pdf"
        shutil.copy(TextProcess.PDF_FILE, target_path)

    def copy_pdf_to_kq(self, name=None):
        if not name:
            name = self.file_name
        shutil.copy(
            TextProcess.PDF_FILE,
            f"/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pdf_documents/kq/{name}.pdf",
        )

    @staticmethod
    def compile_latex_confirm():
        # Xóa các file aux cũ
        aux_files = ["mylatex.aux", "mylatex.log", "mylatex.out"]
        latex_dir = "/home/<USER>/Dropbox/hnd/latexall/mylatex"

        for aux_file in aux_files:
            file_path = os.path.join(latex_dir, aux_file)
            if os.path.exists(file_path):
                os.remove(file_path)

        # Compile latex file nhiều lần để xử lý cross-references
        for _ in range(2):
            subprocess.run(
                [
                    "xelatex",
                    f"-output-directory={latex_dir}",
                    TextProcess.LATEX_FILE,
                ]
            )

    def compile_latex_with_pdflatex(self):
        subprocess.run(
            [
                "pdflatex",
                TextProcess.LATEX_FILE,
            ]
        )

    def copy_pdf_to_path(self, path):
        shutil.copy(TextProcess.PDF_FILE, path)

    def print_pdf(self):
        print_pdf(TextProcess.PDF_FILE)

    @staticmethod
    def copy_pdf_to_kq_and_send_hnd(filename):
        path = f"/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pdf_documents/kq/{filename}.pdf"
        shutil.copy(TextProcess.PDF_FILE, path)
        tele = TelegramSend("hnd")
        tele.send_file_warp(path)

    @staticmethod
    def auto_day_van_ban_sure(tieu_de, loai_vb, mhs, text):
        auto_text_to_ioffice_sure(tieu_de, loai_vb, mhs, text)

    @staticmethod
    def auto_day_van_ban(tieu_de, loai_vb, mhs):
        auto_text_to_ioffice(tieu_de, loai_vb, mhs)

    @staticmethod
    def auto_day_van_ban_pd(tieu_de, so_vanban, loai_vb):
        auto_text_to_ioffice_pd(tieu_de, so_vanban, loai_vb)

    @staticmethod
    def copy_latex_file(path):
        shutil.copy(TextProcess.LATEX_FILE, path)

    @staticmethod
    def compile_latex(mylatex=None):
        if not mylatex:
            mylatex = "mylatex"
        subprocess.run(
            [
                "xelatex",
                "-output-directory=/home/<USER>/Dropbox/hnd/latexall/mylatex",
                f"/home/<USER>/Dropbox/hnd/latexall/mylatex/{mylatex}.tex",
            ]
        )

    @staticmethod
    def compile_pdflatex():
        subprocess.run(
            [
                "pdflatex",
                f"-output-directory=/home/<USER>/Dropbox/hnd/latexall/mylatex",
                TextProcess.LATEX_FILE,
            ]
        )

    @staticmethod
    def open_pdf_with_mupdf():
        subprocess.Popen(["mupdf", TextProcess.PDF_FILE])

    @staticmethod
    def open_pdf_with_okular():
        subprocess.Popen(["okular", TextProcess.PDF_FILE])


# class WindowManager:
#     def __init__(self):
#         self.class =


class PdfManager:
    def __init__(self, file_path):
        self.file = file_path

    def merge_pdf(self, output_file):
        pdf_merger = PdfMerger()
        if os.path.isdir(self.file):
            files = glob.glob(self.file + "/*.pdf")
            for file in files:
                pdf_merger.append(PdfReader(file, False))
            with open(output_file, "wb") as output_pdf_path:
                pdf_merger.write(output_pdf_path)
        else:
            show_message("ERROR", "File is not a list")


def check_connection(host="api.telegram.org", port=443, timeout=5):
    try:
        socket.setdefaulttimeout(timeout)
        socket.socket(socket.AF_INET, socket.SOCK_STREAM).connect((host, port))
        return True
    except socket.error as ex:
        print(f"Network error: {ex}")
        return False


def all_send_warp(chat_id: str, bot_token: str, file_path: str) -> bool:
    """
    Gửi tài liệu đến Telegram sử dụng requests.

    Parameters
    ----------
    chat_id : str
        ID của cuộc trò chuyện
    bot_token : str
        Token của bot Telegram
    file_path : str
        Đường dẫn đến file cần gửi

    Returns
    -------
    bool
        True nếu gửi thành công, False nếu có lỗi
    """
    try:
        url = f"https://cool-butterfly-9ded.tungson92dkh.workers.dev/bot{bot_token}/sendDocument"
        with open(file_path, "rb") as file:
            files = {"document": file}
            data = {"chat_id": chat_id}
            response = requests.post(url, files=files, data=data)
            response.raise_for_status()  # Kiểm tra lỗi HTTP
        return True
    except FileNotFoundError:
        logger.error(f"Không tìm thấy file: {file_path}")
        return False
    except requests.exceptions.RequestException as e:
        logger.error(f"Lỗi khi gửi file {file_path}: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"Lỗi không xác định khi gửi file {file_path}: {str(e)}")
        return False


def show_message(label, message) -> None:
    # Tạo cửa sổ gốc nhưng ẩn đi
    root = tk.Tk()
    root.withdraw()

    # Hiển thị message box
    messagebox.showinfo(label, message)

    # Đóng cửa sổ gốc
    root.destroy()


def copy_file_to_clip(path):
    subprocess.run(["cb", "copy", path])


def run_command_new_tmux(command, window_name):
    subprocess.run("wmctrl -xa terminator.Terminator", shell=True)
    subprocess.run(["tmux", "new-window", "-n", window_name, command])


class TelegramSend:
    def __init__(self, name: str) -> None:
        """
        Khởi tạo đối tượng TelegramSend.

        Args:
            name: Tên cấu hình (ví dụ: "hnd", "qlhn").
        """
        self.name = name
        if name == "qlhn":
            self.token = "7488802978:AAEVqjw8XbYRummVLPYZfIW-sK-pnZkwnx8"
            self.chat_id = "-1001587612405"
        elif name == "hs":  # ho so lan son
            self.token = "5301034041:AAF3VQT11V-NH8Wo-KdAbCxRIzfrpxKqE4M"
            self.chat_id = "-1002431393882"
        elif name == "hnd":
            self.token = "2143046655:AAE5iwz9KY8ofLZ_Vm3xhBrjpEyILDYzRy8"
            self.chat_id = "-1001512252982"
        elif name == "pdfhs":
            self.token = "5517353700:AAGbzj1BW7Gg11wTBrXIUQEUXmLez_NodcA"
            self.chat_id = "-1002238974030"
        elif name == "tbhs":  # tb ho so gui den
            self.token = (
                "5301034041:AAF3VQT11V-NH8Wo-KdAbCxRIzfrpxKqE4M"  # telegram token
            )
            self.chat_id = "-1001083762800"
        elif name == "datrakq":
            self.token = (
                "5301034041:AAF3VQT11V-NH8Wo-KdAbCxRIzfrpxKqE4M"  # telegram token
            )
            self.chat_id = "-1001083762800"

    def send_message_warp(self, message: str) -> bool:
        """
        Gửi tin nhắn và bắt lỗi.

        Args:
            message: Nội dung tin nhắn.

        Returns:
            True nếu gửi thành công, False nếu có lỗi.
        """
        try:
            url = f"https://cool-butterfly-9ded.tungson92dkh.workers.dev/bot{self.token}/sendMessage"
            data = {"chat_id": self.chat_id, "text": message}
            response = requests.post(url, data=data)
            response.raise_for_status()  # Kiểm tra lỗi HTTP
            return True
        except requests.exceptions.RequestException as e:
            logger.error(f"Lỗi khi gửi tin nhắn: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Lỗi không xác định khi gửi tin nhắn: {str(e)}")
            return False

    def send_message_normal(self, message: str) -> None:
        """
        Gửi tin nhắn (không bắt lỗi chi tiết).

        Args:
            message: Nội dung tin nhắn.
        """
        url = f"https://cool-butterfly-9ded.tungson92dkh.workers.dev/bot{self.token}/sendMessage"
        data = {"chat_id": self.chat_id, "text": message}
        requests.post(url, data=data)

    def send_file_warp(self, file_path: str) -> bool:
        """
        Gửi file và bắt lỗi.

        Args:
            file_path: Đường dẫn tới file.

        Returns:
            True nếu gửi thành công, False nếu có lỗi.
        """
        try:
            url = f"https://cool-butterfly-9ded.tungson92dkh.workers.dev/bot{self.token}/sendDocument"
            with open(file_path, "rb") as file:
                files = {"document": file}
                data = {"chat_id": self.chat_id}
                response = requests.post(url, files=files, data=data)
                response.raise_for_status()  # Kiểm tra lỗi HTTP
            return True
        except FileNotFoundError:
            logger.error(f"Không tìm thấy file: {file_path}")
            return False
        except requests.exceptions.RequestException as e:
            logger.error(f"Lỗi khi gửi file {file_path}: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Lỗi không xác định khi gửi file {file_path}: {str(e)}")
            return False

    def send_file_normal(self, file_path: str) -> None:
        """
        Gửi file (không bắt lỗi chi tiết).

        Args:
            file_path: Đường dẫn tới file.
        """
        url = f"https://cool-butterfly-9ded.tungson92dkh.workers.dev/bot{self.token}/sendDocument"
        with open(file_path, "rb") as file:
            files = {"document": file}
            data = {"chat_id": self.chat_id}
            requests.post(url, files=files, data=data)


def minimize(classy):
    try:
        get_id_command = f"wmctrl -lx | grep '{classy}' | awk '{{print $1}}'"
        window_id = subprocess.getoutput(get_id_command)
        subprocess.run(f"xdotool windowminimize {window_id}", shell=True)
    except:
        pass


def send_notification(message, tele=False):
    notify_text = "@@@@@@@@@@@@@@@@@@@@@@@@@@@@\n\n" + message
    notify_command = ["notify-send", notify_text]
    subprocess.run(notify_command)
    print(message)
    if tele:
        hnd = TelegramSend("hnd")
        hnd.send_message_warp(message)


def printer_method(printer_name, file_pdf):
    with tempfile.TemporaryDirectory() as temp_dir:
        output_pdf = os.path.join(temp_dir, "output.pdf")

        # Sử dụng qpdf để loại bỏ thẻ /Rotate và các thông tin xoay trang
        subprocess.run(
            [
                "qpdf",
                "--rotate=0:1-z",
                "--remove-restrictions",
                "--object-streams=generate",
                "--linearize",
                file_pdf,
                output_pdf,
            ],
            check=True,
        )

        # In file PDF đã xử lý
        subprocess.run(["lp", "-d", printer_name, output_pdf], check=True)


def print_2_turn(file_pdf, page_break):
    page_break = int(page_break)

    with tempfile.TemporaryDirectory() as temp_dir:
        output_pdf = os.path.join(temp_dir, "output.pdf")

        # Sử dụng qpdf để loại bỏ thẻ /Rotate và các thông tin xoay trang
        subprocess.run(
            [
                "qpdf",
                "--rotate=0:1-z",
                "--remove-restrictions",
                "--object-streams=generate",
                "--linearize",
                file_pdf,
                output_pdf,
            ],
            check=True,
        )

        # In file PDF đã xử lý
        cmd1 = f"lp -P 1-{page_break} {shlex.quote(output_pdf)}"
        cmd2 = f"lp -P {page_break + 1}- {shlex.quote(output_pdf)}"
        subprocess.run(cmd1, shell=True, check=True)
        subprocess.run(cmd2, shell=True, check=True)


def check_printer_connected():
    try:
        # Chạy lệnh lpstat để liệt kê các máy in và lấy thông tin về trang thai của chúng
        result = subprocess.check_output(["lpstat", "-p"]).decode("utf-8")
        # Nếu đầu ra của lpstat không chứa 'printer' thì không có máy in nào được kết nối
        return "printer" not in result
    except subprocess.CalledProcessError as e:
        # Nếu có lỗi khi chạy lệnh (ví dụ: lệnh không tồn tại), trả về True
        return True


def dataframe_to_latex(df):
    latex_str = ""
    for _, row in df.iterrows():
        row_str = " & ".join(map(str, row.values))
        row_str += " \\\\"
        latex_str += row_str + "\n"
    return latex_str


def is_hs_kp_da_tra(item, list_mhs):
    return item.split("/")[-1].split("--")[0] not in list_mhs


def format_date_trying(date_str):
    try:
        # Thử chuyển đổi dùng định dạng AM/PM
        return pd.to_datetime(date_str, format="%d/%m/%Y %I:%M:%S %p", errors="raise")
    except ValueError:
        try:
            # Nếu lỗi, thử chuyển đổi dùng định dạng 24 giờ
            return pd.to_datetime(date_str, format="%d/%m/%Y %H:%M:%S", errors="raise")
        except ValueError:
            # Nếu vẫn lỗi, trả lại giá trị gốc
            return date_str


def get_weekday(date_str):
    try:
        date_obj = datetime.strptime(date_str, "%d/%m/%Y %H:%M:%S")
        weekdays = [
            "Thứ hai",
            "Thứ ba",
            "Thứ tư",
            "Thứ năm",
            "Thứ sáu",
            "Thứ bảy",
            "Chủ nhật",
        ]
        return weekdays[date_obj.weekday()]
    except (ValueError, TypeError):
        return ""


def get_new_hs_folder_name(mhs, df, fid):
    if fid:
        fid = f"--{fid}"
    nguoidangky = df.loc[df["ma ho so"] == mhs, "ten nguoi ptcm"].iloc[0]
    thutuc = df.loc[df["ma ho so"] == mhs, "thu tuc"].iloc[0]
    new_hsbs_td_path = f"/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pdf_documents/filehs/{mhs}--{thutuc}--{convert_name_unix(nguoidangky)}{fid}"
    return unidecode(new_hsbs_td_path)


def filter_datetime_strings(string_list):
    datetime_formats = ["%d/%m/%Y %H:%M:%S", "%d/%m/%Y %I:%M:%S %p"]
    found_match = False
    formatted_date = None
    for string in string_list:
        for date_format in datetime_formats:
            try:
                formatted_date = datetime.strptime(string, date_format).strftime(
                    "%d/%m/%Y %H:%M:%S"
                )
                found_match = True  # Đánh dấu đã tìm thấy chuỗi phù hợp
                break  # Dừng kiểm tra định dạng này
            except ValueError:
                continue
    if not found_match:
        raise ValueError(
            "Không có chuỗi nào phù hợp với các định dạng ngày giờ đã định nghĩa."
        )
    return formatted_date


def convert_name_unix(string):
    return string.lower().replace(" ", "_")


def isin_df_thamdinh(df_all, df_thamdinh):
    return df_all["ma ho so"].isin(df_thamdinh["ma ho so"].tolist())


def upload_desktop_file(string):
    time.sleep(1)
    pyautogui.press(["g", "d"])
    pyautogui.press("f")
    pyperclip.copy(string)
    time.sleep(0.1)
    pyautogui.hotkey("ctrl", "shift", "v")
    pyautogui.press("enter")
    pyautogui.hotkey("ctrl", "a")
    pyautogui.press("enter")


def upload_file_dialog(string, Pcloud=None):
    time.sleep(1)
    if Pcloud:
        pyautogui.press(["g", "p"])
    else:
        pyautogui.press(["g", "k"])
    pyautogui.press("f")
    pyperclip.copy(string)
    time.sleep(0.1)
    pyautogui.hotkey("ctrl", "shift", "v")
    pyautogui.press("enter")
    pyautogui.hotkey("ctrl", "a")
    pyautogui.press("enter")


def write_text(text, file_path):
    with open(file_path, "w") as file:
        file.write(text)


def isin_all_hs_dict(all_hs_dict, df):
    return df["ma ho so"].isin(all_hs_dict)


def is_chua_nhan(df):
    return df["da nhan"] != "1"


def is_da_di(df):
    return df["da di"] == "1"


def is_khong_dat(df):
    return df["van de hs"].str.startswith("0-")


def ends_with_dot_zero(x):
    if isinstance(x, (int, float, str)):
        return str(x).endswith(".0")
    return False


def export_name_csv(df, col=None):
    if col:
        df.to_csv("/home/<USER>/Dropbox/hnd/csv_source/name.csv", index_label=col)
    else:
        df.to_csv("/home/<USER>/Dropbox/hnd/csv_source/name.csv", index=False)


def kiem_tra_ngoai_gio_hanh_chinh():
    gio_hien_tai = datetime.now()
    # Kiểm tra xem có phải ngày 1/1 không
    if gio_hien_tai.day == 1 and gio_hien_tai.month == 1:
        return True

    # Kiểm tra xem có phải là thứ 7 hoặc chủ nhật không
    if gio_hien_tai.weekday() >= 5:
        return True

    # Kiểm tra xem có phải ngoài giờ hành chính không
    gio = gio_hien_tai.hour
    if 18 <= gio <= 23 or 0 <= gio < 7:
        return True

    return False  # nếu ngoài giờ hành chính thì trả về True


def print_pdf(file):
    # if kiem_tra_ngoai_gio_hanh_chinh():
    #     tele = TelegramSend("hnd")
    #     tele.send_file_warp(file)
    #
    # else:
    ds = [
        ("QUYẾT_ĐỊNH_Về_việc_cấp_Giấy_chứng_nhận_đ", 2),
        ("QUYẾT_ĐỊNH_Về_việc_cấp_chứng_chỉ", 1),
        ("cầu_khắc_phục", 1),
        ("Lịch", 1),
        ("thu_hồi", 1),
    ]

    for string, page_break in ds:
        if string in file:
            print_2_turn(file, page_break)
            break
    else:  # This else belongs to the for loop, not the if statement
        printer_method("HAIMAT", file)


def print_file_by_printer(file_name, printer_name):
    file_to_print = file_name

    # Kiểm tra xem file cần in có tồn tại không
    if os.path.exists(file_to_print):
        try:
            # Sử dụng lệnh lpr để in file
            subprocess.run(["lpr", "-P", printer_name, file_to_print], check=True)
            print(f"Đã gửi lệnh in file {file_to_print} đến máy in {printer_name}")
        except subprocess.CalledProcessError as e:
            print(f"Lỗi khi in file: {e}")
    else:
        print(f"Không tìm thấy file {file_to_print}")


def close_app(app_name):
    try:
        pid = subprocess.check_output(["pidof", app_name]).decode().strip()
        # Nếu có nhiều PID, chọn PID đầu tiên
        pid = pid.split(" ")[0]
        # Đóng tiến trình dùng PID
        os.system(f"kill -9 {pid}")
        print(f"Đã đóng ứng dụng {app_name}.")
    except subprocess.CalledProcessError:
        print(f"Không tìm thấy tiến trình {app_name} đang chạy.")


# Gọi hàm và in ra kết quả

# Tìm PID của tiến trình Zathura


# print_pdf('/home/<USER>/Dropbox/hnd/latexall/vb_cho_in/0-20240417101731-QUYẾT_ĐỊNH_Về_việc_cấp_chứng_chỉ_hành_ng.pdf')
# print_pdf('/home/<USER>/Dropbox/hnd/latexall/vb_cho_in/0-20240502154118-QUYẾT_ĐỊNH_Về_việc_cấp_chứng_chỉ_hành_ng.pdf')


def find_latest_pdf(directory):
    # Đảm bảo đường dẫn là một thư mục
    if not os.path.isdir(directory):
        raise ValueError("Đường dẫn không hợp lệ hoặc không phải là một thư mục.")

    # Tìm tất cả các file có định dạng .pdf trong thư mục
    pdf_files = [
        os.path.join(directory, file)
        for file in os.listdir(directory)
        if file.endswith(".pdf")
    ]

    # Sắp xếp các file PDF theo thời gian sửa đổi cuối cùng ( từ mới nhất đến cũ nhất )
    latest_pdf = sorted(pdf_files, key=os.path.getmtime, reverse=True)

    # Trả về file PDF mới nhất hoặc None nếu không có file PDF nào
    return latest_pdf[0] if latest_pdf else None


def get_lastest_file(path, extension):
    list_of_files = glob.glob(
        f"{path}/*{extension}"
    )  # Lấy tất cả các file với định dạng mong muốn
    # Loại bỏ các file tạm thời bắt đầu bằng '~'
    list_of_files = [
        f for f in list_of_files if not os.path.basename(f).startswith("~")
    ]
    if list_of_files:  # Kiểm tra xem có file nào không
        latest_file = max(list_of_files, key=os.path.getctime)
        pyperclip.copy(latest_file.strip(".docx"))
        return latest_file
    else:
        return None  # Trả về None nếu không có file nào


def generate_iso_time():
    now = datetime.now()
    # Định dạng chuỗi theo ISO 8601: YYYY-MM-DDTHH:MM:SS
    return now.strftime("%Y%m%d_%H%M%S")


def replace_hanh_chinh(df_dsqd, col):
    return (
        df_dsqd[col]
        .str.replace("huyện", "")
        .str.replace("tỉnh", "")
        .str.replace("thành phố", "")
    )


def compile_latex(text, ten_file_pdf, mhs, okular=True):
    os.chdir("/home/<USER>/Dropbox/hnd/latexall/mylatex")
    f = open("mylatex.tex", "w", encoding="utf-8")
    f.write(text)
    f.close()
    try:
        subprocess.run(["pdflatex", "mylatex.tex"])
    except Exception as e:
        print(e)
        pass
    shutil.copy(
        "mylatex.pdf",
        f"/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pdf_documents/kq/{mhs}-{ten_file_pdf}.pdf",
    )
    if okular:
        subprocess.Popen(
            [
                "okular",
                f"/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pdf_documents/kq/{mhs}-{ten_file_pdf}.pdf",
            ]
        )
    else:
        subprocess.Popen(
            [
                "zathura",
                f"/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pdf_documents/kq/{mhs}-{ten_file_pdf}.pdf",
            ]
        )


def wait_for_file_word(timer):
    path = "/home/<USER>/Desktop"
    # Xóa tất cả file .docx trong thư mục Desktop
    docx_files = glob.glob(path + "/*.docx")
    for file in docx_files:
        os.remove(file)
    start_time = time.time()
    while len(glob.glob(path + "/*.docx")) == 0:
        if time.time() - start_time > timer:  # Nếu đã trôi qua 30 giây
            print(f"Timeout after {timer} seconds.")
        time.sleep(0.5)


# Sử dụng hàm


from playwright.sync_api import sync_playwright
import time
import pyperclip
import pyautogui


def auto_day_vb(file_vb_word, trichyeu, loai_vb):
    dayvb = AutoDayVb(file_vb_word, trichyeu, loai_vb)
    dayvb.run()


def auto_day_vb_phucdap(file_vb_word, trichyeu, input_text, loai_vb):
    dayvb = AutoDayVb(file_vb_word, trichyeu, loai_vb, input_text)
    dayvb.run()

    # Event loop để xử lý các sự kiện


def get_max_socchnd(df):
    # Lấy 50 giá trị cuối cùng của DataFrame
    df = df.tail(50)
    df = df[df["so cchnd"].notnull()]
    df["so cchnd"] = df["so cchnd"].astype(int)
    mask = df["trinh do cm"].str.contains("Đại học", na=False)
    df_dh = df[mask]
    df_tc = df[~mask]
    maxdh = df_dh["so cchnd"].max()
    maxtc = df_tc["so cchnd"].max()
    return maxdh, maxtc


def auto_text_to_ioffice_pd(tieude, so_vanban, loai_vb, text=None):
    os.chdir("/home/<USER>/Dropbox/hnd/latexall/mylatex")
    ids = str(generate_iso_time())
    name = ids + "-" + tieude.replace(":", "-").replace("/", "-").replace(" ", "_")[:50]
    # word_file_count = len(glob.glob("/home/<USER>/Dropbox/*.docx"))
    # compile_latex(text, name, mhs)
    file_path = f"/home/<USER>/Dropbox/hnd/latexall/vb_cho_in/{str(so_vanban).replace('/', '-')}-{name}.tex"
    if text:
        f = open(file_path, "w", encoding="utf-8")
        f.write(text)
        f.close()
    else:
        shutil.copy("mylatex.tex", file_path)
    save_last_dict(
        [tieude, file_path, so_vanban, loai_vb],
        "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pickle_file/tieude.pk",
    )
    # Chạy script để mở file .tex mới nhất
    subprocess.run(
        [
            "tmux",
            "new-window",
            "-n",
            "latexedit",
            f"nvim '{file_path}'",
        ]
    )
    subprocess.run(["wmctrl", "-xa", "Terminator"])


def auto_text_to_ioffice(tieude, loai_vb, mhs, text=None):
    os.chdir("/home/<USER>/Dropbox/hnd/latexall/mylatex")
    ids = str(generate_iso_time())
    if "THÔNG BÁO KẾT QUẢ" in tieude:
        ids = "0-" + ids
        path = "/home/<USER>/Dropbox/hnd/csv_source/name.csv"
        df_name = pd.read_csv(path, dtype=str, index_col="ma ho so")
        df_name.at[mhs, "van de hs"] = ids
        df_name.to_csv(path, index_label="ma ho so")
    elif "THÔNG BÁO" in tieude:
        path = "/home/<USER>/Dropbox/hnd/csv_source/name.csv"
        df_name = pd.read_csv(path, dtype=str, index_col="ma ho so")
        df_name.at[mhs, "van de hs"] = ids
        df_name.to_csv(path, index_label="ma ho so")
    df_cchnd = pd.read_csv(
        "/home/<USER>/Dropbox/hnd/csv_source/cchnd.csv", dtype=str, index_col="ma ho so"
    )
    if mhs in df_cchnd.index:
        df_cchnd.at[mhs, "van de hs"] = ids
        df_cchnd.to_csv(
            "/home/<USER>/Dropbox/hnd/csv_source/cchnd.csv", index_label="ma ho so"
        )
    else:
        df_dkkd = pd.read_csv(
            "/home/<USER>/Dropbox/hnd/csv_source/dkkd.csv",
            dtype=str,
            index_col="ma ho so",
        )
        if mhs in df_dkkd.index:
            df_dkkd.at[mhs, "van de hs"] = ids
            df_dkkd.to_csv(
                "/home/<USER>/Dropbox/hnd/csv_source/dkkd.csv", index_label="ma ho so"
            )

    name = ids + "-" + tieude.replace(":", "-").replace("/", "-").replace(" ", "_")[:50]
    # word_file_count = len(glob.glob("/home/<USER>/Dropbox/*.docx"))
    # compile_latex(text, name, mhs)
    file_path = f"/home/<USER>/Dropbox/hnd/latexall/vb_cho_in/{name}.tex"
    if text:
        f = open(file_path, "w", encoding="utf-8")
        f.write(text)
        f.close()
    else:
        shutil.copy("mylatex.tex", file_path)

    save_last_dict(
        [tieude, file_path, loai_vb],
        "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pickle_file/tieude.pk",
    )
    # Chạy script để mở file .tex mới nhất
    subprocess.run(
        [
            "tmux",
            "new-window",
            "-n",
            "latexedit",
            f"nvim '{file_path}'",
        ]
    )
    subprocess.run(["wmctrl", "-xa", "Terminator"])


def auto_text_to_ioffice_sure(tieude, loai_vb, mhs, text=None):
    os.chdir("/home/<USER>/Dropbox/hnd/latexall/mylatex")
    ids = str(generate_iso_time())
    name = ids + "-" + tieude.replace(":", "-").replace("/", "-").replace(" ", "_")[:40]
    file_path = f"/home/<USER>/Dropbox/hnd/latexall/vb_cho_in/{mhs}-{name}.pdf"
    file_path_to_manal_copy = f"/home/<USER>/Desktop/{mhs}-{name}.pdf"
    if text:
        f = open("mylatex.tex", "w", encoding="utf-8")
        f.write(text)
        f.close()
    change_workspace("other")
    save_last_dict(
        [tieude, file_path.replace(".pdf", ".tex"), loai_vb],
        "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pickle_file/tieude.pk",
    )
    try:
        subprocess.run(["xelatex", "mylatex.tex"])
    except Exception as e:
        print(e)
        pass
    shutil.copy("mylatex.pdf", file_path)
    shutil.copy("mylatex.tex", file_path.replace(".pdf", ".tex"))
    shutil.copy("mylatex.pdf", file_path_to_manal_copy)
    open_with_foxit(file_path.replace("/home", "Z:"))
    wait_for_file_word(180)

    close_app("FoxitPDFEditor.")
    if "Yêu cầu khắc phục, sửa chữa" in tieude:
        tencs = unidecode.unidecode(
            tieude.split(":")[1]
            .replace("Nhà thuốc ", "")
            .replace("Quầy thuốc ", "")
            .lower()
        )
        file_scan = "/home/<USER>/Pcloud_ssd/Pcloud/scanned_pdf/bbtd" + tencs + ".pdf"
        file_to_up = "/home/<USER>/Desktop/bbtd" + tencs + ".pdf"
        shutil.copy(file_scan, file_to_up)
        bbtd_name = f"{mhs}-bbtd-{tencs}-{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        bbtd_path_to_save = f"/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pdf_documents/bbtd/gpp/{bbtd_name}"
        shutil.copy(file_to_up, bbtd_path_to_save)
        auto_day_vb(
            get_lastest_file("/home/<USER>/Desktop", ".docx") + "-@-" + file_to_up,
            tieude,
            loai_vb,
        )
    else:
        auto_day_vb(get_lastest_file("/home/<USER>/Desktop", ".docx"), tieude, loai_vb)
    print_pdf(file_path)
    return ids


def insert_stt(df):
    stt = [i + 1 for i in range(len(df))]
    df.insert(0, "stt", stt)


def get_day_before(num_day, day_now=None):
    if day_now is None:
        now = datetime.now()
    else:
        now = day_now
    day_before = now - timedelta(days=num_day)

    # Kiểm tra nếu hôm nay là thứ 7 hoặc chủ nhật
    if day_before.weekday() == 5:  # Thứ 7
        day_before -= timedelta(days=1)
    elif day_before.weekday() == 6:  # Chủ nhật
        day_before -= timedelta(days=2)

    ngay = str(day_before.day).zfill(2)
    if day_before.month < 3:
        thang = str(day_before.month).zfill(2)
    else:
        thang = str(day_before.month)
    nam = str(day_before.year)

    return ngay, thang, nam, f"{ngay}/{thang}/{nam}"


def get_current_date():
    now = datetime.now()

    # Kiểm tra nếu hôm nay là thứ 7 hoặc chủ nhật
    if now.weekday() == 5:  # Thứ 7
        now -= timedelta(days=1)
    elif now.weekday() == 6:  # Chủ nhật
        now -= timedelta(days=2)
    # else:
    #     now -= timedelta(days=1)

    ngay = str(now.day).zfill(2)
    if now.month < 3:
        thang = str(now.month).zfill(2)
    else:
        thang = str(now.month)
    nam = str(now.year)

    return ngay, thang, nam, f"{ngay}/{thang}/{nam}"


def phone_format(n):
    if len(n) > 5:
        n = n.replace(".", "")
        n = f"{n[:3]}.{n[3:6]}.{n[-4:]}"
    return n


def update_df_name_cho_tra(dfname, values, ma_hs):
    path_name = "/home/<USER>/Dropbox/hnd/csv_source/name.csv"
    df_update = pd.DataFrame([values], index=[ma_hs])  # cập nhật dataframe df2
    dfname.update(df_update)
    dfname.loc[ma_hs, "da nhan"] = "1"
    dfname.loc[ma_hs, "CHỜ TRẢ"] = "1"
    dfname.to_csv(path_name, index_label="ma ho so")


def open_with_foxit(output_path):
    command = [
        "/usr/bin/wine",
        "/home/<USER>/.wine/drive_c/Program Files (x86)/Foxit Software/Foxit PDF Editor/FoxitPDFEditor.exe",
        output_path,
    ]
    subprocess.Popen(command)


def wait_for_window_with_class(window_class, timeout=60, check_interval=1):
    """
    Chờ cho đến khi cửa sổ với class cụ thể xuất hiện và active.

    :param window_class: Class của cửa sổ cần kiểm tra.
    :param timeout: Thời gian tối đa để chờ (tính bằng giây).
    :param check_interval: Khoảng thời gian giữa các lần kiểm tra (tính bằng giây).
    :return: True nếu cửa sổ xuất hiện và active, False nếu hết thời gian chờ.
    """
    end_time = time.time() + timeout

    while time.time() < end_time:
        try:
            # Sử dụng xdotool để kiểm tra cửa sổ với class cụ thể
            result = subprocess.run(
                ["xdotool", "search", "--class", window_class], stdout=subprocess.PIPE
            )
            if result.stdout:
                # Lấy window ID từ kết quả
                window_id = result.stdout.decode().strip()
                # Kiểm tra xem cửa sổ có đang active không
                active_result = subprocess.run(
                    ["xdotool", "getactivewindow"], stdout=subprocess.PIPE
                )
                active_window_id = active_result.stdout.decode().strip()

                if window_id == active_window_id:
                    print(f"Cửa sổ với class '{window_class}' đã xuất hiện và active.")
                    return True
        except subprocess.SubprocessError as e:
            print(f"Lỗi khi kiểm tra cửa sổ: {e}")

        time.sleep(check_interval)

    print(
        f"Hết thời gian chờ, cửa sổ với class '{window_class}' không xuất hiện hoặc không active."
    )
    return False


def sendkey_xpath(driver, xpath, string):
    driver.find_element(By.XPATH, xpath).send_keys(string)


def minimize(clas):
    # Tìm window ID của cửa sổ có class là Terminator
    window_ids = subprocess.getoutput(f"xdotool search --class {clas}").split()

    # Kiểm tra nếu window ID hợp lệ và thu nhỏ cửa sổ
    for window_id in window_ids:
        try:
            int(window_id)  # Kiểm tra xem window_id có phải là số không
            subprocess.run(["xdotool", "windowminimize", window_id])
        except ValueError:
            # Bỏ qua ID không hợp lệ
            continue


def extract_ten_cs_regex(text):
    pattern = regex.compile(
        r"(Quầy|Nhà) thuốc \p{Lu}[^\s]*(?:\s\p{Lu}[^\s]*)*", regex.UNICODE
    )
    matches = set(match.group(0) for match in pattern.finditer(text))
    return " ".join(matches)


def close_pyqt_window(title):
    subprocess.call(["wmctrl", "-F", "-c", title])


def convert_unix_style(string):
    return unidecode.unidecode(string).lower().replace(" ", "_")


def move_files_to_top_folder(top_dir):
    for root, dirs, files in os.walk(top_dir):
        # Kiểm tra nếu đây không phải là thư mục sâu nhất (có thư mục con)
        if dirs:
            continue

        # Chỉ khi không có thư mục con nào, thì chúng ta đang ở thư mục sâu nhất
        # Vừa kiểm tra và chuyển tất cả các file PDF
        for file in files:
            shutil.move(os.path.join(root, file), top_dir)


def check_graduation_year(values, year):
    # Lấy 4 ký tự cuối cùng từ 'ngay tot nghiep', chuyển chúng thành số nguyên
    graduation_year = int(values["ngay tot nghiep"][-4:])

    # Lấy năm hiện tại
    current_year = datetime.now().year

    # So sánh năm tốt nghiệp với năm hiện tại trừ đi 3
    return graduation_year < (current_year - year)


def wait_for_download_to_complete(download_folder, file_extension=".zip", timeout=300):
    """
    Wait until a file with the specified extension is fully downloaded in the given folder.

    :param download_folder: The folder where files are being downloaded.
    :param file_extension: The file extension to look for, defaults to ".zip".
    :param timeout: Maximum time to wait in seconds, defaults to 300 seconds.
    """
    start_time = time.time()
    while time.time() - start_time < timeout:
        files = glob.glob(os.path.join(download_folder, f"*{file_extension}"))
        part_files = glob.glob(os.path.join(download_folder, "*.part"))
        if files and not part_files:
            return True
        time.sleep(1)
    raise TimeoutError("Download did not complete within the specified timeout.")


def run_python_313(name):
    subprocess.run(
        [
            "/home/<USER>/python313/bin/python",
            f"/home/<USER>/Dropbox/hnd/python_script/{name}.py",
        ],
        check=True,
    )


def update_multi_with_vlookup(df_target, df_source, key_column, value_columns):
    """
    df_target: DataFrame cần được cập nhật.
    df_source: DataFrame nguồn chứa dữ liệu cần cập nhật.
    key_column: tên cột dùng để tra cứu (vlookup).
    value_columns: list các tên cột trong df_source mà từ đó bạn muốn "vlookup" giá trị.
    """

    # Áp dụng bản đồ để cập nhật df_target bằng phương thức .map() cho từng cột giá trị
    for col in value_columns:
        for key, value in get_dict_from_2col(df_source, key_column, col).items():
            df_target.loc[df_target[key_column] == key, col] = value
    return df_target


def convert_and_sort_dates(df, column_name):
    df[column_name] = df[column_name].apply(format_date_trying)
    df["is_date"] = df["han"].apply(lambda x: isinstance(x, pd.Timestamp))
    # Sắp xếp DataFrame: những hàng không phải ngày giờ lên trên cùng, sau đó sắp xếp theo 'han'
    df = df.sort_values(by=["is_date", "han"], ascending=[True, True])
    # Bỏ cột tạm thời
    df = df.drop("is_date", axis=1)
    df["han"] = df["han"].apply(
        lambda x: x.strftime("%d/%m/%Y %H:%M:%S") if isinstance(x, pd.Timestamp) else x
    )
    return df


def get_dict_from_2col(df, key_column, value_columns):
    return df.set_index(key_column)[value_columns].to_dict()


def df_2_col_to_dict(column_key, column_value, df):
    return pd.Series(df[column_value].values, index=df[column_key]).to_dict()


def drop_last_50_rows(df):
    return df.iloc[:-50]


def add_tail_to_df(df, df_tail):
    return pd.concat([df, df_tail], ignore_index=False)


def append_keep_original_and_index(df_dshn, df_from_dsqd):
    df_tail = df_dshn.tail(50)
    if df_from_dsqd.index.isin(df_tail.index).all():
        df_tail = df_tail.update(df_from_dsqd)
    else:
        df_tail = pd.concat([df_tail, df_from_dsqd], ignore_index=False)
    df_dshn = drop_last_50_rows(df_dshn)
    df_dshn = add_tail_to_df(df_dshn, df_tail)
    return df_dshn


def update_multil_col_with_vlookup(
    df_target, df_source, key_column, value_columns, new_column_names=None
):
    """
    df_target: DataFrame cần được cập nhật.
    df_source: DataFrame nguồn chứa dữ liệu cần cập nhật.
    key_column: tên cột dùng để tra cứu (vlookup).
    value_columns: danh sách các cột trong df_source mà từ đó bạn muốn "vlookup" giá trị.
    new_column_names: danh sách tên cột mới trong df_target mà giá trị từ df_source sẽ được cập nhật vào.
                      Nếu None, cập nhật trực tiếp vào các cột trong value_columns.
    """
    # Nếu value_columns chỉ là một chuỗi, chuyển nó thành danh sách để xử lý chung
    if isinstance(value_columns, str):
        value_columns = [value_columns]

    # Nếu new_column_names không được cung cấp, dùng value_columns làm tên cột mới
    if new_column_names is None:
        new_column_names = value_columns

    # Tạo một bản đồ cho từng cột trong value_columns
    for value_column, new_column_name in zip(value_columns, new_column_names):
        lookup_dict = df_source.set_index(key_column)[value_column].to_dict()
        df_target[new_column_name] = df_target[key_column].map(lookup_dict)

    return df_target


def update_with_vlookup(
    df_target, df_source, key_column, value_column, new_column_name=None
):
    """
    df_target: DataFrame cần được cập nhật.
    df_source: DataFrame nguồn chứa dữ liệu cần cập nhật.
    key_column: tên cột dùng để tra cứu (vlookup).
    value_column: tên cột trong df_source mà từ đó bạn muốn "vlookup" giá trị.
    new_column_name: tên cột mới trong df_target mà giá trị từ df_source sẽ được cập nhật vào. Nếu None, cập nhật trực tiếp vào value_column.
    """
    # Tạo một cột mới trong df_target nếu cần
    if new_column_name is None:
        new_column_name = value_column

    # Tạo một bản đồ từ df_source để tra cứu nhanh
    lookup_dict = df_source.set_index(key_column)[value_column].to_dict()
    for key, value in lookup_dict.items():
        df_target.loc[df_target[key_column] == key, new_column_name] = value
    return df_target


def get_so_dkkd(df):
    df_tail = df.tail(100)
    list_so_dkkd = df_tail["so dkkd"].tolist()
    so_dh = list(filter(lambda x: x.startswith("00"), list_so_dkkd))
    so_tc_cd = [item for item in list_so_dkkd if item not in so_dh]
    maxnt = int(max(so_dh))
    maxqt = int(max(so_tc_cd))

    return maxnt, maxqt


def extract_to_folder(file, folder):
    with zipfile.ZipFile(file, "r") as zip_ref:
        zip_ref.extractall(folder)
    os.remove(file)


def trim_text(text):
    return " ".join(text.strip().split())


def compile_latex_gdp(text, ten_file_pdf):
    os.chdir("/home/<USER>/Dropbox/hnd/latexall/mylatex")
    f = open("mylatex.tex", "w", encoding="utf-8")
    f.write(text)
    f.close()
    try:
        subprocess.run(["pdflatex", "mylatex.tex"])
    except Exception as e:
        print(e)
        pass
    shutil.copy(
        "/home/<USER>/Dropbox/hnd/latexall/mylatex.pdf",
        f"/home/<USER>/Dropbox/hnd/latexall/documents/LÊ DIỄM/HỒ SƠ CÔNG TY/{ten_file_pdf}.pdf",
    )
    subprocess.Popen(
        [
            "/usr/bin/zathura",
            f"/home/<USER>/Dropbox/hnd/latexall/documents/LÊ DIỄM/HỒ SƠ CÔNG TY/{ten_file_pdf}.pdf",
        ]
    )


def check_directory_exists(directory_path):
    # Sử dụng os.path.isdir để kiểm tra thư mục có tồn tại không
    if os.path.isdir(directory_path):
        print(f"Thư mục '{directory_path}' tồn tại.")
        return True
    else:
        print(f"Thư mục '{directory_path}' không tồn tại.")
        return False


def update_common_add_diff(dfa, dfb):
    # Lấy các index của dfb đã tồn tại trong df_tail
    common_indices = dfb.index.intersection(dfa.index)
    # Lấy các index của dfb không tồn tại trong df_tail
    new_indices = dfb.index.difference(dfa.index)
    # Cập nhật các hàng có index chung
    dfa = dfa.copy()  # Tạo copy để tránh SettingWithCopyWarning
    for col in dfb.columns:
        if col in dfa.columns:
            dfa.loc[common_indices, col] = dfb.loc[common_indices, col]
    # Thêm các hàng mới từ dfb vào df_tail
    dfa = pd.concat([dfa, dfb.loc[new_indices]], axis=0)
    return dfa


def format_name(name: str):
    return unidecode.unidecode(name.lower()).replace(" ", "_").replace("-", "_")


def update_df_name_da_nhan(dfname, values, ma_hs):
    path_name = "/home/<USER>/Dropbox/hnd/csv_source/name.csv"
    df_update = pd.DataFrame([values], index=[ma_hs])  # cập nhật dataframe df2
    dfname.update(df_update)
    dfname.at[ma_hs, "da nhan"] = "1"
    if "KHO 1" in values.keys():
        dfname.at[ma_hs, "dia chi co so"] = values["KHO 1"]
    dfname.to_csv(path_name, index_label="ma ho so")


# Test hàm


def pd_to_date(df, col):
    df[col] = pd.to_datetime(df[col], format="%d/%m/%Y", dayfirst=True, exact=True)


def lower_first_char(string):
    if string.startswith("TDP"):
        return string
    else:
        return string[0].lower() + string[1:]


def lower_first_char_pd(string):
    if pd.isna(string):
        return string
    if str(string).startswith("TDP"):
        return string
    else:
        return str(string)[0].lower() + str(string)[1:]


def captalize_first_char(string):
    if string.startswith("TDP"):
        return string
    else:
        return string[0].upper() + string[1:]


def creat_latex(text, latex_file):
    f = open(latex_file, "w", encoding="utf-8")
    f.write(text)
    f.close()


def get_and_set_index(file_name, index_col):
    df = pd.read_csv(file_name, dtype="str")
    df.set_index(index_col, inplace=True, drop=True)
    df.fillna("", inplace=True)
    return df


def custom_assert(conditions):
    """
    Kiểm tra danh sách các điều kiện và hiện thông báo lỗi dạng popup nếu có điều kiện nào là False.

    :param conditions: Danh sách các tuple, mỗi tuple chứa một điều kiện và một thông báo lỗi
    """
    for condition, message in conditions:
        if not condition:
            # Tạo cửa sổ chính (master window) nhưng ẩn nó đi vì chúng ta chỉ cần messagebox
            root = tk.Tk()
            root.withdraw()

            # Hiện thông báo lỗi và kết thúc chương trình
            messagebox.showerror("Assertion Error", message)
            raise AssertionError(message)


def yes_no(window_label, question):
    # Tạo một cửa sổ Tkinter đơn giản
    root = tk.Tk()
    root.withdraw()  # Ẩn cửa sổ chính

    # Tạo hộp thoại Yes/No
    messagebox_root = tk.Toplevel(root)
    messagebox_root.withdraw()
    messagebox_root.attributes("-topmost", True)
    answer = messagebox.askquestion(window_label, question, parent=messagebox_root)

    messagebox_root.destroy()  # Đóng cửa sổ messagebox
    root.destroy()  # Đóng cửa sổ chính

    # Trả về True nếu người dùng nhấn "Yes", ngược lại trả về False
    return answer == "yes"


def fzf_get_dict_from_list(list_id_column):
    if len(list_id_column) == 0:
        show_message("error", "list rong")
        return None

    with tempfile.NamedTemporaryFile(mode="w+", delete=False) as temp_output:
        output_file = temp_output.name

    temp_file = "/tmp/id_list.txt"
    with open(temp_file, "w") as f:
        f.write("\n".join(list_id_column))

    print(f"Đã lưu danh sách ID vào {temp_file}")
    print("Chọn một mục trong FZF và nhấn Enter để tiếp tục...")

    # Sử dụng subprocess.run thay vì os.system
    os.system("wmctrl -xa Terminator")
    result = subprocess.run(
        [
            "tmux",
            "new-window",
            "-n",
            "fzf_get_dict_from_list",
            f"cat {temp_file} | fzf > {output_file} && touch {output_file}.done",
        ],
        shell=False,
    )

    # Kiểm tra nếu lệnh thất bại
    if result.returncode != 0:
        print(f"Lệnh tmux thất bại với return code: {result.returncode}")
        exit(1)  # Dừng script

    # Đợi cho đến khi file .done được tạo
    start_time = time.time()
    while not os.path.exists(f"{output_file}.done"):
        if time.time() - start_time > 15:
            print("Đã quá 15 giây, hủy script")
            sys.exit(1)
        time.sleep(0.1)

    # Xóa file .done sau khi hoàn thành
    os.remove(f"{output_file}.done")

    # Đọc kết quả từ file
    with open(output_file, "r") as f:
        selected_value = f.read().strip()

    # Xóa file tạm
    os.unlink(output_file)

    print(f"Giá trị đã chọn: {selected_value}")

    return selected_value


def fzf_get_dict_from_column(df, column=None):
    if not column:
        list_id_column = df.index.tolist()
    else:
        list_id_column = df[column].tolist()

    if len(list_id_column) == 0:
        show_message("error", "list rong")
        return None

    with tempfile.NamedTemporaryFile(mode="w+", delete=False) as temp_output:
        output_file = temp_output.name

    temp_file = "/tmp/id_list.txt"
    with open(temp_file, "w") as f:
        f.write("\n".join(list_id_column))

    print(f"Đã lưu danh sách ID vào {temp_file}")
    print("Chọn một mục trong FZF và nhấn Enter để tiếp tục...")

    # Sử dụng subprocess.run thay vì os.system
    os.system("wmctrl -xa Terminator")
    result = subprocess.run(
        [
            "tmux",
            "new-window",
            "-n",
            "ve_so_do",
            f"cat {temp_file} | fzf > {output_file} && touch {output_file}.done",
        ],
        shell=False,
    )

    # Kiểm tra nếu lệnh thất bại
    if result.returncode != 0:
        print(f"Lệnh tmux thất bại với return code: {result.returncode}")
        exit(1)  # Dừng script

    # Đợi cho đến khi file .done được tạo
    start_time = time.time()
    while not os.path.exists(f"{output_file}.done"):
        if time.time() - start_time > 15:
            print("Đã quá 15 giây, hủy script")
            sys.exit(1)
        time.sleep(0.1)

    # Xóa file .done sau khi hoàn thành
    os.remove(f"{output_file}.done")

    # Đọc kết quả từ file
    with open(output_file, "r") as f:
        selected_value = f.read().strip()

    # Xóa file tạm
    os.unlink(output_file)

    print(f"Giá trị đã chọn: {selected_value}")

    # Tìm hàng tương ứng trong DataFrame
    if not column:
        selected_row = df[df.index == selected_value]

    else:
        selected_row = df[df[column] == selected_value]
    if selected_row.empty:
        return None

    row_dict = selected_row.iloc[0].to_dict()
    if not column:
        row_dict["id_column"] = selected_value
    return row_dict


if __name__ == "__main__":
    tele = TelegramSend("hnd")
    tele.send_file_normal(
        "/home/<USER>/Pcloud_ssd/Pcloud/signed-signed-20250525_235350-quyet_dinh_ve_viec_cap_chung_chi_hanh_nghe_duoc_ng_17_21_00.pdf"
    )
