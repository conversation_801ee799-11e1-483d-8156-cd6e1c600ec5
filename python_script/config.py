class TelegramConfig:
    def __init__(self):
        self.config = {
            'hnd': {
                'token': '2143046655:AAE5iwz9KY8ofLZ_Vm3xhBrjpEyILDYzRy8',
                'chat_id': '-1001512252982'
            },
            'ql': {
                'token': '7488802978:AAEVqjw8XbYRummVLPYZfIW-sK-pnZkwnx8', 
                'chat_id': '-1001587612405'
            }
        }
    
    def get_token(self, channel):
        return self.config[channel]['token']
        
    def get_chat_id(self, channel):
        return self.config[channel]['chat_id'] 