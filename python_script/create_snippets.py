import subprocess
import sys
from typing import Dict
import os
import time
import psutil
import json


def send_notification(message):
    notify_text = "@@@@@@@@@@@@@@@@@@@@@@@@@@@@\n\n" + message
    notify_command = ["notify-send", notify_text]
    subprocess.run(notify_command)


# Biến toàn cục để theo dõi trạng thái terminal
_terminal_was_running = False
_terminal_exit_time = 0
_TERMINAL_GRACEFUL_EXIT_TIME = 3.0  # Độ lệch thời gian cho phép (giây)
_KILL_MARKER_FILE = os.path.expanduser("~/awesome_terminal_kill.json")
_TIME_THRESHOLD = 3.0  # Đ<PERSON> lệch thời gian cho phép (giây)


def create_snippet_from_fields(fields: Dict[str, str], trigger: str = "jj") -> str:
    """
    Tạo một snippet LuaSnippets từ dictionary các trường.

    Args:
        fields: Dictionary chứa các trường và giá trị mặc định
        trigger: Trigger key cho snippet (mặc định là "jj")

    Returns:
        Chuỗi LuaSnippets được định dạng
    """
    # Tạo danh sách các insert nodes với giá trị mặc định
    insert_nodes = []
    for i, (field, value) in enumerate(fields.items()):
        if value and value != "":
            insert_nodes.append(f'i({i + 1}, "{value}")')
        else:
            insert_nodes.append(f"i({i + 1})")

    # Tạo format string cho các trường
    field_lines = []
    for i, (field, _) in enumerate(fields.items()):
        field_lines.append(f'"{field}" = "<>"')

    # Tạo snippet string
    snippet = f'''  s(
    {{ trig = "{trigger}", snippetType = "autosnippet" }},
    fmta(
      [[{chr(10).join(field_lines)}]],
      {{ {", ".join(insert_nodes)} }}
    )
  ),'''

    return snippet


def find_first_snippet(content: str) -> tuple[int, int]:
    """
    Tìm vị trí bắt đầu và kết thúc của snippet đầu tiên trong nội dung.

    Args:
        content: Nội dung file toml.lua

    Returns:
        Tuple chứa vị trí bắt đầu và kết thúc của snippet đầu tiên
    """
    lines = content.split("\n")
    start_line = -1
    end_line = -1

    # Tìm dòng bắt đầu của snippet đầu tiên
    for i, line in enumerate(lines):
        if line.strip().startswith("s("):
            start_line = i
            break

    if start_line == -1:
        raise ValueError("Không tìm thấy snippet nào trong file")

    # Tìm dòng kết thúc của snippet đầu tiên
    bracket_count = 0
    for i in range(start_line, len(lines)):
        line = lines[i]
        bracket_count += line.count("{")
        bracket_count -= line.count("}")

        if bracket_count == 0 and line.strip().endswith("),"):
            end_line = i
            break

    if end_line == -1:
        raise ValueError("Không tìm thấy kết thúc của snippet đầu tiên")

    return start_line, end_line


def creat_snippet(fields: Dict[str, str], trigger: str = "jj") -> None:
    """
    Cập nhật file toml.lua với snippet mới.

    Args:
        fields: Dictionary chứa các trường và giá trị mặc định
        trigger: Trigger key cho snippet
    """
    # Đường dẫn đến file toml.lua
    toml_path = os.path.expanduser(
        "~/Dropbox/hnd/dotfiles/config/nvim/luasnippets/toml.lua"
    )

    # Đọc nội dung file hiện tại
    with open(toml_path, "r") as f:
        content = f.read()

    # Tìm vị trí của snippet đầu tiên
    start_line, end_line = find_first_snippet(content)
    lines = content.split("\n")

    # Tạo snippet mới
    new_snippet = create_snippet_from_fields(fields, trigger)

    # Thay thế nội dung
    new_lines = lines[:start_line] + [new_snippet] + lines[end_line + 1 :]
    new_content = "\n".join(new_lines)

    # Ghi lại file
    with open(toml_path, "w") as f:
        f.write(new_content)


def check_awesome_kill_marker() -> bool:
    """
    Kiểm tra xem terminal có bị kill bởi AwesomeWM hay không bằng cách so sánh thời gian.

    Returns:
        bool: True nếu terminal bị kill bởi AwesomeWM, False nếu không
    """
    if not os.path.exists(_KILL_MARKER_FILE):
        return False

    try:
        with open(_KILL_MARKER_FILE, "r") as f:
            data = json.load(f)
            kill_time = data.get("kill_time", 0)
            current_time = time.time()

            # Nếu thời gian kill gần đây (trong vòng 5 giây)
            if abs(current_time - kill_time) < _TERMINAL_GRACEFUL_EXIT_TIME:
                return True
    except (json.JSONDecodeError, IOError):
        pass

    return False


def is_xfce4_terminal_running() -> bool:
    """
    Kiểm tra xem xfce4-terminal có đang chạy hay không và phát hiện cách nó thoát.
    Phân biệt giữa:
    - Thoát bình thường (Mod+Q trong AwesomeWM)
    - Kill đột ngột (kill -9 hoặc crash)

    Returns:
        bool: True nếu xfce4-terminal đang chạy, False nếu không
    """
    is_running = False

    # Kiểm tra xem terminal có đang chạy không
    for proc in psutil.process_iter(["name"]):
        try:
            if "xfce4-terminal" in proc.info["name"].lower():
                is_running = True
                break
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            pass

    # Nếu terminal đang chạy
    if is_running:
        return True

    # Nếu terminal không chạy, kiểm tra file marker
    if os.path.exists(_KILL_MARKER_FILE):
        with open(_KILL_MARKER_FILE, "r") as f:
            data = json.load(f)
            kill_time = data.get("kill_time", 0)
            current_time = time.time()
            time_diff = abs(current_time - kill_time)

            # Nếu thời gian kill trong khoảng 5s
            if time_diff <= _TIME_THRESHOLD:
                return False
            else:
                return True
    else:
        send_notification("khong co file marker")
        sys.exit()

    # Nếu không tìm thấy file marker


def creat_and_get(default_fields: Dict[str, str]):
    """
    Tạo cửa sổ Neovim để nhập liệu và đợi người dùng thoát,
    sau đó đọc giá trị từ file TOML.
    """
    creat_snippet(default_fields)
    # Tạo đường dẫn đến file input snippet
    input_file = "/home/<USER>/Dropbox/hnd/latexall/mylatex/input_snippet.toml"
    if os.path.exists(input_file):
        os.remove(input_file)

    # Mở Neovim trong cửa sổ mới
    subprocess.run(
        [
            "xfce4-terminal",
            "--command",
            f"nvim {input_file}",
        ]
    )

    # Chờ cho đến khi file thay đổi (người dùng thoát Neovim)
    start_time = time.time()
    while time.time() - start_time < 12000:  # 12000 giây timeout
        if not is_xfce4_terminal_running():
            send_notification("xfce4-terminal đã bị kill do Mod+Q trong AwesomeWM")
            sys.exit()

        for proc in psutil.process_iter(["name"]):
            if "xfce4-terminal" in proc.info["name"].lower():
                break
        else:
            if os.path.exists(input_file):
                try:
                    # Đọc giá trị từ file TOML
                    values = read_toml_file(input_file)
                    print("Giá trị nhập vào:", values)
                    os.remove(input_file)
                    return values
                except ValueError as e:
                    print(f"Lỗi: {str(e)}")
                    return None
        time.sleep(0.1)

    print("Hết thời gian chờ")
    return None


def read_toml_file(file_path: str) -> Dict[str, str]:
    """
    Đọc và parse file TOML.

    Args:
        file_path: Đường dẫn đến file TOML

    Returns:
        Dictionary chứa các giá trị từ file TOML

    Raises:
        ValueError: Nếu file không tồn tại hoặc có định dạng không hợp lệ
    """
    if not os.path.exists(file_path):
        raise ValueError(f"File {file_path} không tồn tại")

    try:
        # Đọc file dưới dạng text trước để kiểm tra định dạng
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read().strip()

        # Kiểm tra nếu file rỗng
        if not content:
            return {}

        # Kiểm tra định dạng cơ bản
        lines = content.split("\n")
        result = {}
        for line in lines:
            line = line.strip()
            if not line or line.startswith("#"):
                continue

            if "=" not in line:
                raise ValueError(f"Dòng không hợp lệ: {line}")

            # Tách key và value
            key, value = line.split("=", 1)
            key = key.strip()
            value = value.strip()

            # Loại bỏ dấu ngoặc kép khỏi key nếu có
            if key.startswith('"') and key.endswith('"'):
                key = key[1:-1]
            elif key.startswith("'") and key.endswith("'"):
                key = key[1:-1]

            # Xử lý giá trị string
            if value.startswith('"') and value.endswith('"'):
                value = value[1:-1]
            elif value.startswith("'") and value.endswith("'"):
                value = value[1:-1]

            result[key] = value

        return result

    except Exception as e:
        raise ValueError(f"Lỗi khi đọc file TOML: {str(e)}")


if __name__ == "__main__":
    default_fields = {
        "Họ tên": "dfdff",
        "Tuổi": "25",
        "Email": "<EMAIL>",
        "Địa chỉ": "Vinh Phuk",
        "Số điện thoại": "",
    }
    creat_and_get(default_fields)
