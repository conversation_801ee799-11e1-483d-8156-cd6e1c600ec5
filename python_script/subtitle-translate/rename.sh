#!/bin/bash

# Đường dẫn đến thư mục chứa các file .srt
TARGET_DIR="/home/<USER>/Pcloud_ssd/Pcloud/Zalo_Download/Bash Mastery The Complete Guide to Bash Shell Scripting (hoctapgiare.top)"

# Tìm tất cả các file .srt trong thư mục và các thư mục con
find "$TARGET_DIR" -type f -name "*_en.srt" | while read -r file; do
    # Lấy tên file mới bằng cách loại bỏ _en
    new_file="${file%_en.srt}.srt"
    
    # Kiểm tra xem file mới đã tồn tại chưa
    if [ ! -f "$new_file" ]; then
        # Đổi tên file
        mv "$file" "$new_file"
        echo "Đã đổi tên: $file -> $new_file"
    else
        echo "File $new_file đã tồn tại, bỏ qua $file"
    fi
done

echo "Hoàn thành đổi tên file!" 