from pathlib import Path
from typing import List
import os


def expand_path(path: str) -> Path:
    """Convert string path to Path object, expanding ~ and environment variables."""
    return Path(os.path.expanduser(os.path.expandvars(path))).resolve()


def split_content(content: str, max_chars: int = 10000) -> List[str]:
    """Split content into smaller chunks while preserving subtitle blocks."""
    lines = content.splitlines()
    chunks = []
    current_chunk = []
    current_length = 0

    for line in lines:
        line_length = len(line) + 1  # +1 for newline

        if current_length + line_length > max_chars and line.strip() == "":
            chunks.append("\n".join(current_chunk))
            current_chunk = []
            current_length = 0

        current_chunk.append(line)
        current_length += line_length

    if current_chunk:
        chunks.append("\n".join(current_chunk))

    return chunks


def is_srt_file(path: Path) -> bool:
    """Check if file is a valid .srt file."""
    return path.suffix.lower() == ".srt" and path.is_file()

