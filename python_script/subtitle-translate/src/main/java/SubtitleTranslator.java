import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.hc.client5.http.classic.methods.HttpPost;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.CloseableHttpResponse;
import org.apache.hc.core5.http.ParseException;
import org.apache.hc.core5.http.io.entity.EntityUtils;
import org.apache.hc.core5.http.io.entity.StringEntity;

import java.io.*;
import java.nio.file.*;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Stream;

public class SubtitleTranslator {
    private static final Integer CHARACTER_PER_BATCH = 10000;
    private static final String API_KEY = System.getenv("GEMINI_API_KEY");
    private static final String GEMINI_API_URL = "https://generativelanguage.googleapis.com/v1/models/gemini-1.5-flash:generateContent?key=" + API_KEY;
    private static final long DELAY_BETWEEN_FILES = 5000; // 5 seconds delay between files
    private static final long DELAY_BETWEEN_BATCHES = 1000; // 1 second delay between batches

    public static void main(String[] args) {
        if (API_KEY == null || API_KEY.isEmpty()) {
            System.err.println("Error: GEMINI_API_KEY environment variable is not set");
            System.exit(1);
        }
        System.out.println("Enter the root directory path containing .srt files: ");
        String rootPath = new java.util.Scanner(System.in).nextLine();

        // Xử lý đường dẫn với ~
        if (rootPath.startsWith("~")) {
            String userHome = System.getProperty("user.home");
            rootPath = rootPath.replace("~", userHome);
        }

        try {
            Path directory = Paths.get(rootPath).toAbsolutePath().normalize();
            if (!Files.exists(directory)) {
                System.err.println("Directory does not exist: " + directory);
                return;
            }
            System.out.println("Processing directory: " + directory);
            processDirectory(directory);
        } catch (Exception e) {
            System.err.println("Error processing directory: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private static void processDirectory(Path directory) throws IOException {
        try (Stream<Path> paths = Files.walk(directory)) {
            paths.filter(path -> path.toString().toLowerCase().endsWith(".srt"))
                 .forEach(path -> {
                     try {
                         processFile(path);
                         // Delay between files to avoid API rate limits
                         Thread.sleep(DELAY_BETWEEN_FILES);
                     } catch (Exception e) {
                         System.err.println("Error processing file " + path + ": " + e.getMessage());
                     }
                 });
        }
    }

    private static void processFile(Path inputPath) throws IOException, ParseException, InterruptedException {
        System.out.println("Processing file: " + inputPath);
        
        // Create backup path with ".bak" extension
        Path backupPath = inputPath.resolveSibling(inputPath.getFileName().toString() + ".bak");
        Path tempPath = inputPath.resolveSibling(inputPath.getFileName().toString() + ".temp");
        
        // Create backup of original file if it doesn't exist
        if (!Files.exists(backupPath)) {
            Files.copy(inputPath, backupPath);
            System.out.println("Created backup at: " + backupPath);
        }

        try {
            // Read the entire SRT file content
            String srtContent = new String(Files.readAllBytes(inputPath));

            // Split the SRT content into parts
            List<String> parts = splitSRTContent(srtContent, CHARACTER_PER_BATCH);

            // Create a temporary file for translation
            if (Files.exists(tempPath)) {
                Files.delete(tempPath);
            }
            Files.createFile(tempPath);
            
            // Translate each part and save to temporary file
            try (BufferedWriter writer = Files.newBufferedWriter(tempPath)) {
                for (int i = 0; i < parts.size(); i++) {
                    String part = parts.get(i);
                    System.out.printf("Translating part %d of %d for file: %s%n", i + 1, parts.size(), inputPath.getFileName());
                    
                    String translatedPart = translateText(part);
                    if (translatedPart == null || translatedPart.isEmpty()) {
                        throw new IOException("Translation failed - empty response");
                    }
                    writer.write(translatedPart);
                    writer.write("\n");
                    writer.flush();

                    // Delay between batches to avoid API rate limits
                    if (i < parts.size() - 1) {
                        Thread.sleep(DELAY_BETWEEN_BATCHES);
                    }
                }
            }

            // Replace original file with translated content
            Files.move(tempPath, inputPath, StandardCopyOption.REPLACE_EXISTING);
            
            System.out.println("Completed translation of: " + inputPath);
            System.out.println("Original file backed up at: " + backupPath);
        } catch (Exception e) {
            // If something goes wrong, try to restore from backup
            System.err.println("Error during translation: " + e.getMessage());
            try {
                if (Files.exists(backupPath)) {
                    Files.copy(backupPath, inputPath, StandardCopyOption.REPLACE_EXISTING);
                    System.out.println("Restored original file from backup");
                }
            } catch (IOException restoreError) {
                System.err.println("Failed to restore from backup: " + restoreError.getMessage());
            }
            // Clean up temp file if it exists
            try {
                if (Files.exists(tempPath)) {
                    Files.delete(tempPath);
                }
            } catch (IOException tempError) {
                System.err.println("Failed to delete temp file: " + tempError.getMessage());
            }
            throw e;
        }
    }

    private static List<String> splitSRTContent(String srtContent, int charLimit) {
        String[] lines = srtContent.split("\n");
        List<String> parts = new ArrayList<>();
        StringBuilder partBuilder = new StringBuilder();
        int currentLength = 0;

        for (String line : lines) {
            if (currentLength + line.length() + 1 > charLimit && line.trim().isEmpty()) {
                parts.add(partBuilder.toString().trim());
                partBuilder.setLength(0);
                currentLength = 0;
            }
            partBuilder.append(line.replace("\"", "\\\"")).append("\n");
            currentLength += line.length() + 1;
        }

        if (partBuilder.length() > 0) {
            parts.add(partBuilder.toString().trim());
        }

        return parts;
    }

    private static String translateText(String text) throws IOException, ParseException, InterruptedException {
        int maxRetries = 3;
        int attempt = 0;
        while (attempt < maxRetries) {
            try (CloseableHttpClient client = org.apache.hc.client5.http.impl.classic.HttpClients.createDefault()) {
                HttpPost post = new HttpPost(GEMINI_API_URL);
                post.setHeader("Content-Type", "application/json");

                String prompt = "Translate the subtitles in this file into Vietnamese with the following requirements:\n" +
                        "\n" +
                        "Maintain the original format, including sequence numbers, timestamps, and the number of lines.\n" +
                        "The translations must match the context, culture, and situations occurring in the movie.\n" +
                        "Preserve the capitalization exactly as in the original text.\n" +
                        "Do not merge content from different timestamps into a single translation block.\n" +
                        "Return only the translated content in the specified format, without any additional explanations, introductions, or questions.\n" + text;

                String jsonRequest = String.format("""
                {
                   "contents": [
                     {
                       "role": "user",
                       "parts": [
                         {
                           "text": "%s"
                         }
                       ]
                     }
                   ],
                   "generationConfig": {
                     "temperature": 0.7,
                     "topK": 50,
                     "topP": 0.9,
                     "maxOutputTokens": 8192
                   }
                 }
                """, prompt.replace("\"", "\\\""));
                post.setEntity(new StringEntity(jsonRequest));

                try (CloseableHttpResponse response = client.execute(post)) {
                    String jsonResponse = EntityUtils.toString(response.getEntity());
                    ObjectMapper mapper = new ObjectMapper();
                    JsonNode rootNode = mapper.readTree(jsonResponse);
                    
                    if (response.getCode() != 200) {
                        System.err.println("API Error: " + jsonResponse);
                        throw new IOException("API returned error code: " + response.getCode());
                    }
                    
                    if (rootNode.path("candidates").isArray() && rootNode.path("candidates").size() > 0) {
                        JsonNode contentNode = rootNode.path("candidates").get(0).path("content");
                        if (contentNode.path("parts").isArray() && contentNode.path("parts").size() > 0) {
                            String translatedText = contentNode.path("parts").get(0).path("text").asText();
                            System.out.println("Successfully translated text");
                            return translatedText;
                        }
                    }
                    throw new IOException("Invalid API response format");
                }
            } catch (Exception e) {
                System.err.println("Attempt " + (attempt + 1) + " failed: " + e.getMessage());
                if (attempt < maxRetries - 1) {
                    Thread.sleep(2000); // Wait for 2 seconds before retrying
                }
            }
            attempt++;
        }
        throw new IOException("Failed to translate text after " + maxRetries + " attempts");
    }
}
