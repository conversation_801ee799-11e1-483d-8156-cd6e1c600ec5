from pathlib import Path
from typing import List
import sys
from time import sleep
from tqdm import tqdm

from translator import SubtitleTranslator
from utils import expand_path, split_content, is_srt_file


def find_srt_files(directory: Path) -> List[Path]:
    """Find all .srt files in directory and subdirectories."""
    return [p for p in directory.rglob("*.srt") if is_srt_file(p)]


def process_file(translator: SubtitleTranslator, file_path: Path) -> bool:
    """Process a single subtitle file."""
    backup_path = file_path.with_suffix(".srt.bak")
    temp_path = file_path.with_suffix(".srt.temp")

    # Skip if already processed
    if backup_path.exists():
        print(f"Skipping {file_path.name} (backup exists)")
        return False

    try:
        # Create backup
        file_path.rename(backup_path)
        print(f"Created backup: {backup_path.name}")

        # Read content
        content = backup_path.read_text(encoding="utf-8")
        chunks = split_content(content)

        # Translate chunks
        translated_parts = []
        for chunk in tqdm(chunks, desc=f"Translating {file_path.name}", unit="chunk"):
            translated_text = translator.translate_text(chunk)
            if translated_text is None:
                raise Exception("Translation failed")
            translated_parts.append(translated_text)
            sleep(1)  # Delay between chunks

        # Save translation
        temp_path.write_text("\n".join(translated_parts), encoding="utf-8")
        temp_path.rename(file_path)

        print(f"Successfully translated: {file_path.name}")
        return True

    except Exception as e:
        print(f"Error processing {file_path.name}: {str(e)}")
        # Restore original file
        if backup_path.exists():
            if file_path.exists():
                file_path.unlink()
            backup_path.rename(file_path)
            print(f"Restored original file: {file_path.name}")
        return False

    finally:
        # Cleanup temp file
        if temp_path.exists():
            temp_path.unlink()


def main():
    try:
        translator = SubtitleTranslator()
    except ValueError as e:
        print(f"Error: {str(e)}")
        sys.exit(1)

    # Get directory path
    dir_input = input("Enter subtitle directory: ").strip()
    directory = expand_path(dir_input)

    if not directory.exists():
        print(f"Directory not found: {directory}")
        sys.exit(1)

    # Find all .srt files
    srt_files = find_srt_files(directory)
    if not srt_files:
        print("No .srt files found")
        sys.exit(0)

    print(f"Found {len(srt_files)} .srt files")

    # Process each file
    success_count = 0
    for file_path in srt_files:
        if process_file(translator, file_path):
            success_count += 1
        sleep(5)  # Delay between files

    print(
        f"\nCompleted: {success_count}/{len(srt_files)} files translated successfully"
    )


if __name__ == "__main__":
    main()

