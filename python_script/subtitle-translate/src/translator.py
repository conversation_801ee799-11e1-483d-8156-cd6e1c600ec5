import os
import google.generativeai as genai
from typing import Optional


class SubtitleTranslator:
    def __init__(self):
        self.api_key = os.getenv("GEMINI_API_KEY")
        if not self.api_key:
            raise ValueError("GEMINI_API_KEY environment variable is not set")

        genai.configure(api_key=self.api_key)
        self.model = genai.GenerativeModel("gemini-pro")
        self.max_retries = 3

    def translate_text(self, text: str) -> Optional[str]:
        """Translate text using Gemini API with retry mechanism."""
        prompt = self._create_translation_prompt(text)

        for attempt in range(self.max_retries):
            try:
                response = self.model.generate_content(
                    prompt,
                    generation_config=genai.types.GenerationConfig(
                        temperature=0.7,
                        top_k=50,
                        top_p=0.9,
                        max_output_tokens=8192,
                    ),
                )

                if response.text:
                    return response.text

            except Exception as e:
                print(f"Attempt {attempt + 1} failed: {str(e)}")
                if attempt < self.max_retries - 1:
                    continue

        return None

    def _create_translation_prompt(self, text: str) -> str:
        """Create the translation prompt with specific requirements."""
        return f"""Translate the subtitles in this file into Vietnamese with the following requirements:

Maintain the original format, including sequence numbers, timestamps, and the number of lines.
The translations must match the context, culture, and situations occurring in the movie.
Preserve the capitalization exactly as in the original text.
Do not merge content from different timestamps into a single translation block.
Return only the translated content in the specified format, without any additional explanations, introductions, or questions.

{text}"""
