# Subtitle Translator

C<PERSON><PERSON> cụ dịch phụ đề từ tiếng <PERSON>h sang tiếng Việt sử dụng Gemini API.

## Yêu cầu
- Python 3.8+
- pip (Python package manager)

## Cài đặt

1. Clone repository:
```bash
git clone https://github.com/yourusername/subtitle_translator.git
cd subtitle_translator
```

2. Cài đặt dependencies:
```bash
pip install -r requirements.txt
```

3. Cấu hình API key:
Thêm Gemini API key vào biến môi trường:
```bash
export GEMINI_API_KEY="your_api_key_here"
```

## Sử dụng

1. Chạy chương trình:
```bash
python src/main.py
```

2. Nhập đường dẫn thư mục chứa file phụ đề cần dịch (hỗ trợ ký tự ~):
```
Enter subtitle directory: ~/path/to/subtitles
```

## Tính năng
- <PERSON>ự động quét tất cả file .srt trong thư mục và thư mục con
- Tự động backup file gốc với phần mở rộng .bak
- X<PERSON> lý lỗi và khôi phục từ backup nếu có vấn đề
- Hiển thị tiến trình dịch
- Tối ưu hóa số lượng request API