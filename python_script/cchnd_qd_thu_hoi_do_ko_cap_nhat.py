import os

import pandas as pd

from god_class import (
    TextProcess,
    convert_ngay,
    get_current_date,
    insert_stt,
)

os.chdir("/home/<USER>/Dropbox/hnd/csv_source")
values = {}


values["ngay"], values["thang"], values["nam"], values["ngayqd"] = get_current_date()

df_dsth_all = pd.read_csv("dsthuhoicchnd.csv", dtype=str, index_col="so cchnd")
df_dsth_all["trinh do cm"] = (
    df_dsth_all["trinh do cm"]
    .str.replace("cd", "Cao đẳng dược")
    .str.replace("CD", "Cao đẳng dược")
    .str.replace("dh", "Đại học dược")
    .str.replace("DH", "Đại học dược")
    .str.replace("tc", "Trung cấp dược")
    .str.replace("TC", "Trung cấp dược")
)

df_dsth_all["vi tri hanh nghe"] = (
    df_dsth_all["vi tri hanh nghe"]
    .str.replace("qt", "Quầy thuốc")
    .str.replace("nt", "Nhà thuốc")
)
df_dsth_all.drop_duplicates(inplace=True)


df_dsth = df_dsth_all[df_dsth_all["ngay qd"].isnull()]

df_dsth.drop(columns=["ngay qd"], inplace=True)
insert_stt(df_dsth)

df_dsth["ten nguoi ptcm"] = df_dsth["ten nguoi ptcm"].str.title()
df_dsth["so cchnd"] = df_dsth.index
df_dsth["so cchnd"] = df_dsth["so cchnd"].apply(
    lambda x: x + "/CCHND-SYT-VP" if x.isdigit() else x
)
df_dsth["ly do thu hoi"] = df_dsth["ly do thu hoi"].fillna(
    "Thu hồi do quá hạn cập nhật"
)
df_dsth["ngay cap cchnd"] = df_dsth["ngay cap cchnd"].apply(convert_ngay)

df_dsth = df_dsth[
    [
        "stt",
        "so cchnd",
        "ngay cap cchnd",
        "ten nguoi ptcm",
        "vi tri hanh nghe",
        "ly do thu hoi",
    ]
]
# Kiểm tra giá trị trùng lặp trong cột "so cchnd"
duplicates = df_dsth[df_dsth.index.duplicated(keep=False)]
if not duplicates.empty:
    from god_class import show_message

    show_message("thong bao", "CẢNH BÁO: Có các số CCHND bị trùng lặp:")
    show_message("thong bao", duplicates[["ten nguoi ptcm", "so cchnd"]])
    raise Exception("Phát hiện số CCHND bị trùng lặp, vui lòng kiểm tra lại dữ liệu")


mask_lai = df_dsth["ly do thu hoi"].str.contains("lại").any()
mask_dc = df_dsth["ly do thu hoi"].str.contains("chỉnh").any()
if mask_lai and mask_dc:
    values["cancu"] = (
        r"\textit{Căn cứ hồ sơ đề nghị cấp lại, điều chỉnh nội dung trên Chứng chỉ hành nghề dược của người hành nghề dược;}"
    )
elif mask_lai:
    values["cancu"] = (
        r"\textit{Căn cứ hồ sơ đề nghị cấp lại Chứng chỉ hành nghề dược của người hành nghề dược;}"
    )
elif mask_dc:
    values["cancu"] = (
        r"\textit{Căn cứ hồ sơ đề nghị điều chỉnh nội dung trên Chứng chỉ hành nghề dược của người hành nghề dược;}"
    )
else:
    values["cancu"] = ""


df_dsth["ly do thu hoi"] = (
    df_dsth["ly do thu hoi"]
    .str.replace(
        "Thu hồi do quá hạn cập nhật",
        "Người hành nghề tự nguyện thu hồi CCHND",
    )
    .str.replace(
        "Tự nguyện thu hồi",
        "Người hành nghề tự nguyện thu hồi CCHND",
    )
)
df_dsth.sort_values(by=["ly do thu hoi"], ascending=[False])
ds = df_dsth.astype(str).apply("&".join, axis=1)
values["s"] = ds.str.cat(sep=r"\\" + "\n") + r"\\"
values["so_cc"] = str(len(df_dsth)).zfill(2)


df_dsth_all["ngay qd"].fillna(values["ngayqd"], inplace=True)
df_dsth_all["ten nguoi ptcm"] = df_dsth_all["ten nguoi ptcm"].str.title()
df_dsth_all["ngay cap cchnd"] = df_dsth_all["ngay cap cchnd"].apply(convert_ngay)
df_dsth_all.to_csv("dsthuhoicchnd.csv", index_label="so cchnd")

title = f"QUYẾT ĐỊNH Về việc thu hồi Chứng chỉ hành nghề dược ngày {values['ngayqd']}"
qd = TextProcess("cchnd_qd_thu_hoi")
qd.format_text(values)
qd.auto_day_van_ban(title, "QĐ", 0)
