import os
import pandas as pd

from god_class import auto_text_to_ioffice_pd, get_current_date
from top_most_get_text import input_dialog

os.chdir("/home/<USER>/Dropbox/hnd/csv_source")
path = "gsp_cs_kcb.csv"
indexs = "SỐ GPHĐ FULL"
index_val = input_dialog("NHẬP", "NHẬP SỐ GPHĐ (FULL)", "")
df_traloi_xm = pd.read_csv(path, dtype=str, index_col=indexs)
default_values = {"NƠI CẤP GPHĐ": "Sở Y tế tỉnh Phú Thọ", "GIÃN DÒNG": "0"}
list_upper = []
multiline = "KHO BẢO QUẢN THUỐC"
list_ngay = ["NGÀY GPHĐ", "NGÀY TB"]
list_phone = ["SỐ ĐT"]
list_title = ["ten nguoi ptcm KỸ THUẬT"]
list_radio = {}
list_widget = {}
list_combo = {}
include = [
    col
    for col in df_traloi_xm.columns.tolist()
    if col not in ["SỐ GPHĐ FULL", "NGÀY TB SYT"]
]

text = r"""
\documentclass{{article}}
\usepackage[T5]{{fontenc}}
\nonstopmode
\usepackage[fontsize=14pt]{{scrextend}}
\usepackage[a4paper,vmargin=2cm,right=2cm,left=3cm,]{{geometry}}
\usepackage{{mathptmx}}
\usepackage{{ulem}}
\usepackage{{microtype}}
\renewcommand{{\ULdepth}}{{7pt}}
\renewcommand{{\ULthickness}}{{0.5pt}}
\usepackage{{setspace}}
\usepackage{{parskip}}
\setlength{{\parskip}}{{{GIÃN DÒNG}pt}}
\usepackage{{tabularray}}
\usepackage{{indentfirst}}
\setlength{{\parindent}}{{1.27cm}}
\thispagestyle{{empty}}
\usepackage{{fancyhdr}}
\fancyhf{{}}
\fancyhead[C]{{\small\thepage}}
\renewcommand{{\headrulewidth}}{{0pt}}
\pagestyle{{fancy}}
\begin{{document}}

\setstretch{{1}}
\noindent
\begin{{minipage}}{{\textwidth}}
\begin{{tblr}}{{width=1\linewidth,
		colspec={{X[1,c] X[1.9,c]}},
		colsep=0pt,
		rowsep=-3pt,
		row{{2}} = {{font=\bfseries}}}}
	\fontsize{{13pt}}{{0pt}}\selectfont UBND TỈNH VĨNH PHÚC & \fontsize{{13pt}}{{0pt}}\selectfont \textbf{{CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM}}\\
	{{\fontsize{{13pt}}{{0pt}}\selectfont SỞ Y TẾ\\[3pt]
		\rule[0.6\baselineskip]{{.14\linewidth}}{{.5pt}}}}   & \fontsize{{14pt}}{{0pt}}\selectfont \uline{{Độc lập - Tự do - Hạnh phúc}}\\[6pt]    
	\fontsize{{14pt}}{{0pt}}\selectfont Số: \hspace{{1.5cm}}/TB-SYT & \fontsize{{14pt}}{{0pt}}\selectfont \textit{{Phú Thọ, ngày {NGAY} tháng {THANG} năm {NAM}}}\\[6pt]
\end{{tblr}}
\end{{minipage}}

\vspace{{0.2cm}}

\noindent

\begin{{center}}\bfseries
	THÔNG BÁO\\Về việc đăng tải thông tin của Cơ sở khám bệnh, chữa bệnh tự công bố\\đáp ứng Thực hành tốt bảo quản thuốc\\
	
	\rule[0.6\baselineskip]{{.3\linewidth}}{{.5pt}}	
	
	
	
\end{{center}}

{{\centering Kính gửi: {TÊN CƠ SỞ KCB}\par}}


\vspace{{0.5cm}}

Sở Y tế tỉnh Phú Thọ nhận được Thông báo số {SỐ TB} ngày {NGÀY TB} của {TÊN CƠ SỞ KCB} về việc tự công bố kho bảo quản thuốc của cơ sở đạt Thực hành tốt bảo quản thuốc và đề nghị Sở Y tế thông báo trên Cổng thông tin điện tử Sở Y tế. 

Căn cứ Điều 13, Thông tư số 36/2018/TT-BYT ngày 22/11/2018 của Bộ trưởng Bộ Y tế quy định về Thực hành tốt bảo quản thuốc, nguyên liệu làm thuốc, Sở Y tế tỉnh Phú Thọ đồng ý công bố thông tin của {TÊN CƠ SỞ KCB}:

Tên cơ sở: {TÊN CƠ SỞ KCB};

Số giấy phép hoạt động khám bệnh, chữa bệnh: {SỐ GPHĐ FULL} do {NƠI CẤP GPHĐ} cấp ngày {NGÀY GPHĐ};

Địa chỉ: {dia chi co so KCB};

Người chịu trách nhiệm chuyên môn kỹ thuật: Bác sĩ {ten nguoi ptcm KỸ THUẬT};

Số điện thoại: {SỐ ĐT}; Email: {EMAIL};

Kho thuốc công bố đáp ứng Thực hành tốt bảo quản thuốc:

{KHO BẢO QUẢN THUỐC}

Thời điểm thông báo đáp ứng Thực hành tốt bảo quản thuốc: {NGÀY TB SYT}.

Sở Y tế yêu cầu {TÊN CƠ SỞ KCB} duy trì tuân thủ các điều kiện Thực hành tốt bảo quản thuốc theo quy định tại Khoản 4, Điều 4, Thông tư số 36/2018/TT-BYT./.

\vspace{{-0.5cm}}

\setstretch{{1}}
\noindent
\begin{{minipage}}[t]{{0.5\textwidth}} \singlespacing
   \fontsize{{12pt}}{{14pt}}\selectfont \textbf{{\textit{{Nơi nhận:}}}}\\
   \fontsize{{11pt}}{{13pt}}\selectfont - Như kính gửi;\\
   \fontsize{{11pt}}{{13pt}}\selectfont - Giám đốc, các PGĐ Sở;\\
   \fontsize{{11pt}}{{13pt}}\selectfont - Các phòng CN Sở Y tế;\\
   \fontsize{{11pt}}{{13pt}}\selectfont - TT KSBT (đăng tải Website Sở);\\
   \fontsize{{11pt}}{{13pt}}\selectfont - Lưu: VT, NVD.\\
\end{{minipage}}
\begin{{minipage}}[t]{{0.5\textwidth}}\singlespacing
\begin{{center}}
\textbf{{KT. GIÁM ĐỐC}}\\
\textbf{{PHÓ GIÁM ĐỐC}}\\
\vspace{{2.5cm}}
\textbf{{Nguyễn Đắc Ca}}\\
\end{{center}}
\end{{minipage}}
\end{{document}}
"""


def func(data_dict):
    global text

    (
        data_dict["NGAY"],
        data_dict["THANG"],
        data_dict["NAM"],
        data_dict["NGÀY TB SYT"],
    ) = get_current_date()
    text = text.format(**data_dict)
    fullname = f"THÔNG BÁO Vv đăng tải thông tin của Cơ sở khám bệnh, chữa bệnh tự công bố đáp ứng Thực hành tốt bảo quản thuốc: {data_dict['TÊN CƠ SỞ KCB']}"
    df_traloi_xm.loc[index_val] = data_dict
    df_traloi_xm.to_csv(path, index_label=indexs)
    auto_text_to_ioffice_pd(text, fullname, data_dict["SỐ TB"], "TB")


#
one_index(
    df_traloi_xm,
    include,
    index_val,
    indexs,
    list_radio,
    list_combo,
    list_widget,
    list_upper,
    list_title,
    default_values,
    multiline,
    list_ngay,
    list_phone,
    "NHẬP THÔNG TIN CƠ SỞ CÔNG BỐ GSP",
    func,
    600,
    700,
    100,
    0,
)

