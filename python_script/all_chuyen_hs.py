import re
import pandas as pd
from playwright_class import DichVu<PERSON><PERSON>
from god_class import send_notification, send_error_to_telegram, setup_logging
from loguru import logger


def check_hs_chuyen(value):
    # S<PERSON>a biểu thức chính quy để phù hợp với định dạng thời gian
    con_gio = re.search(r"Còn\s+(\d+)\s+giờ", value)
    tre_han = "hạn" in value or "Đúng" in value
    if con_gio:
        gio = int(con_gio.group(1))
        if gio <= 7:
            return True
    if tre_han:
        return True
    return False  # Thêm return False nếu không thỏa mãn điều kiện nào


class ChuyenHsTdtt(DichVuCong):
    def __init__(self, user):
        super().__init__(user)
        self.user = user

    def click_hs_chuyen(self, elements):
        dstdtt = pd.read_csv(
            "/home/<USER>/Dropbox/hnd/csv_source/thu_tuc_tham_dinh_tt.csv",
            dtype=str,
            index_col="MÃ",
        )
        found = False
        count = 0
        for element in elements:
            text = element.locator("div.tiendocvhs").text_content()
            if (
                check_hs_chuyen(text)
                and self.get_attribute("data-tentatthutuc", element)
                in dstdtt.index.tolist()
            ):
                element.locator("ins.iCheck-helper").click()
                found = True
                count += 1
                print(
                    dstdtt.loc[
                        self.get_attribute("data-tentatthutuc", element), "TÊN TẮT"
                    ]
                )
        if found:
            self.chuyen_buoc("son")
            send_notification(f"Đã chuyển {count} hồ sơ thẩm định thực tế", True)
        else:
            send_notification(f"Không có hồ sơ để chuyển")

    @logger.catch
    def run(self):
        super().setup(headless=False)
        super().login_dvc_by_user(self.user)
        self.go_to_trangthai("TD")
        self.expand_ds()
        elements = self.get_row_elements()
        self.click_hs_chuyen(elements)
        self.cleanup()


dvc = ChuyenHsTdtt("tungson")
dvc.run()
