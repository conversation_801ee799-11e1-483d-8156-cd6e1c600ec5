import os
from qlvb_auto_day import print_pdf
def find_latest_pdf(directory):
    # Đ<PERSON><PERSON> bảo đường dẫn là một thư mục
    if not os.path.isdir(directory):
        raise ValueError("Đường dẫn không hợp lệ hoặc không phải là một thư mục.")

    # Tì<PERSON> tất cả các file có định dạng .pdf trong thư mục
    pdf_files = [os.path.join(directory, file) for file in os.listdir(directory) if file.endswith('.pdf')]

    # Sắp xếp các file PDF theo thời gian sửa đổi cuối cùng ( từ mới nhất đến cũ nhất )
    latest_pdf = sorted(pdf_files, key=os.path.getmtime, reverse=True)

    # Trả về file PDF mới nhất hoặc None nếu không có file PDF nào
    return latest_pdf[0] if latest_pdf else None

# Sử dụng hàm
print_pdf(find_latest_pdf('/home/<USER>/Dropbox/hnd/latexall/vb_cho_in'))