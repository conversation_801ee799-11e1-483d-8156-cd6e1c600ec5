from csv_load_and_export import CsvLoaderFactory
import pandas as pd
import os


df = pd.read_excel("/home/<USER>/Dropbox/co_so_ban_le.xlsx", dtype=str)
df.set_index("so dkkd", inplace=True)
os.chdir("/home/<USER>/Dropbox/hnd/csv_source")
df_dkkd = CsvLoaderFactory.create_basic_loader().load_df("dkkd", "so dkkd")
df = df.merge(
    df_dkkd[["so dt chu hs", "trinh do cm"]],
    left_index=True,
    right_index=True,
    how="left",
)
df.to_excel("/home/<USER>/Dropbox/hnd/csv_source/ds_full.xlsx", index_label="so dkkd")
