import os
from datetime import datetime

from god_class import (
    dataframe_to_latex,
    TextProcess,
    get_current_date,
    insert_stt,
)
from csv_load_and_export import CsvLoaderFactory


os.chdir("/home/<USER>/Dropbox/hnd/csv_source")
df_gpp = CsvLoaderFactory.create_datetime_loader(
    ["ngay het han gpp", "ngay qd", "ngay cap cchnd"]
).load_df("du_lieu_gpp_all")
values = {}

now = datetime.now()
mask1 = df_gpp["ngay het han gpp"] >= datetime(2025, 1, 1)
mask2 = df_gpp["ngay het han gpp"] <= datetime(2025, 12, 31)

filtered_df = df_gpp.loc[mask1 & mask2]

filtered_df = filtered_df.sort_values(by="ngay het han gpp")
filtered_df["ngay het han gpp"] = filtered_df["ngay het han gpp"].dt.strftime(
    "%d/%m/%Y"
)

insert_stt(filtered_df)
filtered_df["so cchnd"] = filtered_df["so cchnd"].apply(
    lambda x: x + "/CCHND-" + "\par SYT-VP" if "-" not in x else x
)
filtered_df["ten qt-nt"] = (
    filtered_df["ten qt-nt"].str.title().str.replace("Thuốc", "thuốc")
)
filtered_df["so gpp"] = filtered_df["so gpp"] + "/GPP"
filtered_df["ten nguoi ptcm"] = filtered_df["ten nguoi ptcm"].str.title()


filtered_df = filtered_df[
    [
        "stt",
        "ten qt-nt",
        "dia chi co so",
        "ten nguoi ptcm",
        "so cchnd",
        "so gpp",
        "ngay het han gpp",
    ]
]


values["ngay"], values["thang"], values["nam"], values["today"] = get_current_date()
values["so_cs"] = str(filtered_df.shape[0]).zfill(2)
values["clip"] = dataframe_to_latex(filtered_df)
text = TextProcess("gpp_thong_bao_den_han")
text.format_text(values)
tieude = "THÔNG BÁO Danh sách cơ sở bán lẻ thuốc đến hạn đánh giá định kỳ việc duy trì đáp ứng Thực hành tốt cơ sở bán lẻ thuốc năm 2025"
text.auto_day_van_ban(tieude, "TB", "0")
