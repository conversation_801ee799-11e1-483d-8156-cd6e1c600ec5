import sys

import pandas as pd
from PyQt5.QtWidgets import (
    QApplication,
    QTableWidget,
    QTableWidgetItem,
    QVBoxLayout,
    QWidget,
)

# Đọ<PERSON> và xử lý dữ liệu
df = pd.read_csv("/home/<USER>/Dropbox/hnd/csv_source/co_so_ban_le.csv", dtype="str")
df.fillna("", inplace=True)
loc = df[(df["noi nhan"] != "") & (df["THANH TOÁN"] == "")]
loc.sort_values(by=["noi nhan"], inplace=True)
loc.reset_index(drop=True, inplace=True)
loc = loc[["ten qt-nt", "ten nguoi ptcm", "noi nhan", "THANH TOÁN"]]
# Khởi tạo ứng dụng PyQt
app = QApplication(sys.argv)
# Khởi tạo QWidget
window = QWidget()
window.setWindowTitle("PyQt5 Table Example")
window.resize(800, 800)  # Resize the window
# Khởi tạo QTableWidget
table = QTableWidget()
table.setRowCount(len(loc))
table.resizeRowsToContents()
table.setColumnCount(len(loc.columns))
table.setHorizontalHeaderLabels(loc.columns.tolist())

# Set column widths
column_widths = [15, 25, 15, 15]
for i, width in enumerate(column_widths):
    table.setColumnWidth(i, width * 10)  # Multiplied by 10 for better visibility

# Set data
for row in range(len(loc)):
    for col in range(len(loc.columns)):
        table.setItem(row, col, QTableWidgetItem(str(loc.iat[row, col])))

# Set font size to 15
font = table.font()
font.setPointSize(15)
table.setFont(font)

# Resize rows to fit content
table.resizeRowsToContents()
# Layout
layout = QVBoxLayout()
layout.addWidget(table)
window.setLayout(layout)

window.show()
sys.exit(app.exec())
