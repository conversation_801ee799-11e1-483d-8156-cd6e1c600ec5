{"cells": [{"cell_type": "code", "execution_count": 2, "id": "0c86ab3d", "metadata": {}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": 3, "id": "253b1442", "metadata": {}, "outputs": [], "source": ["df=pd.read_csv(\"/home/<USER>/Dropbox/hnd/csv_source/dkkd.csv\",dtype=str)"]}, {"cell_type": "code", "execution_count": 4, "id": "be6ab514", "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['ma ho so', 'so cchnd', 'thu tuc', 'ten nguoi ptcm', 'ten qt-nt',\n", "       'so qd', 'ngay qd', 'HẠN XỬ LÝ', 'NGÀY', 'thang', 'nam', 'ngay td',\n", "       'so gpp', 'dia chi co so', 'ten nguoi ptcm', 'gioi tinh', 'cmnd',\n", "       'noi cap cmnd', 'ngay cap cmnd', 'trinh do cm', 'so dt chu hs',\n", "       'noi cap cchnd', 'ngay cap cchnd', 'loai hinh', 'so dkkd cu',\n", "       'ngay dkkd cu', 'so gpp CŨ', 'ngay gpp cu', 'pham vi kd',\n", "       'vi tri hanh nghe', 'ngay sinh', 'dia chi thuong tru', 'noi tot nghiep',\n", "       'ngay tot nghiep', 'co nhan vien khong', 'ngay het han gpp',\n", "       'co quan chu quan', 'dia chi co quan chu quan', 'van de hs', 'noi nhan',\n", "       'bao quan lanh', 'trinh do tat', 'da nhan', '<PERSON><PERSON><PERSON> cấp', '<PERSON><PERSON> do cấp',\n", "       'so dkkd', 'so gpp'],\n", "      dtype='object')"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["df.columns"]}, {"cell_type": "code", "execution_count": 7, "id": "c7513d6e", "metadata": {}, "outputs": [], "source": ["df=df[['ma ho so','so dkkd','so gpp','so qd', 'ngay qd', 'so cchnd', 'thu tuc', 'ten nguoi ptcm', 'ten qt-nt','HẠN XỬ LÝ', 'NGÀY', 'thang', 'nam', 'ngay td',\n", "       'so gpp', 'dia chi co so', 'ten nguoi ptcm', 'gioi tinh', 'cmnd',\n", "       'noi cap cmnd', 'ngay cap cmnd', 'trinh do cm', 'so dt chu hs',\n", "       'noi cap cchnd', 'ngay cap cchnd', 'loai hinh', 'so dkkd cu',\n", "       'ngay dkkd cu', 'so gpp CŨ', 'ngay gpp cu', 'pham vi kd',\n", "       'vi tri hanh nghe', 'ngay sinh', 'dia chi thuong tru', 'noi tot nghiep',\n", "       'ngay tot nghiep', 'co nhan vien khong', 'ngay het han gpp',\n", "       'co quan chu quan', 'dia chi co quan chu quan', 'van de hs', 'noi nhan',\n", "       'bao quan lanh', 'trinh do tat', 'da n<PERSON>', '<PERSON><PERSON><PERSON> cấp', '<PERSON><PERSON> do cấp']]"]}, {"cell_type": "code", "execution_count": 8, "id": "8c88537a", "metadata": {}, "outputs": [], "source": ["df.to_csv('/home/<USER>/Dropbox/hnd/csv_source/dkkd.csv',index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "e84fd5a7", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "/home/<USER>/python313/bin/python", "name": "/home/<USER>/python313/bin/python"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "i/home/<USER>/python313/bin/python", "version": "3.12.6"}}, "nbformat": 4, "nbformat_minor": 5}