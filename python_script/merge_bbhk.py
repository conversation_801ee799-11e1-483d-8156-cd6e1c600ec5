import os
import pandas as pd
from god_class import TextProcess, get_dict_from_index_df
from top_most_get_text import input_dialog


class MergeBbhk:
    def __init__(self):
        self.file_latex = "form_bb_hk"
        self.df = pd.read_csv(
            "/home/<USER>/Dropbox/hnd/csv_source/dshn_duoc.csv", dtype=str
        )
        self.df.set_index("so cchnd", inplace=True)
        self.dkkd = pd.read_csv(
            "/home/<USER>/Dropbox/hnd/csv_source/dkkd.csv", dtype=str
        )
        self.dkkd.set_index("so dkkd", inplace=True)

    def get_so_cchnd(self):
        self.so_cchnd = input_dialog("Nhập số cchnd", "Nhập số cchnd", "")

    def get_values(self):
        self.values = get_dict_from_index_df(self.df, self.so_cchnd)
        self.values["so cchnd"] = (
            self.so_cchnd if "-" in self.so_cchnd else self.so_cchnd + "/CCHND-SYT-VP"
        )
        self.values["ngaykt"] = pd.Timestamp.now().strftime("%d/%m/%Y")

    def convert_values(self):
        if self.values["loai hinh"] == "Quầy thuốc":
            self.values["pham vi kd"] = (
                "Mua và bán lẻ thuốc thuộc Danh mục thuốc thiết yếu và Danh mục thuốc không kê đơn (có bao gồm: thuốc dạng phối hợp có chứa dược chất gây nghiện; thuốc dạng phối hợp có chứa dược chất hướng thần; thuốc dạng phối hợp có chứa tiền chất; thuốc độc; thuốc trong danh mục thuốc, dược chất thuộc danh mục chất bị cấm sử dụng trong một số ngành, lĩnh vực), trừ vắc xin; bảo quản ở điều kiện thường"
            )
        else:
            self.values["pham vi kd"] = (
                "Mua thuốc để bán lẻ (có bao gồm: thuốc dạng phối hợp có chứa dược chất gây nghiện; thuốc dạng phối hợp có chứa dược chất hướng thần; thuốc dạng phối hợp có chứa tiền chất; thuốc độc; thuốc trong danh mục thuốc, dược chất thuộc danh mục chất bị cấm sử dụng trong một số ngành, lĩnh vực), trừ vắc xin; bảo quản ở điều kiện thường"
            )
        self.values["ten qt-nt"] = (
            self.values["ten qt-nt"].title().replace("Thuốc", "thuốc")
        )
        self.values["ong-ba"] = (
            self.values["gioi tinh"].replace("0", "bà").replace("1", "ông")
        )
        self.values["ong-ba-title"] = self.values["ong-ba"].title()
        self.values["ten nguoi ptcm"] = self.values["ten nguoi ptcm"].title()

    def leak_values(self):
        so_dkkd = self.values["so dkkd"]
        self.values_leak = get_dict_from_index_df(self.dkkd, so_dkkd)
        self.values["ngay qd"] = self.values_leak["ngay qd"]
        self.values["ngay het han gpp"] = self.values_leak["ngay het han gpp"]
        self.values["noi cap cchnd"] = self.values_leak["noi cap cchnd"]

    def format_to_latex(self):
        file_latex = TextProcess(self.file_latex)
        file_latex.format_text(self.values)
        file_latex.compile_latex()
        file_latex.open_pdf()
        file_latex.copy_latex_file(
            f"/home/<USER>/Dropbox/hnd/latexall/hau_kiem/bb_hau_kiem/{self.values['ten qt-nt']}.tex"
        )


def main():
    merge_bbhk = MergeBbhk()
    merge_bbhk.get_so_cchnd()
    merge_bbhk.get_values()
    merge_bbhk.convert_values()
    merge_bbhk.leak_values()
    merge_bbhk.format_to_latex()


if __name__ == "__main__":
    main()
