from abc import ABC, abstractmethod
import pandas as pd
from typing import Optional

from god_class import send_notification


class DataLoader(ABC):
    @abstractmethod
    def load_df(self, file_name: str, index_col: Optional[str] = None) -> pd.DataFrame:
        pass


class FilePathProvider:
    def __init__(self, base_path: str):
        self.base_path = base_path

    def get_filepath(self, file_name: str) -> str:
        return f"{self.base_path}/{file_name}.csv"


class DataFrameTransformer(ABC):
    @abstractmethod
    def transform(self, df: pd.DataFrame) -> pd.DataFrame:
        pass


class FillNaTransformer(DataFrameTransformer):
    def transform(self, df: pd.DataFrame) -> pd.DataFrame:
        return df.fillna("")


class DateTimeTransformer(DataFrameTransformer):
    def __init__(self, date_columns: list[str]):
        self.date_columns = date_columns

    def transform(self, df: pd.DataFrame) -> pd.DataFrame:
        for col in self.date_columns:
            df[col] = df[col].fillna("01/01/1900")
            df[col] = pd.to_datetime(df[col], format="%d/%m/%Y", errors="coerce")
            if df[col].isna().any():
                send_notification(
                    f"Cột {col.upper()} ở file {df.attrs['name'].upper()} có các giá trị không chuyển đổi được sang định dạng ngày tháng, xem file {df.attrs['name']}_error.csv, sort len xem gia tri rong bang visidata",
                    True,
                )
                df.to_csv(f"{df.attrs['name']}_error.csv", index=False)
        return df


class CsvLoader(DataLoader):
    def __init__(
        self,
        file_path_provider: FilePathProvider,
        transformers: Optional[list[DataFrameTransformer]] = None,
    ):
        self.file_path_provider = file_path_provider
        self.transformers = transformers or []

    def load_df(self, file_name: str, index_col: Optional[str] = None) -> pd.DataFrame:
        filepath = self.file_path_provider.get_filepath(file_name)
        df = pd.read_csv(filepath, dtype=str)
        df.attrs["name"] = file_name

        for transformer in self.transformers:
            df = transformer.transform(df)

        if index_col:
            df.set_index(index_col, inplace=True)

        return df


# Factory for creating common loader configurations
class CsvLoaderFactory:
    @staticmethod
    def create_basic_loader() -> CsvLoader:
        path_provider = FilePathProvider("/home/<USER>/Dropbox/hnd/csv_source")
        return CsvLoader(path_provider)

    @staticmethod
    def create_fillna_loader() -> CsvLoader:
        path_provider = FilePathProvider("/home/<USER>/Dropbox/hnd/csv_source")
        return CsvLoader(path_provider, [FillNaTransformer()])

    @staticmethod
    def create_datetime_loader(date_columns: list[str]) -> CsvLoader:
        path_provider = FilePathProvider("/home/<USER>/Dropbox/hnd/csv_source")
        return CsvLoader(path_provider, [DateTimeTransformer(date_columns)])


class DataExporter(ABC):
    def __init__(self, df: pd.DataFrame):
        self.df = df

    @abstractmethod
    def format_data(self) -> pd.DataFrame:
        """Format the data before exporting"""
        pass

    def export_to_csv(self, file_name: str, index_label: str = None):
        formatted_df = self.format_data()
        if index_label:
            formatted_df.to_csv(f"{file_name}.csv", index_label=index_label)
        else:
            formatted_df.to_csv(f"{file_name}.csv", index=False)


class DataNormalExporter(DataExporter):
    def format_data(self) -> pd.DataFrame:
        return self.df.copy()


class DataDateFormatter(DataExporter):
    def __init__(self, df: pd.DataFrame, date_columns: list[str]):
        super().__init__(df)
        self.date_columns = date_columns

    def format_data(self) -> pd.DataFrame:
        formatted_df = self.df.copy()
        for col in self.date_columns:
            formatted_df[col] = formatted_df[col].dt.strftime("%d/%m/%Y")
        return formatted_df
