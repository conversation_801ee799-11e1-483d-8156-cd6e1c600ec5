"""
Module xử lý cấp GDP định kỳ.
<PERSON><PERSON><PERSON> này chứa các hàm và chức năng để xử lý việc cấp GDP định kỳ cho các cơ sở kinh doanh dược.
<PERSON>o gồm các chức năng như:
- Cập nhật thông tin GDP
- Tạo file latex
- Xử lý dữ liệu từ file CSV
"""

import os
import pandas as pd
from loguru import logger

from god_class import (
    TextProcess,
    convert_unix_style,
    get_dict_from_index_df,
    get_current_date,
    update_df_from_dict_by_index,
    update_df_name_da_<PERSON>han,
    yes_no,
    run_command_new_tmux,
)
from create_snippets import creat_and_get
from top_most_get_text import input_dialog


def creat_latex_file(path_latex, text):
    """
    Tạo file latex từ đường dẫn và nội dung được cung cấp.

    Args:
        path_latex (str): Đường dẫn đến file latex cần tạo
        text (str): Nội dung cần ghi vào file latex

    Returns:
        None
    """
    with open(path_latex, "w", encoding="utf-8") as f:
        f.write(text)


os.chdir("/home/<USER>/Dropbox/hnd/csv_source")


@logger.catch
def run_main(mhs):
    df_gdp = pd.read_csv("gdp.csv", dtype="str")
    so_dkkd = df_gdp.loc[df_gdp["ma ho so"] == mhs, "so dkkd"].values[0]
    if not so_dkkd:
        so_dkkd = input_dialog("NHẬP", f"NHẬP SỐ GCN DKKD CHO ma ho so {mhs}", "")
    df_gdp.set_index("so dkkd", inplace=True, drop=True)
    df_gdp.fillna("", inplace=True)
    df_name = pd.read_csv("name.csv", dtype="str", index_col="ma ho so")

    old_dict = get_dict_from_index_df(df_gdp, so_dkkd)  # lấy dữ liệu t
    default_dict = {
        "loai hinh 1-cong ty, 2-chi nhanh": "",
        "loai hinh": "",
        "so cchnd": old_dict.get("so cchnd", ""),
        "ten cong ty": old_dict.get("ten cong ty", ""),
        "tru so": old_dict.get("tru so", ""),
        "KHO 1": old_dict.get("KHO 1", ""),
        "KHO 2": old_dict.get("KHO 2", ""),
        "ngay dkkd cu": old_dict.get("ngay dkkd cu", ""),
        "noi cap cchnd": old_dict.get("noi cap cchnd", ""),
        "ngay cap cchnd": old_dict.get("ngay cap cchnd", ""),
        "ten nguoi ptcm": old_dict.get("ten nguoi ptcm", ""),
        "ngay sinh": old_dict.get("ngay sinh", ""),
        "dia chi thuong tru": old_dict.get("dia chi thuong tru", ""),
        "cmnd": old_dict.get("cmnd", ""),
        "ngay cap cmnd": old_dict.get("ngay cap cmnd", ""),
        "so dt chu hs": old_dict.get("so dt chu hs", ""),
        "pham vi kd": old_dict.get("pham vi kd", ""),
        "GIAM_DOC": old_dict.get("GIAM_DOC", ""),
        "van de hs": "",
    }

    values = creat_and_get(default_dict)
    values["so_dkkd"] = so_dkkd
    values["loai hinh"] = (
        values["loai hinh"].replace("1", "Công ty").replace("2", "Chi nhánh")
    )
    from solid_string_format import DictStringFormatter

    dt = DictStringFormatter(values)

    list_upper = ["ten nguoi ptcm"]
    list_title = ["ten nguoi ptcm"]
    list_ngay = ["ngay cap cchnd", "ngay sinh", "ngay cap cmnd", "ngay qd"]
    list_phone = ["so dt chu hs"]

    values = (
        dt.apply_upper(list_upper)
        .apply_title(list_title)
        .apply_phone_format(list_phone)
        .apply_date_format(list_ngay)
        .get_result()
    )

    values["dia chi co so"] = values["tru so"]
    values["ten qt-nt"] = values["ten cong ty"]
    values["ten ct upper"] = values["ten cong ty"].upper()
    values["so dkkd"] = values["so gdp"] = so_dkkd
    values["loai cap"] = "Cấp lại GDP"
    values["so cchnd"] = values["so cchnd"].replace("/CCHND-SYT-VP", r"\\/CCHND-SYT-VP")
    values["ngay"], values["thang"], values["nam"], values["ngay qd"] = (
        get_current_date()
    )
    values["ngay het han gdp"] = (
        f"{values['ngay']}/{values['thang']}/{int(values['nam']) + 3}"
    )
    values["han_gdp_text"] = (
        f"Giấy chứng nhận này có giá trị đến ngày {values['ngay']} tháng {values['thang']} năm {int(values['nam']) + 3}./."
    )
    values["nam_han"] = int(values["nam"]) + 3
    df_gdp["so dkkd"] = df_gdp.index.str.replace("/ĐKKDD", r"\\/ĐKKDD")
    df_gdp = df_gdp.set_index("ma ho so", drop=True)
    update_df_from_dict_by_index(df_gdp, "gdp.csv", values, mhs)

    update_df_name_da_nhan(df_name, values, mhs)

    bbtd = TextProcess("bbtd_gdp_tdt")
    bbtd.format_text(values)

    tenct = convert_unix_style(values["ten cong ty"])
    if yes_no("Có ra QĐ không", "Có ra QĐ không?"):
        qd = TextProcess("gdp_quyet_dinh_dky")
        tieude = f"QUYẾT ĐỊNH Về việc cấp Giấy chứng nhận đạt Thực hành tốt phân phối thuốc cho {values['ten cong ty']}"
        qd.auto_day_van_ban(tieude, "QĐ", "0")

    if yes_no("Có ra kq luôn không", "Có ra kq luôn không?"):
        kq = TextProcess("form_gdp_new")
        kq.format_text(values)
        year = values["nam"]
        path = f"/home/<USER>/Dropbox/hnd/latexall/gdp_tex/{year}_{tenct}_{mhs}.tex"
        kq.copy_latex_file(path)
        run_command_new_tmux(f"nvim {path}", "latex-edit")


if __name__ == "__main__":
    input_mhs = input_dialog("NHẬP", "NHẬP ma ho so", "")
    if input_mhs:
        run_main(input_mhs)
