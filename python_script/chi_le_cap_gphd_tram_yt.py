import os
import shutil

import pandas as pd

from god_class import convert_ngay, compile_latex

os.chdir("/home/<USER>/Dropbox/hnd/latexall/mylatex")
df_ds_tram = pd.read_excel("/home/<USER>/Dropbox/hnd/csv_source/GPHD.xlsx", dtype=str)
print(df_ds_tram)
df_ds_tram["NGÀY"] = df_ds_tram["NGÀY"].apply(convert_ngay)

tinh_xuoc = [518, 522]
thanh_pho_xuoc = [514, 519, 520, 521]
phuong_xuoc = [505]
tram_them = [437]
so_gp = 479  # số trước số bắt đầu


def lower_first_char(text):
    return text[0].lower() + text[1:]


for index, value in df_ds_tram.iterrows():
    so_gp += 1
    so_gphd = f"00{so_gp}"
    ngay, thang, nam = value["NGÀY"].split("/")
    so_cchn = value["SỐ CHỨNG CHỈ HÀNH NGHỀ"]
    ten_cs = "Trạm Y tế " + value["ĐỊA ĐIỂM"][::-1].split(",")[2][::-1]
    ten_cs = ten_cs.upper()
    nguoi_ptcm = value["TÊN NGƯỜI PTCM"].title()
    ngay_cap = convert_ngay(value["NGÀY CẤP"])
    dia_diem = lower_first_char(value["ĐỊA ĐIỂM"].replace("TDP", "tổ dân phố"))

    if len(dia_diem) < 59:
        gian_dong = 1.5
    else:
        gian_dong = 1.35
    if so_gp in tinh_xuoc:
        dia_diem = dia_diem.replace("tỉnh Vĩnh Phúc", r"\\tỉnh Vĩnh Phúc")
    elif so_gp in thanh_pho_xuoc:
        dia_diem = dia_diem.replace("thành phố", r"\\thành phố")
    elif so_gp in phuong_xuoc:
        dia_diem = dia_diem.replace("phường", r"\\phường")
    elif so_gp in tram_them:
        gian_dong = 1.5
    text = rf"""
        \documentclass{{article}}
    \usepackage[utf8]{{inputenc}}
    \usepackage[T5]{{fontenc}}
    \nonstopmode
    \usepackage{{graphicx}}
    \usepackage[a4paper, landscape,right=2cm,left=2cm,top=2.3cm,bottom=2cm]{{geometry}}
    \usepackage[fontsize=14pt]{{scrextend}}
    \usepackage{{times}}
    \usepackage{{setspace}}
    \usepackage{{eso-pic}}
    \usepackage{{parskip}}
    \setlength{{\parskip}}{{0pt}}
    \usepackage{{tabularray}}
    \usepackage{{ulem}}
    \renewcommand{{\ULdepth}}{{5pt}}
    \renewcommand{{\ULthickness}}{{0.6pt}}
    \setlength{{\parindent}}{{0pt}}
    \begin{{document}}

    \setstretch{{1}}
    \pagestyle{{empty}}
    \AddToShipoutPictureBG{{\includegraphics[width=\paperwidth, height=\paperheight]{{GPHD.png}}}}

    \noindent
    \begin{{minipage}}{{\textwidth}}
    \begin{{tblr}}{{width=1\textwidth,
            colspec={{X[1.6,c] X[3,c] X[1.6,c]}},
            colsep=0pt,row{{2}}={{font=\bfseries}},
            rowsep=0pt}}
    	\fontsize{{13pt}}{{0pt}}\selectfont UBND TỈNH VĨNH PHÚC & \fontsize{{14pt}}{{0pt}}\selectfont \textbf{{CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM}}&\\
    	{{\fontsize{{13pt}}{{0pt}}\selectfont SỞ Y TẾ\\[3pt]
    \rule[0.6\baselineskip]{{.14\linewidth}}{{.7pt}}}} & \fontsize{{14pt}}{{0pt}}\selectfont \uline{{Độc lập - Tự do - Hạnh phúc}}&\\[6pt]
    Số: {{\color{{red}}\textbf{{{so_gphd}}}}}/VP-GPHĐ &   &\\
    \end{{tblr}}
\end{{minipage}}

    \vspace{{-0.15cm}}


    \begin{{center}}
        \color{{red}} \bfseries  \fontsize{{20pt}}{{0pt}}\selectfont
        GIẤY PHÉP HOẠT ĐỘNG\\
        KHÁM BỆNH, CHỮA BỆNH\\[7pt]

        \fontsize{{14pt}}{{0pt}}\selectfont \color{{black}}
        GIÁM ĐỐC SỞ Y TẾ\\
    \end{{center}}


    \setstretch{{1.3}}

    \vspace{{0.2\baselineskip}}

    Căn cứ Luật Khám bệnh, chữa bệnh ngày 09 tháng 01 năm 2023;\\
    Căn cứ Nghị định số 96/2023/NĐ-CP ngày 30 tháng 12 năm 2023 của Chính phủ quy định chi tiết một số điều của Luật Khám bệnh, chữa bệnh;\\
    Xét đề nghị của Trưởng phòng Nghiệp vụ Dược Sở Y tế.\\

    \vspace{{-0.4cm}}

    \begin{{center}}

        \bfseries CẤP PHÉP HOẠT ĐỘNG KHÁM BỆNH, CHỮA BỆNH\\


    \end{{center}}

    \vspace{{0.4cm}}



    Tên cơ sở khám bệnh, chữa bệnh: \textbf{{{ten_cs}}}

    \setstretch{{1.5}}

    \begin{{minipage}}[t]{{0.67\textwidth}}  %gian dòng

        Hình thức tổ chức: Trạm Y tế

    Địa chỉ hoạt động: {dia_diem}

        Phạm vi hoạt động chuyên môn: Thực hiện các kỹ thuật chuyên môn được Giám đốc
        
        Sở Y tế phê duyệt ban hành kèm theo Giấy phép hoạt động khám bệnh, chữa bệnh

        Thời gian làm việc hằng ngày: 24/24 giờ, 07 ngày/tuần.

    \end{{minipage}}\hfill
    \begin{{minipage}}[t]{{0.31\textwidth}}\setstretch{{1}}
        \begin{{center}}
            \textit{{Phú Thọ, ngày {ngay} tháng {thang} năm {nam}}}\\
            \textbf{{KT. GIÁM ĐỐC}}\\
            \textbf{{PHÓ GIÁM ĐỐC}}\\
    \vspace{{3cm}}
    \textbf{{Nguyễn Đắc Ca}}
        \end{{center}}
    \end{{minipage}}
    \end{{document}}
        """
    compile_latex(text, ten_cs, so_gphd)
    shutil.copy("mylatex.tex", f"{ten_cs}.tex")
