"""
<PERSON><PERSON>t này tự động hóa quy trình xử lý các tệp ZIP chứa hồ sơ.

<PERSON><PERSON> thực hiện các công việc sau:
1. <PERSON><PERSON><PERSON><PERSON> nén các tệp ZIP chính từ thư mục `/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pdf_documents/filehs` và các tệp ZIP con bên trong thư mục đã giải nén.
2. <PERSON><PERSON><PERSON> thông tin từ tệp `temp.csv` để quản lý hồ sơ.
3. Di chuyển và sao chép các thư mục hồ sơ dựa trên trạng thái "đã nhận"
   và các thông tin khác từ CSV (mã hồ sơ, tên người đăng ký, thủ tục).
4. Tạo bản sao lưu cho các hồ sơ đã xử lý vào thư mục `file_hs_backup`.
5. <PERSON><PERSON><PERSON> hiện các tệp mới hoặc cập nhật trong hồ sơ so với bản sao lưu.
   <PERSON><PERSON><PERSON> có tệp mới, nó sẽ tạo một tệp ZIP chứa các tệp bổ sung này
   với tên theo định dạng `BSHS_{mã hồ sơ}_{thủ tục}_{người đăng ký}.zip`.
6. Gửi tệp ZIP chứa các tài liệu bổ sung qua Telegram.
7. Gửi thông báo qua Telegram nếu hồ sơ không có bổ sung và có vấn đề
   được ghi nhận trong tệp CSV.
8. Dọn dẹp các thư mục tạm và thư mục đã giải nén ban đầu sau khi xử lý.
"""

import shutil

import pandas as pd
import os

from unidecode import unidecode
from god_class import send_notification, TelegramSend


def move_zip_file():
    tele_file_hs = TelegramSend("pdfhs")

    os.chdir("/home/<USER>/Dropbox/hnd/csv_source")

    from god_class import FileZipManager

    zip_file_hs = FileZipManager(
        "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pdf_documents/filehs"
    )

    zip_file_hs.extract_all_zip_files()

    zip_inner = FileZipManager(zip_file_hs.folders[0])

    zip_inner.extract_all_zip_files()

    df_temp = pd.read_csv("temp.csv", index_col="ma ho so")

    list_mhs = [
        folder
        for folder in zip_inner.folders
        if df_temp.at[os.path.basename(folder), "da nhan"] != "1"
    ]
    for folder in list_mhs:
        mhs = os.path.basename(folder)
        nguoidangky = (
            unidecode(df_temp.at[mhs, "ten nguoi ptcm"].split("(")[0])
            .replace(" ", "_")
            .lower()
        )
        thutuc = df_temp.at[mhs, "thu tuc"]
        destination_path = f"/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pdf_documents/filehs/{mhs}--{thutuc}--{nguoidangky}"

        # Xóa thư mục đích nếu đã tồn tại
        if os.path.exists(destination_path):
            shutil.rmtree(destination_path)

        shutil.copytree(folder, destination_path)

        backup_destination = f"/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pdf_documents/file_hs_backup/{mhs}--{thutuc}--{nguoidangky}"
        if not os.path.exists(backup_destination):
            shutil.move(folder, backup_destination)
        else:
            # Kiểm tra số lượng file trong hai thư mục
            folder_files = set(os.listdir(folder))
            destination_files = set(os.listdir(backup_destination))

            if folder_files == destination_files and pd.notna(
                df_temp.at[mhs, "van de hs"]
            ):
                print("không bo sung hồ sơ")
                send_notification(
                    f"{mhs}--{thutuc}--{nguoidangky} không bo sung hồ sơ", True
                )
            elif folder_files != destination_files:
                # Tìm các file mới trong folder
                new_files = folder_files - destination_files

                if new_files:
                    # Tạo thư mục tạm để chứa các file mới
                    temp_dir = os.path.join(os.path.dirname(folder), "temp_new_files")
                    os.makedirs(temp_dir, exist_ok=True)

                    # Copy các file mới vào thư mục tạm
                    for file in new_files:
                        shutil.copy2(os.path.join(folder, file), temp_dir)

                    # Nén thư mục tạm thành file zip
                    zip_filename = f"BSHS_{mhs}_{thutuc}_{nguoidangky}.zip"
                    zip_filepath = os.path.join(os.path.dirname(folder), zip_filename)
                    shutil.make_archive(zip_filepath[:-4], "zip", temp_dir)
                    tele_file_hs.send_file_normal(zip_filepath)

                    # Xóa thư mục tạm và file zip
                    shutil.rmtree(temp_dir)
                    os.remove(os.path.join(os.path.dirname(folder), zip_filename))

    shutil.rmtree(zip_file_hs.folders[0])


if __name__ == "__main__":
    move_zip_file()
