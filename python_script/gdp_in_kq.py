import os

from playwright_class import CapTaiKhoanKetNoi
import pandas as pd
from god_class import (
    get_current_date,
    TextProcess,
    open_latex_with_tmux,
    send_notification,
    update_df_from_dict_by_index,
    yes_no,
)
from top_most_get_text import input_dialog

os.chdir("/home/<USER>/Dropbox/hnd/csv_source")


def danh_dau_cho_tra(mahs):
    df = pd.read_csv("name.csv", dtype="str", index_col="ma ho so")
    df.loc[df.index == mahs, "CHỜ TRẢ"] = "1"
    df.to_csv("name.csv", index_label="ma ho so")


def run_main():
    so_qd = input_dialog("NHẬP", "Nhập số QĐ", "")
    df_gdp = pd.read_csv("gdp.csv", dtype="str", index_col="ma ho so")
    mask = (
        df_gdp["so qd"].isnull()
        & (df_gdp["so dkkd"].notnull())
        & (df_gdp["ngay qd"].notnull())
    )
    df_filter = df_gdp[mask]
    if df_filter.empty:
        mask = df_gdp["so qd"] == so_qd
        df_filter = df_gdp[mask]
    values = df_filter.to_dict("records")[0]
    values["so qd"] = so_qd
    values["ma ho so"] = df_filter.index[0]

    values["ngay"], values["thang"], values["nam"] = values["ngay qd"].split("/")
    values["ngayht"], values["thanght"], values["namht"], _ = get_current_date()
    values["ten cong ty"] = values["ten cong ty"].upper()
    values["ten nguoi ptcm"] = values["ten nguoi ptcm"].title()
    values["nam_han"] = int(values["nam"]) + 3
    values["so cchnd"] = (
        values["so cchnd"] + "/CCHND-SYT-VP"
        if "/" not in values["so cchnd"]
        else values["so cchnd"]
    )

    update_df_from_dict_by_index(df_gdp, "gdp.csv", values, values["ma ho so"])
    danh_dau_cho_tra(values["ma ho so"])

    gdp = TextProcess("form_gdp_new")
    gcn = TextProcess("form_dkkd_gdp_new")
    gdp.format_text(values)

    gdp.copy_latex_file(
        f"/home/<USER>/Dropbox/hnd/latexall/gdp_tex/gdp-{values['ma ho so']}-{values['ten cong ty']}.tex"
    )
    gcn.format_text(values)
    gdp.copy_latex_file(
        f"/home/<USER>/Dropbox/hnd/latexall/gdp_tex/gcn-dkkd-{values['ma ho so']}-{values['ten cong ty']}.tex"
    )
    open_latex_with_tmux(
        f"/home/<USER>/Dropbox/hnd/latexall/gdp_tex/gdp-{values['ma ho so']}-{values['ten cong ty']}.tex",
        "gdp",
    )
    open_latex_with_tmux(
        f"/home/<USER>/Dropbox/hnd/latexall/gdp_tex/gcn-dkkd-{values['ma ho so']}-{values['ten cong ty']}.tex",
        "gcn-dkkd",
    )
    if yes_no("Thông báo", "Có cấp tài khoản kết nối không"):
        cap = CapTaiKhoanKetNoi(values["ngay qd"])
        cap.process_all_gdp()
        send_notification("ĐÃ XONG")


if __name__ == "__main__":
    run_main()
