import os
import pickle
import subprocess
import logging
import sys

from PIL import Image
from rembg import remove

# Cấu hình logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler(sys.stderr)],
)


def send_notification(message):
    try:
        notify_text = "@@@@@@@@@@@@@@@@@@@@@@@@@@@@\n\n" + message
        notify_command = ["notify-send", notify_text]
        subprocess.run(notify_command, check=True)
    except subprocess.CalledProcessError as e:
        logging.error(f"Lỗi khi gửi thông báo: {e}")


def load_pickle(file):
    try:
        with open(file, "rb") as f:
            return pickle.load(f)
    except FileNotFoundError:
        logging.error(f"Không tìm thấy file pickle: {file}")
        raise
    except pickle.PickleError as e:
        logging.error(f"Lỗi khi đọc file pickle: {e}")
        raise


def tach_nen(mhs, path):
    try:
        # Tạ<PERSON> thư mục đích nếu chưa tồn tại
        output_dir = "/home/<USER>/anh_cchnd"
        os.makedirs(output_dir, exist_ok=True)

        # Kiểm tra quyền ghi
        if not os.access(output_dir, os.W_OK):
            raise PermissionError(f"Không có quyền ghi vào thư mục {output_dir}")

        image_count = 0
        for root, _, files in os.walk(path):
            for file in files:
                if file.lower().endswith((".png", ".jpg", ".jpeg")):
                    image_count += 1
                    input_image_path = os.path.join(root, file)
                    logging.info(f"Đang xử lý ảnh: {input_image_path}")

                    try:
                        input_image = Image.open(input_image_path)
                    except Exception as e:
                        logging.error(f"Lỗi khi mở ảnh {input_image_path}: {e}")
                        continue

                    try:
                        # Xóa nền và lưu ảnh đầu ra
                        output_image = remove(input_image)
                        image_path = os.path.join(output_dir, f"{mhs}.png")

                        output_image.save(image_path)
                        logging.info(f"Đã lưu ảnh vào: {image_path}")

                        # Sử dụng Gwenview để mở ảnh
                        subprocess.Popen(["feh", image_path])
                        send_notification("Đang tách nền")
                        return  # Thoát sau khi xử lý thành công 1 ảnh
                    except Exception as e:
                        logging.error(f"Lỗi khi xử lý ảnh {input_image_path}: {e}")
                        continue

        if image_count == 0:
            raise ValueError(f"Không tìm thấy ảnh trong thư mục: {path}")
        elif image_count > 1:
            raise ValueError(f"Tìm thấy nhiều hơn 1 ảnh trong thư mục: {path}")

    except Exception as e:
        logging.error(f"Lỗi trong hàm tach_nen: {e}")
        raise


def main():
    try:
        path = load_pickle(
            "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pickle_file/hs_path.pk"
        )
        if not path:
            raise ValueError("Không đọc được đường dẫn từ file pickle")

        parts = path.split("/")[-1].split("--")
        if len(parts) != 3:
            raise ValueError(f"Định dạng đường dẫn không hợp lệ: {path}")

        mhs, thutuc, _ = parts
        tach_nen(mhs, path)
        subprocess.Popen(["pkill", "kitty"])

    except Exception as e:
        logging.error(f"Lỗi chính: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
