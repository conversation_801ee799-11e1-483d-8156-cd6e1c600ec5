import datetime as dt
import os

import pandas as pd

from god_class import insert_stt, convert_ngay, pd_to_date, yes_no
from top_most_get_text import input_dialog
from loguru import logger
from god_class import TextProcess


def get_last_monday():
    today = dt.datetime.today()
    # Tìm ngày thứ 2 gần nhất
    weekday = today.weekday()
    days_since_monday = (weekday) % 7
    last_monday = today - dt.timedelta(days=days_since_monday)
    return last_monday.date()


def get_nearest_monday():
    today = dt.date.today()
    one_day = dt.timedelta(days=1)
    monday = today + one_day * ((7 - today.weekday()) % 7)
    return monday


@logger.catch
def run_main():
    os.chdir("/home/<USER>/Dropbox/hnd/csv_source")
    df_name = pd.read_csv("name.csv", dtype="str")
    values = {}
    df_thu_tuc_tdtt = pd.read_csv("thu_tuc_tham_dinh_tt.csv", dtype=str)
    list_thutuc = df_thu_tuc_tdtt["TÊN TẮT"].tolist()
    is_tdtt = df_name["thu tuc"].isin(list_thutuc)
    is_dadi = df_name["da di"] != "1"
    not_dang_tamdung = df_name["trang thai"] != "ĐANG TẠM DỪNG"
    # is_ten_qtnt = df_name["ten qt-nt"].notnull()
    not_vande_gpp = df_name["van de hs"] != "gpp"
    condition = is_tdtt & is_dadi & not_dang_tamdung & not_vande_gpp
    # filtered_df = df[df['thu tuc'].str.contains('GPP')]
    # if filtered_df['ten qt-nt'].isnull().any():
    #     show_message("CÒN QT CHƯA NHẬP", "CHƯA NHẬP HẾT")
    ngaytb = (
        get_last_monday() if dt.date.today().weekday() == 1 else get_nearest_monday()
    )
    values["ngaybd"] = ngaytb
    values["ngaykt"] = convert_ngay(
        (values["ngaybd"] + dt.timedelta(days=4)).strftime("%d%m%Y")
    )
    values["ngaybd"] = convert_ngay(values["ngaybd"].strftime("%d%m%Y"))

    df_name["han back_up"] = pd.to_datetime(
        df_name["han back_up"], format="%d/%m/%Y %H:%M:%S"
    )
    df_name = df_name.sort_values(by="han back_up")
    df_dsdi = df_name[condition]
    insert_stt(df_dsdi)
    df_dsdi.fillna("", inplace=True)
    # df_dsdi["dia chi co so"] = df_dsdi["dia chi co so"].apply(change_xa)
    mask = df_dsdi["ten qt-nt"].str.contains("THUỐC")
    df_dsdi.loc[mask, "ten qt-nt"] = (
        df_dsdi.loc[mask, "ten qt-nt"].str.title().str.replace("Thuốc", "thuốc")
    )
    df_dsdi["ten nguoi ptcm"] = df_dsdi["ten nguoi ptcm"].str.title()
    ngaydau = input_dialog("Title", "NHẬP NGÀY ĐẦU", "")
    if not ngaydau:
        return
    ngaythuhai = input_dialog("Title", "NGÀY THỨ HAI", ngaydau)
    if not ngaythuhai:
        return
    buoi1 = input_dialog("Title", "stt BUỔI 1", "")
    buoi2 = input_dialog("Title", "stt BUỔI 2", "")
    stt1 = []
    stt2 = []
    if buoi1:
        stt1 = buoi1.split(" ")
    if buoi2:
        stt2 = buoi2.split(" ")

    ngaydau = convert_ngay(ngaydau)
    ngaythuhai = convert_ngay(ngaythuhai)
    stt = stt1 + stt2
    mask = df_dsdi["stt"].astype(str).isin(stt)
    df_dsdi = df_dsdi[mask]
    buoi1 = {stt: ngaydau for stt in stt1}
    buoi2 = {stt: ngaythuhai for stt in stt2}
    df_dsdi["ngay td"] = df_dsdi["stt"].astype(str).map(buoi1 | buoi2)

    pd_to_date(df_dsdi, "ngay td")
    df_dsdi.sort_values("ngay td", ascending=True, inplace=True)
    df_dsdi["ngay td"] = df_dsdi["ngay td"].dt.strftime("%d%m%Y")
    df_dsdi["ngay td"] = df_dsdi["ngay td"].apply(convert_ngay)
    df_lichthamdinh = df_dsdi[
        [
            "ten qt-nt",
            "ten nguoi ptcm",
            "dia chi co so",
            "gioi tinh",
            "ma ho so",
            "so dt chu hs",
            "ngay sinh",
            "ngay td",
        ]
    ]
    insert_stt(df_lichthamdinh)
    df_lichthamdinh.to_csv("lichthamdinh.csv", index=False)
    df_dsdi = df_dsdi[
        ["ten qt-nt", "ten nguoi ptcm", "dia chi co so", "ngay td"]
    ]
    insert_stt(df_dsdi)
    ds = df_dsdi.astype(str).apply("&".join, axis=1)
    values["clip"] = ds.str.cat(sep=r"\\" + "\n") + r"\\"
    ngaytb = convert_ngay(ngaytb.strftime("%d%m%Y"))
    values["ngay"] = ngaytb.split("/")[0]
    values["thang"] = ngaytb.split("/")[1]
    values["nam"] = ngaytb.split("/")[2]
    values["socs"] = str(len(df_dsdi)).zfill(2)

    if yes_no("có mỹ phẩm không", "có mỹ phẩm không"):
        file_text = TextProcess("form_tb_lich_td_mp")
        name = f"THÔNG BÁO Lịch thẩm định thực tế cơ sở kinh doanh dược, cơ sở sản xuất mỹ phẩm tuần từ ngày {values['ngaybd']} đến ngày {values['ngaykt']}"
    else:
        file_text = TextProcess("form_tb_lich_td")
        name = f"THÔNG BÁO Lịch thẩm định thực tế cơ sở kinh doanh dược tuần từ ngày {values['ngaybd']} đến ngày {values['ngaykt']}"
    file_text.format_text(values)
    file_text.auto_day_van_ban(name, "TB", 0)


if __name__ == "__main__":
    run_main()
