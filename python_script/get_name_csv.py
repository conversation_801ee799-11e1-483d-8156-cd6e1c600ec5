import os
import pandas as pd

from god_class import update_multi_with_vlookup, update_with_vlookup

os.chdir("/home/<USER>/Dropbox/hnd/csv_source")

df = pd.read_csv("thamdinh.csv", skiprows=4)
df.fillna("", inplace=True)

df["ma thu tuc"] = df["ve viec"].str.split(" - ").str[0]

df["dia chi thuong tru"] = df["ten nguoi ptcm"].apply(
    lambda x: x.split(",", 1)[1].strip() if "," in x else ""
)

df["ten nguoi ptcm"] = df["ten nguoi ptcm"].str.split(",").str[0]

df["han"] = df["TGQD HỒ SƠ"].str.split(" - Hạn xử lý: ").str[1].str.split(" - ").str[0]

df["ngay tiep nhan"] = (
    df["TGQD HỒ SƠ"].str.split(" - <PERSON><PERSON><PERSON> tiế<PERSON> nhận: ").str[1].str.split(" - ").str[0]
)

df.rename(columns={"SỐ HỒ SƠ": "ma ho so"}, inplace=True)

df = df[
    [
        "ma ho so",
        "ma thu tuc",
        "ten nguoi ptcm",
        "dia chi thuong tru",
        "ngay tiep nhan",
        "han",
    ]
]

df["ma ho so"] = df["ma ho so"].str.strip()

df_thutuc = pd.read_csv("thu_tuc_duoc.csv")

update_with_vlookup(df, df_thutuc, "ma thu tuc", "TÊN TẮT")


df_name = pd.read_csv("name.csv")

update_with_vlookup(df_name, df, "ma ho so", "han")

col_to_update = [col for col in df if col != "ma ho so"]

update_multi_with_vlookup(df_name, df, "ma ho so", col_to_update)

df_to_add = df[~df["ma ho so"].isin(df_name["ma ho so"])]

# Thêm các hàng đã lọc vào df1
df_name = pd.concat([df_name, df_to_add], ignore_index=True)
from god_class import convert_and_sort_dates

df_name = convert_and_sort_dates(df_name, "han")
df_name.to_csv("name.csv", index=False)
