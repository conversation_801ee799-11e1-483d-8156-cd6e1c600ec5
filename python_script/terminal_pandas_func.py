import os
import sys

import pandas as pd
import unidecode

# Tắt cảnh báo SettingWithCopyWarning
pd.options.mode.chained_assignment = None  # default='warn'

os.chdir("/home/<USER>/Dropbox/hnd/csv_source")


# Đ<PERSON><PERSON> các tham số từ dòng lệnh
file_path = sys.argv[1]
column = sys.argv[2]
condition = sys.argv[3]


def filter_rows_by_condition(file_path, column, condition):
    # Đọc file Excel
    df = pd.read_csv(file_path, dtype=str)
    df.fillna("", inplace=True)
    df["check"] = df[column].str.upper().apply(unidecode.unidecode)
    # Lọc DataFrame dựa trên điều kiện trong cột Y
    filtered_df = df[
        df["check"].str.contains(unidecode.unidecode(condition.upper()), na=False)
    ]
    filtered_df.drop(columns=["check"], inplace=True)
    # In ra từng dòng của filtered_df dưới dạng dict
    for index, row in filtered_df.iterrows():
        print(row.to_dict())
        print("\n")


# G<PERSON>i hàm và in kết quả
filter_rows_by_condition(file_path, column, condition)
# filter_rows_by_condition('vanbandi.csv','trich_yeu','cấp điều chỉnh')