import os
import subprocess
from datetime import datetime

from loguru import logger

from god_class import PandasCsv
from top_most_get_text import input_dialog

os.chdir("/home/<USER>/Dropbox/hnd/csv_source")


class TimeConvert:
    def __init__(self, time):
        self.raw_time = time
        self.time = self.convert()

    def convert(self):
        return datetime.strptime(self.raw_time, "%d%m%Y")

    def format(self):
        return self.convert().strftime("%d/%m/%Y")

    def __str__(self):
        return self.time


def final(num, text):
    if num == "00" or num is None:
        text = ""
    return text


@logger.catch
def run_main():
    year_now = datetime.now().year
    month_now = datetime.now().month
    ngaybatdau = TimeConvert(
        input_dialog("NHẬP", f"NHẬP NGÀY BẮT ĐẦU:", f"0101{year_now}")
    ).convert()
    ngayketthuc = TimeConvert(
        input_dialog("NHẬP", f"NHẬP NGÀY KẾT THÚC:", f"31{month_now}{year_now}")
    ).convert()
    ngaycuoicung = ngayketthuc.strftime("%d/%m/%Y")
    thoigian = f"{month_now} THÁNG ĐẦU NĂM {year_now}"
    cchn_kcb_capmoi = 154
    pd_cchnd = PandasCsv("cchnd.csv").get_df_between_time(
        ngaybatdau, ngayketthuc, "ngay qd"
    )
    pd_cchnd.fillna("", "loai cap")
    pd_dkkd_cap = PandasCsv("dkkd.csv").get_df_between_time(
        ngaybatdau, ngayketthuc, "ngay qd"
    )
    pd_dkkd_th = PandasCsv("ds_da_cham_dut.csv").get_df_between_time(
        ngaybatdau, ngayketthuc, "ngay qd cham dut"
    )
    pd_dkkd_th.fillna("", "ly do")
    pd_dkkd_th.upper("TÊN CSKD")
    pd_dkkd_gdp = PandasCsv("gdp.csv").get_df_between_time(
        ngaybatdau, ngayketthuc, "ngay qd"
    )
    pd_hoithao = PandasCsv("hoi_thao_gioi_thieu_thuoc.csv").get_df_between_time(
        ngaybatdau, ngayketthuc, "ngay"
    )

    pd_luyke = PandasCsv(
        "luy_ke_hnd.csv",
    )
    pd_luyke.to_datetime("ngay", "%d/%m/%Y")
    ngay_tinh_lk = pd_luyke.df.loc[pd_luyke.df["ngay"] < ngaybatdau, "ngay"].max()
    ngay_tinh_lk = ngay_tinh_lk.strftime("%d/%m/%Y")
    print("ngày bắt đầu tính luỹ kế", ngay_tinh_lk)
    # CAP MOI
    cap_moi_do_qua_han = int(pd_cchnd.get_num_contains("loai cap", "CẬP NHẬT"))  # ok
    cap_mo_all = int(pd_cchnd.get_num_eq("loai cap", ""))  # ok
    cap_moi_all = str(cap_moi_do_qua_han + cap_mo_all).zfill(2)  # ok
    cap_dc_cchnd = pd_cchnd.get_num_contains("loai cap", "CHỈNH")  # ok
    cap_lai_cchnd = pd_cchnd.get_num_eq("loai cap", "(CẤP LẠI LẦN 1)")  # ok

    cap_lan_dau_gpp = pd_dkkd_cap.get_num_eq("thu tuc", "CAP GPP VA DKKD")  # ok
    tai_tham_dinh_gpp = pd_dkkd_cap.get_num_eq("thu tuc", "CẤP GPP")  # ok
    hau_kiem = tai_tham_dinh_gpp  # ok
    tai_tham_dinh_gdp = pd_dkkd_gdp.get_num_contains("loai cap", "LẠI")  # ok
    cap_lan_dau_gdp = pd_dkkd_gdp.get_num_contains("loai cap", "LẦN ĐẦU")  # ok
    # THU HOI

    cchnd_thu_hoi_do_caplaidc = str(int(cap_dc_cchnd) + int(cap_lai_cchnd)).zfill(
        2
    )  # ok
    cchnd_thu_hoi = str(
        int(cap_dc_cchnd) + int(cap_lai_cchnd) + cap_moi_do_qua_han
    ).zfill(
        2
    )  # ok
    dkkd_gpp_th = pd_dkkd_th.get_df_by_mask(
        ~pd_dkkd_th["TÊN CSKD"].str.contains("CÔNG TY")
    )  # ok
    dkkd_gdp_th = pd_dkkd_th.get_df_by_mask(
        pd_dkkd_th["TÊN CSKD"].str.contains("CÔNG TY")
    )  # ok

    gpp_xin_cham_dut = dkkd_gpp_th.get_num_contains("ly do", "XIN cham dut")  # ok
    gpp_td_khong_dat = dkkd_gpp_th.get_num_contains("ly do", "KHÔNG ĐÁP ỨNG")  # ok
    gpp_bi_thu_hoi = dkkd_gpp_th.get_num_contains("ly do", "BỊ THU HỒI")  # ok
    gpp_dung_hoat_dong = str(
        int(gpp_xin_cham_dut) + int(gpp_td_khong_dat) + int(gpp_bi_thu_hoi)
    ).zfill(2)
    gdp_dung_hoat_dong = dkkd_gdp_th.get_num_contains("ly do", "XIN cham dut")  # ok

    # LUYKE
    pd_luyke.format_datetime("ngay", "%d/%m/%Y")
    mask = pd_luyke["ngay"] == ngay_tinh_lk
    luy_ke_cchnd = int(pd_luyke.get_values_by_mask(mask, "CCHND")) + int(
        cap_mo_all
    )  # ok
    luy_ke_gdp = (
        int(pd_luyke.get_values_by_mask(mask, "DKKD-GDP"))
        + int(cap_lan_dau_gdp)
        - int(gdp_dung_hoat_dong)
    )  # ok
    luy_ke_gpp = (
        int(pd_luyke.get_values_by_mask(mask, "DKKD-GPP"))
        + int(cap_lan_dau_gpp)
        - int(gpp_dung_hoat_dong)
    )  # ok
    luyke_cchn_kcb = int(pd_luyke.get_values_by_mask(mask, "CCHN-KCB")) + int(
        cchn_kcb_capmoi
    )  # ok

    so_hoi_thao_gtt = str(pd_hoithao.df.shape[0]).zfill(2)  # ok
    pd_luyke.set_index("ngay")
    pd_luyke.df.loc[ngaycuoicung, ["CCHND", "DKKD-GPP", "DKKD-GDP", "CCHN-KCB"]] = [
        luy_ke_cchnd,
        luy_ke_gpp,
        luy_ke_gdp,
        luyke_cchn_kcb,
    ]  # ok
    pd_luyke.to_csv("luy_ke_hnd.csv", "ngay")  # ok

    tai_tham_dinh_gpp = final(
        tai_tham_dinh_gpp,
        rf"""; cấp giấy chứng nhận đạt Thực hành tốt cơ sở bán lẻ thuốc cho {tai_tham_dinh_gpp} cơ sở bán lẻ thuốc""",
    )

    cap_lan_dau_gdp = final(
        cap_lan_dau_gdp,
        rf"Cấp giấy chứng nhận đủ điều kiện kinh doanh dược cho {cap_lan_dau_gdp} cơ sở bán buôn thuốc",
    )

    thuhoi = final(
        gdp_dung_hoat_dong,
        rf"- Thu hồi Giấy chứng nhận đạt Thực hành tốt phân phối thuốc và giấy chứng nhận đủ điều kiện kinh doanh dược của {gdp_dung_hoat_dong} cơ sở bán buôn thuốc, lý do thu hồi: {gdp_dung_hoat_dong} cơ sở xin chấm dứt hoạt động kinh doanh.",
    )

    tai_tham_dinh_gdp = final(
        tai_tham_dinh_gdp,
        rf"; cấp giấy chứng nhận đạt Thực hành tốt phân phối thuốc cho {tai_tham_dinh_gdp} cơ sở bán buôn thuốc",
    )

    hau_kiem = final(hau_kiem, f"- Hậu kiểm {hau_kiem} cơ sở bán lẻ thuốc.")
    gpp_td_khong_dat = final(
        gpp_td_khong_dat,
        f"; {gpp_td_khong_dat} cơ sở bị thu hồi do không đạt điều kiện duy trì Thực hành tốt cơ sở bán lẻ thuốc",
    )
    pd_cbmp = PandasCsv("cap_so_tiep_nhan_mp.csv").get_df_between_time(
        ngaybatdau, ngayketthuc, "NGÀY CẤP SỐ TIẾP NHẬN"
    )
    my_pham = str(pd_cbmp.df.shape[0]).zfill(2)
    pd_qcmp = PandasCsv("xn_qc_mp.csv").get_df_between_time(
        ngaybatdau, ngayketthuc, "ngay"
    )
    qcmp = str(pd_qcmp.df.shape[0]).zfill(2)
    pd_ctmp = PandasCsv("ds_ct_mp.csv").get_df_between_time(
        ngaybatdau, ngayketthuc, "ngay"
    )
    ctmp = str(pd_ctmp.df.shape[0]).zfill(2)
    text = rf"""\documentclass{{article}}
    \nonstopmode
    \usepackage[T5]{{fontenc}}
    \usepackage[fontsize=14pt]{{scrextend}}
    \usepackage[a4paper,vmargin=2cm,right=2cm,left=3cm,]{{geometry}}
    \usepackage{{mathptmx}}
    \usepackage{{ulem}}
    \renewcommand{{\ULdepth}}{{7pt}}
    \renewcommand{{\ULthickness}}{{0.5pt}}
    \usepackage{{setspace}}
    \usepackage{{tabularx}}
    \usepackage{{tabularray}}
    \usepackage{{pdflscape}}
    \usepackage{{indentfirst}}
    \setlength{{\parindent}}{{1.27cm}}
    \thispagestyle{{empty}}
    \usepackage{{fancyhdr}}
    \fancyhf{{}}
    \fancyhead[C]{{\small\thepage}}
    \renewcommand{{\headrulewidth}}{{0pt}}
    \pagestyle{{fancy}}
    \begin{{document}}


    \begin{{center}}
        \textbf{{BÁO CÁO CÔNG TÁC HÀNH NGHỀ Y DƯỢC MỸ PHẨM}}

        \textbf{{ {thoigian}}}
    \end{{center}}

    \setstretch{{1.2}}

    \textbf{{I. Quản lý hành nghề Y: }}

    \textbf{{1. Chứng chỉ hành nghề khám bệnh, chữa bệnh }}

     - Số CCHN KB, CB cấp mới: {cchn_kcb_capmoi}
     
     - Số CCHN KB, CB lũy kế đến ngày {ngaycuoicung}: {luyke_cchn_kcb}
     
     \textbf{{2. GPHĐ KB, CB:}}
     
     - Số GPHĐ KB, CB cấp mới:
     
    - Cấp đổi địa điểm phòng khám:

    - Cấp bổ sung danh mục kỹ thuật phòng khám:

    - Thu hồi giấy phép hoạt động khám bệnh, chữa bệnh do chấm dứt hoạt
    động:

     \textbf{{II. Quản lý hành nghề dược:}}

    \textbf{{1. Chứng chỉ hành nghề dược:}}

    - Cấp chứng chỉ hành nghề dược cho {cap_moi_all} công dân; điều chỉnh nội dung trên chứng chỉ hành nghề dược cho {cap_dc_cchnd} công dân; cấp lại chứng chỉ hành nghề dược cho {cap_lai_cchnd} công dân.

    - Thu hồi {cchnd_thu_hoi_do_caplaidc} chứng chỉ hành nghề dược để cấp lại, cấp điều chỉnh; thu hồi {cap_moi_do_qua_han} chứng chỉ hành nghề dược do người hành nghề không cập nhật kiến thức chuyên môn dược theo quy định.

    - Lũy kế đến ngày {ngaycuoicung}, tổng số {luy_ke_cchnd} chứng chỉ hành nghề dược.

    \textbf{{2. Cơ sở kinh doanh dược:}}

    - Cấp Giấy chứng nhận đạt Thực hành tốt cơ sở bán lẻ thuốc và giấy chứng nhận đủ điều kiện kinh doanh dược cho {cap_lan_dau_gpp} cơ sở bán lẻ thuốc{tai_tham_dinh_gpp}.

    {hau_kiem}

    - Thu hồi Giấy chứng nhận đạt Thực hành tốt cơ sở bán lẻ thuốc và giấy chứng nhận đủ điều kiện kinh doanh dược của {gpp_dung_hoat_dong} cơ sở bán lẻ thuốc, lý do thu hồi: {gpp_xin_cham_dut} cơ sở xin chấm dứt hoạt động kinh doanh{gpp_td_khong_dat}.

    - {cap_lan_dau_gdp}{tai_tham_dinh_gdp}

    {thuhoi}

    - Lũy kế số cơ sở bán lẻ thuốc là {luy_ke_gpp} cơ sở, số cơ sở bán buôn thuốc là {luy_ke_gdp} cơ sở.

    \textbf{{3. Hội thảo giới thiệu thuốc: }}

    - Thẩm định Cấp giấy xác nhận nội dung thông tin thuốc theo hình thức hội thảo giới thiệu thuốc cho {so_hoi_thao_gtt} hội thảo giới thiệu thuốc.

    \textbf{{III. Quản lý mỹ phẩm:}}

    - Cấp Giấy tiếp nhận phiếu công bố sản phẩm mỹ phẩm cho {my_pham} sản phẩm mỹ phẩm.

    - Cấp Giấy chứng nhận đủ điều kiện sản xuất mỹ phẩm cho {ctmp} cơ sở sản xuất mỹ phẩm.

    - Cấp Giấy giấy xác nhận nội dung quảng cáo mỹ phẩm cho {qcmp} sản phẩm mỹ phẩm.

    - Luỹ kế số cơ sở sản xuất mỹ phẩm là ....cơ sở
    \end{{document}}

    """
    from god_class import compile_latex

    compile_latex(text, "bc-quy", "")


if __name__ == "__main__":
    run_main()
