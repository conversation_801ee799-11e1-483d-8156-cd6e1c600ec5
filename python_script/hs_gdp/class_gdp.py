import sys
import os

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import datetime
from datetime import timedelta

import pandas as pd

from god_class import dataframe_to_latex, TextProcess, get_current_date, yes_no


def remove_rows_with_string(df, column_name, string_to_remove):
    mask = ~df[column_name].str.contains(string_to_remove, na=False)
    return df[mask]


def get_so_luong_trang_bi(df, ten_trang_bi):
    # Lọc các tên theo chức vụ
    so_luong = df.loc[df["Tên trang thiết bị"].str.contains(ten_trang_bi), "Số lượng"]

    # Kiểm tra nếu có bất kỳ kết quả nào thì join chúng lại thành chuỗi
    if not so_luong.empty:
        return so_luong.values[0]
    else:
        return None


def get_ho_va_ten(df, chuc_vu):
    # <PERSON><PERSON><PERSON> các tên theo chức vụ
    nhan_vien = df.loc[df["CHỨC VỤ"].str.contains(chuc_vu), "HỌ VÀ TÊN"]

    # <PERSON><PERSON>m tra nếu có bất kỳ kết quả nào thì join chúng lại thành chuỗi
    if not nhan_vien.empty:
        return ", ".join(nhan_vien)
    else:
        return None


def get_date():
    now = datetime.datetime.now()
    day = str(now.day).zfill(2)
    month = now.month
    if month < 3:
        month = str(month).zfill(2)
    year = now.year
    to_day = now.strftime("%d/%m/%Y")
    ngay_1 = (now + timedelta(days=1)).strftime("%d/%m/%Y")
    ngay_2 = (now + timedelta(days=2)).strftime("%d/%m/%Y")
    ngay_3 = (now + timedelta(days=3)).strftime("%d/%m/%Y")
    ngay4 = (now + timedelta(days=4)).strftime("%d/%m/%Y")
    ngay_bc, thang_bc, nam_bc = ngay4.split("/")
    return day, month, year, to_day, ngay_1, ngay_2, ngay_3, ngay_bc, thang_bc, nam_bc
    # return (
    #     "15",
    #     "10",
    #     "2022",
    #     "15/10/2022",
    #     ngay_1,
    #     ngay_2,
    #     ngay_3,
    #     ngay_bc,
    #     thang_bc,
    #     nam_bc,
    # )


class HsGDP:
    def __init__(self):
        cong_ty = pd.read_csv(
            "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/excel_documents/cong_ty.csv",
            dtype=str,
        )
        self.cong_ty = cong_ty.iloc[0].to_dict()

        self.cong_ty["ngay"], self.cong_ty["thang"], self.cong_ty["nam"], _ = (
            get_current_date()
        )

        self.df_nhan_vien = pd.read_csv(
            "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/excel_documents/nhan_vien.csv",
            dtype=str,
        )
        self.df_trang_bi = pd.read_csv(
            "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/excel_documents/ttb.csv",
            dtype=str,
        )
        self.df_trang_bi.fillna("", inplace=True)

        self.sop = TextProcess("hs_gdp/form_sop_gdp")
        if yes_no("bao quan lanh", "Có bao quan lanh KHÔNG?"):
            self.cong_ty["nhietdo"] = r" bảo quản ở điều kiện thường, bảo quản lạnh"
            self.cong_ty["qt-lanh"] = """
30. Quy trình bảo quản thuốc trong tủ lạnh

31. Quy trình bảo dưỡng thiết bị tủ lạnh

32. Quy trình xử lý tình huống khi không đảm bảo điều kiện bảo quản

            """
            self.hs = TextProcess("hs_gdp/form_hs_gdp")
            self.cong_ty["list_dm_sop"] = r"""
\noindent
30. Quy trình bảo quản thuốc trong tủ lạnh \dotfill 123

\noindent
31. Quy trình bảo dưỡng thiết bị tủ lạnh \dotfill 126

\noindent
32. Quy trình xử lý tình huống khi không đảm bảo điều kiện bảo quản lạnh \dotfill 130
"""
            self.cong_ty["sop_lanh"] = ""
            self.cong_ty["sop_lanh"] = self.cong_ty["sop_lanh"].replace(
                "ngay", self.cong_ty["ngay"]
            )
        else:
            self.cong_ty["list_dm_sop"] = (
                ""  # khong co sop lanh trong danh sach dau tien
            )
            self.cong_ty["sop_lanh"] = (
                r"\end{document}"  # khong co sop lanh trong danh muc cuoi cung
            )
            self.cong_ty["nhietdo"] = " bảo quản ở điều kiện thường"
            self.hs = TextProcess("hs_gdp/form_hs_gdp")
        (
            self.cong_ty["ngay"],
            self.cong_ty["thang"],
            self.cong_ty["nam"],
            self.cong_ty["ngay ban hanh"],
            self.cong_ty["ngay 1"],
            self.cong_ty["ngay 2"],
            self.cong_ty["ngay 3"],
            self.cong_ty["ngay bc"],
            self.cong_ty["thang bc"],
            self.cong_ty["nam bc"],
        ) = get_date()
        self.cong_ty["ke toan"] = get_ho_va_ten(self.df_nhan_vien, "ke toan")
        if self.cong_ty["giam doc"] == self.cong_ty["ten nguoi ptcm"]:
            self.cong_ty["thu ky thanh tra"] = self.cong_ty["ke toan"]
        else:
            self.cong_ty["thu ky thanh tra"] = self.cong_ty["ten nguoi ptcm"]
        if self.cong_ty["tong giam doc"] != "":
            self.cong_ty["chuc danh"] = "TỔNG GIÁM ĐỐC"
        self.cong_ty["giam doc"] = get_ho_va_ten(self.df_nhan_vien, "giam doc")
        self.cong_ty["thu kho"] = get_ho_va_ten(self.df_nhan_vien, "thu kho")
        self.cong_ty["nhan vien kd"] = get_ho_va_ten(
            self.df_nhan_vien, "nhan vien kinh doanh"
        )
        self.cong_ty["nhan vien giao hang"] = get_ho_va_ten(
            self.df_nhan_vien, "nhan vien giao hang"
        )
        self.dao_tao = TextProcess("hs_gdp/form_dao_tao_gdp")
        self.mo_ta_cv = TextProcess("hs_gdp/form_mo_ta_cong_viec_gdp")
        self.quy_dinh_chung = TextProcess("hs_gdp/form_quy_dinh_chung_gdp")
        self.cscl = TextProcess("hs_gdp/form_cscl_gdp")
        self.bieu_mau_so_sach_gdp = TextProcess("hs_gdp/form_bieu_mau_so_gdp")
        self.quyet_dinh = TextProcess("hs_gdp/form_quyet_dinh_gdp")
        self.bia_dung = TextProcess("hs_gdp/form_bia_doc_gdp")
        self.bia_ngang = TextProcess("hs_gdp/form_bia_ngang_gdp")
        self.cong_ty["dsnhansu"] = dataframe_to_latex(self.df_nhan_vien)

        self.cong_ty["dsnhansu"] = (
            self.cong_ty["dsnhansu"]
            .replace("ten nguoi ptcm", "Người phụ trách chuyên môn dược")
            .replace("nhan vien kinh doanh", "Nhân viên kinh doanh")
            .replace("nhan vien giao hang", "Nhân viên giao hàng")
            .replace("ke toan", "Kế toán")
            .replace("thu kho", "Thủ kho")
            .replace("thu ky thanh tra", "Thủ ký thanh tra")
            .replace("giam doc", "Giám đốc")
        )
        self.cong_ty["dstrangthietbi"] = dataframe_to_latex(self.df_trang_bi)
        self.cong_ty["so nhiet ke tu ghi"] = get_so_luong_trang_bi(
            self.df_trang_bi, "Nhiệt kế tự ghi"
        )
        self.cong_ty["so dieu hoa"] = get_so_luong_trang_bi(
            self.df_trang_bi, "Điều hòa"
        )

    def format_sop(self):
        self.sop.format_text(self.cong_ty)
        self.sop.compile_latex()
        self.sop.copy_pdf_to_kq_and_send_hnd(
            "SOP {}".format(self.cong_ty["ten cong ty"])
        )
        self.sop.open_pdf_with_mupdf()

    def format_hs(self):
        self.hs.format_text(self.cong_ty)
        self.hs.compile_latex()
        self.hs.copy_pdf_to_kq_and_send_hnd("HS {}".format(self.cong_ty["ten cong ty"]))
        self.hs.open_pdf_with_mupdf()

    def format_dao_tao(self):
        dsdt_and_kq = self.df_nhan_vien[["HỌ VÀ TÊN", "CHỨC VỤ", "TRÌNH ĐỘ CHUYÊN MÔN"]]
        dsdt_and_kq = remove_rows_with_string(
            dsdt_and_kq, "CHỨC VỤ", "phụ trách chuyên môn"
        )
        dsdt_and_kq = dsdt_and_kq[dsdt_and_kq["CHỨC VỤ"] != "ten nguoi ptcm"]
        dsdt_and_kq.reset_index(inplace=True, drop=True)
        dsdt_and_kq.index += 1
        dsdt_and_kq["stt"] = dsdt_and_kq.index
        dsdt_and_kq["KẾT QUẢ"] = "Đạt"
        dsdt_and_kq["diem so"] = ""
        dsdt_and_kq = dsdt_and_kq[
            ["stt", "HỌ VÀ TÊN", "CHỨC VỤ", "TRÌNH ĐỘ CHUYÊN MÔN", "diem so", "KẾT QUẢ"]
        ]
        dsdt_and_kq["CHỨC VỤ"] = (
            dsdt_and_kq["CHỨC VỤ"]
            .str.replace("giam doc", "Giám đốc")
            .str.replace("ke toan", "Kế toán")
            .str.replace("thu kho", "Thủ kho")
            .str.replace("nhan vien kinh doanh", "Nhân viên kinh doanh")
            .str.replace("nhan vien giao hang", "Nhân viên giao hàng")
        )
        dsdt = dsdt_and_kq[["stt", "HỌ VÀ TÊN", "CHỨC VỤ", "TRÌNH ĐỘ CHUYÊN MÔN"]]

        self.cong_ty["ds_daotao"] = dataframe_to_latex(dsdt)
        self.cong_ty["dskq"] = dataframe_to_latex(dsdt_and_kq)
        self.dao_tao.format_text(self.cong_ty)
        self.dao_tao.compile_latex()
        self.dao_tao.copy_pdf_to_kq_and_send_hnd(
            "ĐÀO TẠO {}".format(self.cong_ty["ten cong ty"])
        )
        self.dao_tao.open_pdf_with_mupdf()

    def format_mo_ta_cv(self):
        self.mo_ta_cv.format_text(self.cong_ty)
        self.mo_ta_cv.compile_latex()
        self.mo_ta_cv.copy_pdf_to_kq_and_send_hnd(
            "MÔ TẢ CÔNG VIỆC {}".format(self.cong_ty["ten cong ty"])
        )
        self.mo_ta_cv.open_pdf_with_mupdf()

    def format_quy_dinh_chung(self):
        self.quy_dinh_chung.format_text(self.cong_ty)
        self.quy_dinh_chung.compile_latex()
        self.quy_dinh_chung.copy_pdf_to_kq_and_send_hnd(
            "QUY ĐỊNH CHUNG {}".format(self.cong_ty["ten cong ty"])
        )
        self.quy_dinh_chung.open_pdf_with_mupdf()

    def format_cscl(self):
        self.cscl.format_text(self.cong_ty)
        self.cscl.compile_latex()
        self.cscl.copy_pdf_to_kq_and_send_hnd(
            "CSCL {}".format(self.cong_ty["ten cong ty"])
        )
        self.cscl.open_pdf_with_mupdf()

    def format_bieu_mau_so_sach(self):
        self.bieu_mau_so_sach_gdp.format_text(self.cong_ty)
        self.bieu_mau_so_sach_gdp.compile_latex()
        self.bieu_mau_so_sach_gdp.copy_pdf_to_kq_and_send_hnd(
            "Biểu mẫu sổ sách {}".format(self.cong_ty["ten cong ty"])
        )
        self.bieu_mau_so_sach_gdp.open_pdf_with_mupdf()

    def format_quyet_dinh(self):
        self.quyet_dinh.format_text(self.cong_ty)
        self.quyet_dinh.compile_latex()
        self.quyet_dinh.copy_pdf_to_kq_and_send_hnd(
            "Quyết định {}".format(self.cong_ty["ten cong ty"])
        )
        self.quyet_dinh.open_pdf_with_mupdf()

    def format_bia_doc(self):
        self.bia_dung.format_text(self.cong_ty)
        self.bia_dung.compile_latex()
        self.bia_dung.copy_pdf_to_kq_and_send_hnd(
            "Bìa đứng {}".format(self.cong_ty["ten cong ty"])
        )
        self.bia_dung.open_pdf_with_mupdf()

    def format_bia_ngang(self):
        self.bia_ngang.format_text(self.cong_ty)
        self.bia_ngang.compile_latex()
        self.bia_ngang.copy_pdf_to_kq_and_send_hnd(
            "Bìa ngang {}".format(self.cong_ty["ten cong ty"])
        )
        self.bia_ngang.open_pdf_with_mupdf()


if __name__ == "__main__":
    hs_gdp = HsGDP()
    # hs_gdp.format_sop()
    hs_gdp.format_hs()
    # hs_gdp.format_dao_tao()
    # hs_gdp.format_mo_ta_cv()
    # hs_gdp.format_quy_dinh_chung()
    # hs_gdp.format_cscl()
    # hs_gdp.format_bieu_mau_so_sach()
    # hs_gdp.format_quyet_dinh()
    # hs_gdp.format_bia_doc()
    # hs_gdp.format_bia_ngang()
