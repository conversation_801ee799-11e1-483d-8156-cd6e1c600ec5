import os
import subprocess
import sys
from loguru import logger

from csv_load_and_export import CsvLoaderFactory
from all_pickle import load_pickle
from god_class import (
    get_dict_from_index_df,
    phone_format,
    send_notification,
    update_df_name_da_nhan,
    update_df_from_dict_by_index,
    close_app,
    setup_logging,
)
from top_most_get_text import input_dialog


from create_snippets import creat_and_get
from solid_string_format import DictStringFormatter
from hs_mission_solid import (
    CCHNDDataCollector,
    UpdatePvkd,
    check_dung_ten_and_trung_ten,
    check_and_get_ngay_cap_cchnd,
)

os.chdir("/home/<USER>/Dropbox/hnd/csv_source")


dt = CsvLoaderFactory.create_basic_loader()
df_dshn = dt.load_df("dshn_duoc", "so cchnd")
df_dkkd = dt.load_df("dkkd", "ma ho so")
df_name = dt.load_df("name", "ma ho so")
df_cchnd = dt.load_df("cchnd", "so cchnd")
df_lam_ho_so = dt.load_df("co_so_ban_le", "so cchnd")


class CustomCCHNDDataCollector(CCHNDDataCollector):
    def _create_dict_from_hnd(self, dict_hnd):
        return {
            "so dt chu hs": dict_hnd.get("so dt chu hs", ""),
            "ten qt-nt": dict_hnd.get("ten qt-nt", ""),
            "dia chi co so": dict_hnd.get("dia chi co so", ""),
            "pham vi kd": "default",
            "dieu kien bao quan": "0",
            "ten co so dang ky hn": dict_hnd.get("ten co so dang ky hn", ""),
            "co nhan vien khong": dict_hnd.get("co nhan vien khong", "0") or "0",
            "co quan chu quan": dict_hnd.get("co quan chu quan", ""),
            "dia chi co quan chu quan": dict_hnd.get("dia chi co quan chu quan", ""),
            "noi nhan": dict_hnd.get("noi nhan", ""),
            "van de hs": "",
            "Ma 4772": "",
            "Chủ hộ kd có phải ptcm không": "",
        }


# Sử dụng lớp tùy chỉnh
def collect_data(so_cchnd):
    # collector = CCHNDDataCollector(df_cchnd, df_dshn_duoc)  # Thay thế dòng này
    collector = CustomCCHNDDataCollector(df_cchnd, df_dshn)  # bằng dòng này
    return collector.collect_cchnd_data(so_cchnd)


@setup_logging("gpp_hs_cap_moi.log")
@logger.catch
def gpp_hs_moi(mhs, thutuc="CAP GPP VA DKKD"):
    # if mhs in df_dkkd.index:
    #     show_message("THÔNG BÁO", "Đã nhập thông tin hồ sơ")
    #     sys.exit()

    values = {}
    sdt = phone_format(df_name.loc[mhs, "so dt chu hs"])
    ten_nguoi_ptcm = df_name.loc[mhs, "ten nguoi ptcm"]
    dia_chi_thuong_tru = df_name.loc[mhs, "dia chi thuong tru"]
    cmnd = df_name.loc[mhs, "cmnd"]

    ngay_sinh = df_name.loc[mhs, "ngay sinh"]
    default_dict = {
        "ten qt-nt": "",
        "dia chi co so": "",
        "Ma 4772": "",
        "Chủ hộ kd có phải ptcm không": "",
        "gioi tinh": "0",
        "trinh do cm": "Cao đẳng dược",
        "vi tri hanh nghe": "Quầy thuốc",
        "noi cap cchnd": "Sở Y tế tỉnh Phú Thọ",
        "noi cap cmnd": "Cục Cảnh sát Quản lý hành chính về trật tự xã hội",
        "ngay cap cmnd": "",
        "co nhan vien khong": "0",
        "pham vi kd": "default",
        "dieu kien bao quan": "0",
        "co quan chu quan": "",
        "dia chi co quan chu quan": "",
        "noi tot nghiep": "",
        "ngay tot nghiep": "",
        "van de hs": "",
        "noi nhan": "HIẾU",
        "ten co so dang ky hn": "",
        "dia chi thuong tru": dia_chi_thuong_tru,
        "ten nguoi ptcm": ten_nguoi_ptcm,
        "ngay sinh": ngay_sinh,
        "cmnd": cmnd,
    }

    so_cchnd = input_dialog("Title", "NHẬP so cchnd", "")
    if not so_cchnd:
        sys.exit()

    list_fields_not_get = [
        "so dt chu hs",
        "dia chi thuong tru",
        "cmnd",
        "ngay sinh",
    ]
    if "_" not in ten_nguoi_ptcm:
        list_fields_not_get.append("ten nguoi ptcm")

    dict_get = get_dict_from_index_df(df_dshn, so_cchnd)
    # loại bỏ các trường không cần update vi da co san
    dict_get = {k: v for k, v in dict_get.items() if k not in list_fields_not_get}

    values["so cchnd"] = so_cchnd
    values["ten nguoi ptcm"] = ten_nguoi_ptcm
    values["dia chi thuong tru"] = dia_chi_thuong_tru
    values["cmnd"] = cmnd
    values["ngay sinh"] = ngay_sinh
    values = check_and_get_ngay_cap_cchnd(values)
    values["so dt chu hs"] = sdt

    if so_cchnd in df_lam_ho_so.index:
        values.update({k: v for k, v in dict_get.items() if k in default_dict and v})
        dict_additional = {
            k: "" for k, v in dict_get.items() if k in default_dict and not v
        }
        additional_fields = {  # Đổi tên biến để tránh xung đột
            "co nhan vien khong": "",
            "Mã 4772": "",
            "Chủ hộ kd có phải ptcm không": "",
            "van de hs": "",
        }
        if len(dict_additional) > 0:
            additional_fields.update(dict_additional)
        # Nếu pham vi kd không có bảo quản hoặc chưa cập nhật phạm vi kinh doanh thì thêm vào

        if "pham vi kd" not in values or "bảo quản" not in values["pham vi kd"]:
            additional_fields["pham vi kd"] = "default"
            additional_fields["dieu kien bao quan"] = "0"
        default_dict_local = additional_fields  # Sử dụng tên khác cho biến cục bộ
    else:
        default_dict_local, values_got = collect_data(so_cchnd)
        values.update(values_got)
        default_dict_local["so cchnd"] = so_cchnd
        default_dict_local["ten nguoi ptcm"] = ten_nguoi_ptcm
        default_dict_local["dia chi thuong tru"] = dia_chi_thuong_tru
        default_dict_local["cmnd"] = cmnd
        default_dict_local["ngay sinh"] = ngay_sinh
        default_dict_local["so dt chu hs"] = sdt
        list_to_add = ["ten qt-nt", "dia chi co so"]
        for item in list_to_add:
            if item not in default_dict_local:
                default_dict_local = {item: ""} | default_dict_local

    values_second = creat_and_get(default_dict_local)
    values.update(values_second)
    check_dung_ten_and_trung_ten(values)

    dict_to_update = {"ma ho so": mhs, "thu tuc": thutuc, "so cchnd": so_cchnd}
    values.update(dict_to_update)
    if "đại học" in values["trinh do cm"].lower():
        values["loai hinh"] = "Nhà thuốc"
        values["vi tri hanh nghe"] = "Nhà thuốc"
    else:
        values["loai hinh"] = "Quầy thuốc"
        values["vi tri hanh nghe"] = "Quầy thuốc"

    if "thuốc" not in values["ten qt-nt"].lower():
        values["ten qt-nt"] = values["loai hinh"] + " " + values["ten qt-nt"]

    values["trinh do tat"] = (
        values["trinh do cm"]
        .replace("Cao đẳng dược", "cao đẳng")
        .replace("Trung cấp dược", "trung cấp")
        .replace("Đại học dược", "đại học")
    )

    update_pvkd = UpdatePvkd(values)
    values = update_pvkd.update()

    list_ngay = ["ngay cap cchnd", "ngay tot nghiep", "ngay sinh", "ngay cap cmnd"]
    list_upper = ["ten nguoi ptcm", "ten qt-nt"]

    formatter = DictStringFormatter(values)
    values = formatter.apply_date_format(list_ngay).apply_upper(list_upper).get_result()

    update_df_name_da_nhan(df_name, values, mhs)
    update_df_from_dict_by_index(df_dkkd, "dkkd.csv", values, mhs)
    update_df_from_dict_by_index(df_dshn, "dshn_duoc.csv", values, so_cchnd)
    # Cập nhật ngày qd cho df_cchnd theo ngày cấp chuẩn với các cchnd cấp mà bị lệch ngày
    if so_cchnd in df_cchnd.index:
        values["ngay qd"] = values["ngay cap cchnd"]
        update_df_from_dict_by_index(df_cchnd, "cchnd.csv", values, so_cchnd)

    if "co quan chu quan" in values and len(values["co quan chu quan"]) > 2:
        send_notification(
            "XEM KỸ LẠI CHỮ KÝ VÀ CON DẤU TRONG ĐƠN ĐỀ NGHỊ, TRA CỨU pham vi kd CỦA DOANH NGHIỆP"
        )
    close_app("okular")
    subprocess.run("wmctrl -xa file_hs.file_hs", shell=True)
    send_notification("xong")


if __name__ == "__main__":
    path = load_pickle(
        "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pickle_file/hs_path.pk"
    )
    mhs, _, _ = path.split("/")[-1].split("--")
    gpp_hs_moi(mhs)
