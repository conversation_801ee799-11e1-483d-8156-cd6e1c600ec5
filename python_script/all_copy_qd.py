import os
import shutil
from glob import glob

from god_class import send_notification


def copy_qd_and_rename(source_dir, dest_dir):
    # Tìm file PDF mới nhất trong thư mục nguồn
    list_of_files = glob(os.path.join(source_dir, "*.pdf"))
    latest_file = max(list_of_files, key=os.path.getctime)

    # L<PERSON>y danh sách các file PDF có sẵn trong thư mục đích
    existing_files = glob(os.path.join(dest_dir, "*.pdf"))

    # Số lượng file PDF có sẵn trong thư mục đích

    for i, existing_file in enumerate(existing_files):
        # Lấy tên gốc của file đích
        tengoc = os.path.basename(existing_file).replace(".pdf", "")

        # Tạo tên mới theo quy tắc "qd-{tengoc}.pdf"
        new_file_name = f"qd-{tengoc}.pdf"
        new_file_path = os.path.join(dest_dir, new_file_name)

        # Copy file mới nhất từ thư mục nguồn đến thư mục đích và đổi tên
        shutil.copy2(latest_file, new_file_path)
        send_notification("ĐÃ COPY QUYẾT ĐỊNH")


# Ví dụ sử dụng
source_dir = "/home/<USER>/Dropbox"  # Thay bằng đường dẫn thực tế tới thư mục nguồn
dest_dir = "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pdf_documents/kq"  # Thay bằng đường dẫn thực tế tới thư mục đích

copy_qd_and_rename(source_dir, dest_dir)

