{"cells": [{"cell_type": "code", "execution_count": 14, "id": "5790fbdac198235b", "metadata": {"ExecuteTime": {"end_time": "2024-07-13T14:30:32.514484Z", "start_time": "2024-07-13T14:30:32.512977Z"}}, "outputs": [], "source": ["import pandas as pd\n", "import os\n", "os.chdir('/home/<USER>/Dropbox/hnd/csv_source')\n", "df = pd.read_csv('cap_so_tiep_nhan_mp.csv')"]}, {"cell_type": "code", "execution_count": 16, "id": "a0aaa553", "metadata": {}, "outputs": [], "source": ["\n", "df.reset_index(inplace=True)\n", "df['STT']=df.index+1\n", "df=df[['STT','ten san pham','SỐ TIẾP NHẬN','NGÀY CẤP SỐ TIẾP NHẬN']]\n", "df.to_latex('cap_so_tiep_nhan_mp_cece.tex',index=False)\n"]}], "metadata": {"kernelspec": {"display_name": "python313", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.1"}}, "nbformat": 4, "nbformat_minor": 5}