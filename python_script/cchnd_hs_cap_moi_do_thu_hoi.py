import os
import shutil


from god_class import convert_ngay, phone_format, send_notification, show_message
from all_pickle import load_pickle
from god_class import (
    get_dict_from_index_df,
    update_df_from_dict_by_index,
    update_df_name_da_<PERSON>han,
    close_app,
)
from python312 import run_python_312
from top_most_get_text import input_dialog
from csv_load_and_export import CsvLoaderFactory

# from create_snippets import creat_and_get
from create_snippets import creat_and_get
from solid_string_format import DictStringFormatter

dt = CsvLoaderFactory.create_basic_loader()

os.chdir("/home/<USER>/Dropbox/hnd/csv_source")


df_name = dt.load_df("name", "ma ho so")
df_cchnd = dt.load_df("cchnd", "ma ho so")
df_dshn = dt.load_df("dshn_duoc", "so cchnd")
df_gpp_all = dt.load_df("du_lieu_gpp_all", "so cchnd")
df_cchnd_thu_hoi = dt.load_df("dsthuhoicchnd", "so cchnd")


def check_cnkt_date(values):
    from datetime import datetime

    ngay_cap_cnkt = datetime.strptime(convert_ngay(values["ngay cap cnkt"]), "%d/%m/%Y")
    ngay_cchnd_cu = datetime.strptime(convert_ngay(values["ngay cchnd cu"]), "%d/%m/%Y")
    time_difference = ngay_cap_cnkt - ngay_cchnd_cu

    if time_difference.days < 3 * 365:  # Giả sử 1 năm có 365 ngày
        show_message("CẢNH BÁO", "Xem lại, có thể chưa bị thu hồi CCHND")
    else:
        send_notification("Xong")


def run_main(mhs):
    sdt = phone_format(df_name.loc[mhs, "so dt chu hs"])
    ngay_sinh = df_name.loc[mhs, "ngay sinh"]
    cmnd = df_name.loc[mhs, "cmnd"]
    dia_chi_thuong_tru = df_name.loc[mhs, "dia chi thuong tru"]
    dict_default_vals = {}
    if mhs in df_cchnd.index:
        so_cchnd_cu = df_cchnd.at[mhs, "so cchnd cu"]
        other_list = [
            "ten nguoi ptcm",
            "gioi tinh",
            "ngay sinh",
            "cmnd",
            "ngay cap cmnd",
            "noi cap cmnd",
            "dia chi thuong tru",
            "noi tot nghiep",
            "ngay tot nghiep",
            "noi cap cnkt",
            "ngay cap cnkt",
            "trinh do cm",
            "vi tri hanh nghe",
            "dang ky pham vi",
            "van de hs",
        ]
        dict_dshn = get_dict_from_index_df(df_cchnd, mhs)
        # Tạo danh sách keys cần xóa trước khi lặp
        for key in other_list:
            dict_default_vals[key] = dict_dshn[key]
        values = creat_and_get(dict_default_vals)
        values["ma ho so"] = mhs
        # TODO 1. cập nhật vào df_cchnd
        update_df_from_dict_by_index(df_cchnd, "cchnd.csv", values, mhs)
        # TODO 2. cập nhật vào df_name
        update_df_name_da_nhan(df_name, values, mhs)
        # TODO 3. chạy script tách nén
        run_python_312("tach_nen")
        # TODO 4. đóng app okular
        close_app("okular")

    else:  # TODO NẾU KO CÓ MHS TRONG CCHND.csv":        so_cchnd_cu = input_dialog("NHẬP", "NHẬP SỐ CCHN CŨ ĐÃ BỊ THU HỒI", "")
        so_cchnd_cu = input_dialog("Title", "NHẬP SỐ CCHN CŨ ĐÃ BỊ THU HỒI", "")
        if not so_cchnd_cu or so_cchnd_cu.strip() == "":
            return
        if so_cchnd_cu not in df_cchnd_thu_hoi.index:
            show_message("THÔNG BÁO", "CCHND chưa thu hồi")
        dict_thu_hoi = get_dict_from_index_df(df_cchnd_thu_hoi, so_cchnd_cu)
        list_include = [
            "ngay cap cnkt",
            "noi cap cnkt",
            "trinh do cm",
            "gioi tinh",
            "ten nguoi ptcm",
            "dia chi thuong tru",
            "noi cap cmnd",
            "ngay cap cmnd",
            "noi tot nghiep",
            "ngay tot nghiep",
        ]

        # TODO 2. lấy giá trị từ df_dshn
        dict_dshn = get_dict_from_index_df(df_dshn, so_cchnd_cu)
        dict_default_vals.update({key: dict_dshn.get(key, "") for key in list_include})
        dict_default_vals["dia chi thuong tru"] = dia_chi_thuong_tru
        dict_default_vals["ngay sinh"] = ngay_sinh
        dict_default_vals["cmnd"] = cmnd

        dict_default_vals["noi cap cnkt"] = "Trường cao đẳng Y Dược Phú Thọ"
        dict_default_vals["noi cap cmnd"] = (
            "Cục Cảnh sát Quản lý hành chính về trật tự xã hội"
        )
        dict_default_vals["van de hs"] = ""
        values = creat_and_get(dict_default_vals)

        values["ten nguoi ptcm"] = dict_thu_hoi["ten nguoi ptcm"]
        values["vi tri hanh nghe"] = dict_thu_hoi["vi tri hanh nghe"]
        values["pham vi hanh nghe cu"] = dict_thu_hoi["vi tri hanh nghe"]
        values["dang ky pham vi"] = values["vi tri hanh nghe"]
        values["cmnd"] = cmnd

        values["ngay cchnd cu"] = dict_thu_hoi["ngay cap cchnd"]

        list_upper = ["ten nguoi ptcm"]
        list_ngay = [
            "ngay sinh",
            "ngay cap cmnd",
            "ngay tot nghiep",
            "ngay cchnd cu",
            "ngay cap cnkt",
        ]
        # TODO 1. format values
        formatter = DictStringFormatter(values)
        values = (
            formatter.apply_upper(list_upper).apply_date_format(list_ngay).get_result()
        )
        values["trinh do tat"] = values["trinh do cm"].replace(" dược", "")
        values.update(
            {
                "ma ho so": mhs,
                "so cchnd cu": so_cchnd_cu,
                "ly do thu hoi": "Thu hồi để cấp lại do không cập nhật kiến thức chuyên môn",
                "thu tuc": "CAP CCHND SAU THU HOI",
                "da nhan": "1",
                "loai cap": r"(Cấp lại lần 1 sau khi thu hồi do thiếu cập nhật kiến thức chuyên môn dược)",
                "so dt chu hs": sdt,
            }
        )
        update_df_from_dict_by_index(df_cchnd, "cchnd.csv", values, mhs)
        update_df_name_da_nhan(df_name, values, mhs)
        check_cnkt_date(values)
        run_python_312("tach_nen")
        shutil.rmtree(path)


if __name__ == "__main__":
    path = load_pickle(
        "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pickle_file/hs_path.pk"
    )
    mhs, _, _ = path.split("/")[-1].split("--")
    run_main(mhs)
