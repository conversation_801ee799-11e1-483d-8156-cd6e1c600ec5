import os
from datetime import datetime, timedelta

import pandas as pd

from top_most_get_text import input_dialog
from god import compile_latex

os.chdir("/home/<USER>/Dropbox/hnd/csv_source")

current_date = datetime.now()
month = current_date.month
first_day_current_month = current_date.replace(day=1)
first_day_previous_month = (first_day_current_month - timedelta(days=1)).replace(day=1)
thoigianbatdau = first_day_previous_month.replace(day=16)
thoigianketthuc = first_day_current_month.replace(day=15)

ngaycuoicung = thoigianketthuc.strftime("%d/%m/%Y")

cchn_kcb_capmoi = input_dialog("NHẬP", "so cchnd KCB CẤP MỚI", "", 800, 500, 400, 400)


def get_data(filename, col):
    df = pd.read_csv(filename, dtype="str")
    # df=df[df['so qd'].notnull()]
    df[col] = pd.to_datetime(
        df[col], format="%d/%m/%Y", dayfirst=True, exact=True
    ).fillna(pd.Timestamp.min)
    mask = (df[col] > thoigianbatdau) & (df[col] < thoigianketthuc)
    return df[mask]


pd_cchnd = get_data("cchnd.csv", "ngay qd")
pd_cchnd_thuhoi = get_data("dsthuhoicchnd.csv", "ngay qd")
pd_dkkd_cap = get_data("dkkd.csv", "ngay qd")
pd_dkkd_th = get_data("ds_da_cham_dut.csv", "ngay qd cham dut")
pd_dkkd_gdp = get_data("gdp.csv", "ngay qd")
pd_hoithao = get_data(
    "hoi_thao_gioi_thieu_thuoc.csv",
    "ngay",
)
pd_cbmp = get_data("cap_so_tiep_nhan_mp.csv", "NGÀY CẤP SỐ TIẾP NHẬN")
tongsocchnd = str(pd_cchnd.shape[0]).zfill(2)
pd_cchnd["loai cap"].fillna("", inplace=True)


def get_num_contains(df, col, value):  # get number of rows that contain value in col
    df[col] = df[col].str.upper()
    return str(df[col].apply(lambda x: value in x).sum()).zfill(2)


def get_num_eq(df, col, value):  # get number of rows that equal value in col
    df[col] = df[col].str.upper()
    return str(df[col].apply(lambda x: x == value).sum()).zfill(2)


capmoitinh = int(get_num_eq(pd_cchnd, "loai cap", ""))
capmoi_dothuhoi = int(get_num_contains(pd_cchnd, "loai cap", "THU HỒI"))

capmoicchnd = str(capmoitinh + capmoi_dothuhoi).zfill(2)

capdccchnd = get_num_contains(pd_cchnd, "loai cap", "CHỈNH")

caplaicchnd = str(len(pd_cchnd) - int(capmoicchnd) - int(capdccchnd)).zfill(2)

cchndthuhoi = str(len(pd_cchnd_thuhoi)).zfill(2)
cchndthuhoi_bt = str(int(caplaicchnd) + int(capdccchnd)).zfill(2)
cchdthuhoi_tunguyen = get_num_contains(pd_cchnd_thuhoi, "ly do thu hoi", "TỰ NGUYỆN")
cchndthuhoi_quahan = get_num_contains(pd_cchnd_thuhoi, "ly do thu hoi", "QUÁ HẠN")
my_pham = str(len(pd_cbmp)).zfill(2)


def get_ngay_tinh_lk():  # get the last date in the excel file
    global pd_luyke
    pd_luyke = pd.read_csv("luy_ke_hnd.csv", index_col="ngay")
    pd_luyke.index = pd.to_datetime(pd_luyke.index, format="%d/%m/%Y")
    filtered_values = pd_luyke[pd_luyke.index < thoigianbatdau].index
    return filtered_values.max()


ngay_tinh_lk = get_ngay_tinh_lk()
print("ngày bắt đầu tính luỹ kế", ngay_tinh_lk)

caplandaugpp = get_num_eq(pd_dkkd_cap, "thu tuc", "CAP GPP VA DKKD")
taithamdinhgpp = get_num_eq(pd_dkkd_cap, "thu tuc", "CẤP LẠI GPP")
haukiem = taithamdinhgpp


def final(num, text):
    if num == "00" or num is None:
        text = ""
    return text


pd_dkkd_th["TÊN CSKD"] = pd_dkkd_th["TÊN CSKD"].str.upper()
dkkd_gpp_th = pd_dkkd_th[~pd_dkkd_th["TÊN CSKD"].str.contains("CÔNG TY")]


gppxinchamdut = get_num_contains(dkkd_gpp_th, "ly do", "XIN cham dut")
gppkhongdat = get_num_contains(dkkd_gpp_th, "ly do", "KHÔNG ĐÁP ỨNG")
tongsothuhoigpp = str(int(gppxinchamdut) + int(gppkhongdat)).zfill(2)
taithamdinhgdp = get_num_contains(pd_dkkd_gdp, "loai cap", "LẠI")
caplandaugdp1 = get_num_contains(pd_dkkd_gdp, "loai cap", "LẦN ĐẦU")

thuhoigdp = get_num_contains(pd_dkkd_th, "TÊN CSKD", "CÔNG TY")
luykecchnd = int(pd_luyke.loc[ngay_tinh_lk, "CCHND"]) + int(capmoicchnd)
luykegdp = (
    int(pd_luyke.loc[ngay_tinh_lk, "DKKD-GDP"]) + int(caplandaugdp1) - int(thuhoigdp)
)
luykegpp = (
    int(pd_luyke.loc[ngay_tinh_lk, "DKKD-GPP"])
    + int(caplandaugpp)
    - int(tongsothuhoigpp)
)
luyke_cchn_kcb = int(pd_luyke.loc[ngay_tinh_lk, "CCHN-KCB"]) + int(cchn_kcb_capmoi)

pd_hoithaogtt = str(pd_hoithao.shape[0]).zfill(2)
pd_luyke.loc[ngaycuoicung, ["CCHND", "DKKD-GPP", "DKKD-GDP", "CCHN-KCB"]] = [
    luykecchnd,
    luykegpp,
    luykegdp,
    luyke_cchn_kcb,
]
pd_luyke.index = pd_luyke.index.strftime("%d/%m/%Y")
pd_luyke.to_csv("luy_ke_hnd.csv", index_label="ngay")

taithamdinhgpp = final(
    taithamdinhgpp,
    rf"""; cấp định kỳ Giấy chứng nhận đạt Thực hành tốt cơ sở bán lẻ thuốc cho {taithamdinhgpp} cơ sở bán lẻ thuốc""",
)

caplandaugdp = final(
    caplandaugdp1,
    rf"Cấp lần đầu Giấy chứng nhận đạt Thực hành tốt phân phối thuốc và giấy chứng nhận đủ điều kiện kinh doanh dược cho {caplandaugdp1} cơ sở bán buôn thuốc",
)
thuhoi_gdp = final(
    thuhoigdp,
    rf"- Thu hồi Giấy chứng nhận đạt Thực hành tốt phân phối thuốc và giấy chứng nhận đủ điều kiện kinh doanh dược của {thuhoigdp} cơ sở bán buôn thuốc, lý do thu hồi: cơ sở xin chấm dứt hoạt động kinh doanh.",
)

taithamdinhgdp = final(
    taithamdinhgdp,
    f"; cấp định kỳ Giấy chứng nhận đạt Thực hành tốt phân phối thuốc cho {taithamdinhgdp} cơ sở bán buôn thuốc",
)
caplaicchnd = final(
    caplaicchnd, f"; cấp lại chứng chỉ hành nghề dược cho {caplaicchnd} công dân"
)
haukiem = final(haukiem, f"- Hậu kiểm {haukiem} cơ sở bán lẻ thuốc.")
gppkhongdat = final(
    gppkhongdat,
    f"; {gppkhongdat} cơ sở bị thu hồi do không đạt điều kiện duy trì Thực hành tốt cơ sở bán lẻ thuốc",
)
cchndthuhoi_bt = final(
    cchndthuhoi_bt,
    f" thu hồi {cchndthuhoi_bt} chứng chỉ hành nghề dược để cấp lại, cấp điều chỉnh",
)
cchndthuhoi_tunguyen = final(
    cchdthuhoi_tunguyen,
    f"; thu hồi {cchdthuhoi_tunguyen} chứng chỉ hành nghề dược do người hành nghề tự nguyện đề nghị thu hồi",
)
cchndthuhoi_quahan = final(
    cchndthuhoi_quahan,
    f"; thu hồi {cchndthuhoi_quahan} chứng chỉ hành nghề dược do người hành nghề không cập nhật kiến thức chuyên môn dược theo quy định",
)
text = rf"""\documentclass{{article}}
\nonstopmode
\usepackage[T5]{{fontenc}}
\usepackage[fontsize=14pt]{{scrextend}}
\usepackage[a4paper,vmargin=2cm,right=2cm,left=3cm,]{{geometry}}
\usepackage{{mathptmx}}
\usepackage{{ulem}}
\renewcommand{{\ULdepth}}{{7pt}}
\renewcommand{{\ULthickness}}{{0.5pt}}
\usepackage{{setspace}}
\usepackage{{tabularx}}
\usepackage{{tabularray}}
\usepackage{{pdflscape}}
\usepackage{{indentfirst}}
\setlength{{\parindent}}{{1.27cm}}
\thispagestyle{{empty}}
\usepackage{{fancyhdr}}
\fancyhf{{}}
\fancyhead[C]{{\small\thepage}}
\renewcommand{{\headrulewidth}}{{0pt}}
\pagestyle{{fancy}}
\begin{{document}}


\begin{{center}}
    \textbf{{BÁO CÁO CÔNG TÁC HÀNH NGHỀ Y DƯỢC}}

    \textbf{{thang {month} nam {current_date.year}}}
\end{{center}}

\setstretch{{1.2}}

\textbf{{I. Quản lý hành nghề Y: }}

\textbf{{1. Chứng chỉ hành nghề khám bệnh, chữa bệnh }}

 - Số CCHN KB, CB cấp mới: {cchn_kcb_capmoi}
 
 - Số CCHN KB, CB lũy kế đến ngày {ngaycuoicung}: {luyke_cchn_kcb}
 
 \textbf{{2. GPHĐ KB, CB:}}
 
 - Số GPHĐ KB, CB cấp mới:
 
- Cấp đổi địa điểm phòng khám:

- Cấp bổ sung danh mục kỹ thuật phòng khám:

- Thu hồi giấy phép hoạt động khám bệnh, chữa bệnh do chấm dứt hoạt
động:

 \textbf{{II. Quản lý hành nghề dược:}}

\textbf{{1. Kết quả thẩm định hồ sơ cấp chứng chỉ hành nghề dược:}}

- Thẩm định hồ sơ cấp chứng chỉ hành nghề dược cho {tongsocchnd} công dân, trong đó cấp mới chứng chỉ hành nghề dược cho {capmoicchnd} công dân, điều chỉnh nội dung trên chứng chỉ hành nghề dược cho {capdccchnd} công dân.

- Thu hồi {cchndthuhoi} chứng chỉ hành nghề dược, trong đó {cchndthuhoi_bt}{cchndthuhoi_quahan}{cchndthuhoi_tunguyen}.

- Lũy kế đến ngày {ngaycuoicung}, tổng số {luykecchnd} chứng chỉ hành nghề dược.

\textbf{{2. Kết quả thẩm định hồ sơ của các cơ sơ kinh doanh dược:}}

- Cấp lần đầu Giấy chứng nhận đạt Thực hành tốt cơ sở bán lẻ thuốc và giấy chứng nhận đủ điều kiện kinh doanh dược cho {caplandaugpp} cơ sở bán lẻ thuốc{taithamdinhgpp}.

{haukiem}

- Thu hồi Giấy chứng nhận đạt Thực hành tốt cơ sở bán lẻ thuốc và giấy chứng nhận đủ điều kiện kinh doanh dược của {tongsothuhoigpp} cơ sở bán lẻ thuốc, lý do thu hồi: {gppxinchamdut} cơ sở xin chấm dứt hoạt động kinh doanh{gppkhongdat}.

- {caplandaugdp}{taithamdinhgdp}

{thuhoi_gdp}

- Lũy kế số cơ sở bán lẻ thuốc là {luykegpp} cơ sở, số cơ sở bán buôn thuốc là {luykegdp} cơ sở.

\textbf{{3. Kết quả thẩm định hồ sơ Hội thảo giới thiệu thuốc: }}

- Thẩm định Cấp giấy xác nhận nội dung thông tin thuốc theo hình thức hội thảo giới thiệu thuốc cho {pd_hoithaogtt} Hội thảo giới thiệu thuốc.


\textbf{{4. Kết quả thẩm định hồ sơ mỹ phẩm:}}

- Cấp Giấy tiếp nhận phiếu công bố sản phẩm mỹ phẩm cho {my_pham} sản phẩm mỹ phẩm.

\end{{document}}
"""
compile_latex(text, "bao cao thang")
