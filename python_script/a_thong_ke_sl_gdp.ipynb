{"cells": [{"cell_type": "code", "execution_count": null, "id": "9e8e8122782dad35", "metadata": {}, "outputs": [], "source": ["import pandas as pd "]}, {"cell_type": "code", "execution_count": null, "id": "cf0a155d", "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "\n", "os.chdir('/home/<USER>/Dropbox/hnd/python_script')"]}, {"cell_type": "code", "execution_count": null, "id": "484fe673", "metadata": {}, "outputs": [], "source": ["df_dkkd=pd.read_csv('/home/<USER>/Dropbox/hnd/csv_source/gdp.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "c4310a55", "metadata": {}, "outputs": [], "source": ["\n", "df_dkkd['ngay qd']=pd.to_datetime(df_dkkd['ngay qd'])"]}, {"cell_type": "code", "execution_count": null, "id": "5747f3f9", "metadata": {}, "outputs": [], "source": ["df_dkkd['ngay qd'].fillna(df_dkkd['ngay qd'].min(),inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "f8fa5806", "metadata": {}, "outputs": [], "source": ["df_dkkd=df_dkkd[df_dkkd['ngay qd']<=pd.to_datetime('2024-08-31')]"]}, {"cell_type": "code", "execution_count": null, "id": "018d38f7", "metadata": {}, "outputs": [], "source": ["df_dkkd['huyen']=df_dkkd['tru so'].str.split(',').str.get(-2)"]}, {"cell_type": "code", "execution_count": null, "id": "6d0a6687", "metadata": {}, "outputs": [], "source": ["df_dkkd['huyen'].unique()"]}, {"cell_type": "code", "execution_count": null, "id": "446462ec", "metadata": {}, "outputs": [], "source": ["df_dkkd['ten cong ty']=df_dkkd['ten cong ty'].str.upper()"]}, {"cell_type": "code", "execution_count": null, "id": "0d7b4535", "metadata": {}, "outputs": [], "source": ["df_dkkd = df_dkkd.sort_values('ngay qd', ascending=False).drop_duplicates(subset='ten cong ty', keep='first')"]}, {"cell_type": "code", "execution_count": null, "id": "8ce289ae", "metadata": {}, "outputs": [], "source": ["df_dkkd.shape[0]"]}, {"cell_type": "code", "execution_count": null, "id": "60a2cd6b", "metadata": {}, "outputs": [], "source": ["df_dkkd"]}, {"cell_type": "code", "execution_count": null, "id": "59b51cae", "metadata": {}, "outputs": [], "source": ["pivot_table = pd.pivot_table(df_dkkd, values='ten cong ty', index='huyen', columns='loai hinh', aggfunc='count', fill_value=0)\n", "\n", "# <PERSON><PERSON><PERSON> thị pivot table\n", "print(pivot_table)"]}, {"cell_type": "code", "execution_count": null, "id": "be1cd088", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "/home/<USER>/python313/bin/python", "name": "/home/<USER>/python313/bin/python"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "i/home/<USER>/python313/bin/python", "version": "3.12.6"}}, "nbformat": 4, "nbformat_minor": 5}