import os
from loguru import logger
from all_pickle import load_pickle
import pandas as pd

from solid_string_format import DictStringFormatter
from god_class import (
    update_df_from_dict_by_index,
    update_df_name_da_<PERSON>han,
)
from create_snippets import creat_and_get
from python312 import run_python_312
from top_most_get_text import input_dialog


# TODO ĐẦU TIÊN PHẢI NHẬP ĐƯỢC noi tot nghiep.
@logger.catch
def cchnd_caplai(mhs):
    os.chdir("/home/<USER>/Dropbox/hnd/csv_source")
    df_cchnd = pd.read_csv("cchnd.csv", dtype=str, index_col="ma ho so")
    df_name = pd.read_csv("name.csv", dtype=str, index_col="ma ho so")

    ten_nguoi_ptcm = df_name.loc[mhs, "ten nguoi ptcm"]
    ngay_sinh = df_name.loc[mhs, "ngay sinh"]
    dia_chi_thuong_tru = df_name.loc[mhs, "dia chi thuong tru"]
    so_dt_chu_hs = df_name.loc[mhs, "so dt chu hs"]
    cmnd = df_name.loc[mhs, "cmnd"]

    so_cchnd_cu = input_dialog("Title", "NHẬP SỐ CCHN CŨ", "")
    defaul_val = {
        "gioi tinh": "0",
        "noi cap cmnd": "Cục Cảnh sát Quản lý hành chính về trật tự xã hội",
        "ngay cap cmnd": "",
        "ngay cchnd cu": "",
        "noi tot nghiep": "",
        "ngay tot nghiep": "",
        "dang ky pham vi": "",
        "trinh do cm": "",
        "LẦN": "1",
        "cmnd": cmnd,
        "so dt chu hs": so_dt_chu_hs,
        "ten nguoi ptcm": ten_nguoi_ptcm,
        "ngay sinh": ngay_sinh,
        "dia chi thuong tru": dia_chi_thuong_tru,
        "van de hs": "",
    }

    if not so_cchnd_cu:
        return
    values = creat_and_get(defaul_val)
    list_upper = ["ten nguoi ptcm"]
    list_ngay = ["ngay sinh", "ngay cap cmnd", "ngay cchnd cu", "ngay tot nghiep"]
    list_phone = ["so dt chu hs"]
    formatter = DictStringFormatter(values)
    values = (
        formatter.apply_upper(list_upper)
        .apply_date_format(list_ngay)
        .apply_phone_format(list_phone)
        .get_result()
    )

    values["so cchnd"] = "" if "VP-CCHND" in so_cchnd_cu else so_cchnd_cu
    values["trinh do cm"] = (
        values["trinh do cm"]
        .replace("đại học", "Đại học dược")
        .replace("cao đẳng", "Cao đẳng dược")
        .replace("trung cấp", "Trung cấp dược")
    )
    values["trinh do tat"] = (
        values["trinh do cm"]
        .replace("Đại học dược", "đại học")
        .replace("Cao đẳng dược", "cao đẳng")
        .replace("Trung cấp dược", "trung cấp")
    )
    values["ma ho so"] = mhs
    values["so cchnd cu"] = so_cchnd_cu
    values["pham vi hanh nghe cu"] = values["dang ky pham vi"]
    values["vi tri hanh nghe"] = values["dang ky pham vi"]
    values["ly do thu hoi"] = "Thu hồi để cấp lại"
    values["thu tuc"] = "CẤP LẠI CCHND"
    values["da nhan"] = "1"
    values["loai cap"] = f"(Cấp lại lần {values['LẦN']})"
    update_df_name_da_nhan(df_name, values, mhs)
    update_df_from_dict_by_index(df_cchnd, "cchnd.csv", values, mhs)
    run_python_312("tach_nen")


if __name__ == "__main__":
    path = load_pickle(
        "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pickle_file/hs_path.pk"
    )
    mhs, _, _ = path.split("/")[-1].split("--")
    cchnd_caplai(mhs)
