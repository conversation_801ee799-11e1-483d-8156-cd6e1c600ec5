import datetime as dt
import os
import shutil
from god_class import PandasCsv, compile_latex
import pandas as pd

from top_most_run_script import run_script_dialog

from PyQt5.QtWidgets import QApplication

os.chdir("/home/<USER>/Dropbox/hnd/csv_source")
df_name = PandasCsv("name.csv", "ma ho so")
df_dkkd = PandasCsv("dkkd.csv")

app = QApplication([])
dsbv = [
    "Bệnh viện y dược cổ truyền tỉnh Vĩnh Phúc",
    "Trung tâm Y tế huyện Tam Đảo",
    "Trung tâm Y tế huyện Tam Dương",
]
so_qd = "2354"
mask = df_dkkd["so qd"] == so_qd

df_dsqd = df_dkkd[df_dkkd["so qd"] == so_qd]
ngayqd = df_dsqd["ngay qd"].unique()[0]
ngay, thang, nam = ngayqd.split("/")
df_gpp_all = PandasCsv("du_lieu_gpp_all.csv")
df_gpp_tail = df_gpp_all.get_tail_df(100)
if so_qd not in df_gpp_tail.df["so qd"].tolist():
    df_gpp_all.get_append_df(df_dsqd)
    df_gpp_all.to_csv("du_lieu_gpp_all.csv")
df_dkkd["sort"] = pd.to_datetime(
    df_dkkd["ngay qd"], format="%d/%m/%Y", dayfirst=True, exact=True
)
df_dkkd.df.loc[df_dkkd["so qd"] == "", "sort"] = dt.datetime(2099, 12, 31)
df_dkkd.df.sort_values("sort", ascending=True, inplace=True)
df_dkkd.df.drop("sort", axis="columns", inplace=True)
df_dkkd.to_csv("dkkd.csv", index=False)
df_dsqd["so dkkd"] = df_dsqd["so dkkd"] + "/GPP"


def save_to_csv_and_excel(df, csv_filename, excel_filename):
    # Lưu DataFrame dưới dạng file CSV
    df.to_csv(csv_filename, index=False, encoding="utf-8")

    # Đọc file CSV vừa lưu
    new_df = pd.read_csv(csv_filename)

    # Lưu DataFrame dưới dạng file Excel
    new_df.to_csv(excel_filename, index=False)


# %%
save_to_csv_and_excel(df_dsqd, "dsqd.csv", "dsqd.csv")
for index, row in df_dsqd.iterrows():
    pvkdqt = r"Mua và bán lẻ thuốc thuộc Danh mục thuốc thiết yếu và Danh mục thuốc không kê đơn, trừ vắc xin (có bao gồm: thuốc dạng phối hợp có chứa dược chất gây nghiện; thuốc dạng phối hợp có chứa dược chất hướng thần; thuốc dạng phối hợp có chứa tiền chất; thuốc độc; thuốc trong danh mục thuốc, dược chất thuộc danh mục chất bị cấm sử dụng trong một số ngành, lĩnh vực); "
    pvkdnt = r"Mua thuốc để bán lẻ (có bao gồm: thuốc dạng phối hợp có chứa dược chất gây nghiện; thuốc dạng phối hợp có chứa dược chất hướng thần; thuốc dạng phối hợp có chứa tiền chất; thuốc độc; thuốc trong danh mục thuốc, dược chất thuộc danh mục chất bị cấm sử dụng trong một số ngành, lĩnh vực), trừ vắc xin; "
    pvkdtt = "Mua và bán lẻ thuốc thuộc Danh mục thuốc thiết yếu (có bao gồm: thuốc dạng phối hợp có chứa dược chất gây nghiện; thuốc dạng phối hợp có chứa dược chất hướng thần; thuốc dạng phối hợp có chứa tiền chất; thuốc độc; thuốc trong danh mục thuốc, dược chất thuộc danh mục chất bị cấm sử dụng trong một số ngành, lĩnh vực), bảo quản ở điều kiện thường, bảo quản lạnh."
    bao_quan = row["bao quan lanh"]
    loai_hinh = row["loai hinh"]
    if "TỦ THUỐC" in row["ten qt-nt"]:
        phamvikd = pvkdtt
        if "THỊ TRẤN" in row["ten qt-nt"]:
            loai_hinh_bv = "Tủ thuốc trạm y tế thị trấn"
        elif "XÃ" in row["ten qt-nt"]:
            loai_hinh_bv = "Tủ thuốc trạm y tế xã"
        else:
            loai_hinh_bv = "Tủ thuốc trạm y tế phường"
    else:
        if loai_hinh == "Quầy thuốc":
            phamvikd = pvkdqt
            loai_hinh_bv = "Quầy thuốc bệnh viện"
        else:
            phamvikd = pvkdnt
            loai_hinh_bv = "Nhà thuốc bệnh viện"

        if bao_quan == "1":
            phamvikd += "bảo quản ở điều kiện thường, bảo quản lạnh."
        elif bao_quan == "2":
            phamvikd += "bảo quản ở điều kiện thường, bảo quản mát, bảo quản lạnh."
        else:
            phamvikd += "bảo quản ở điều kiện thường."

    diachi1 = row["dia chi co so"]
    if 64 < len(diachi1) < 78:
        if len(diachi1) > 70:
            diadiem = rf"\textls[-50]{{{diachi1}}}"
        else:
            diadiem = rf"\textls[-20c]{{{diachi1}}}"
    else:
        diadiem = diachi1

    text = rf"""\documentclass[a4paper]{{article}}
                   \usepackage[T5]{{fontenc}}
                   \nonstopmode
                   \usepackage{{amsmath}}
                   \usepackage{{graphicx}}
                   \usepackage[margin=2cm]{{geometry}}
                   \usepackage[fontsize=14pt]{{scrextend}}
                   \usepackage{{times}}
                   \usepackage[onehalfspacing]{{setspace}}
                   \usepackage{{eso-pic}}
                   \usepackage{{microtype}}
                   \usepackage{{parskip}}
                   \setlength{{\parskip}}{{6pt}}
                   \usepackage{{tabularx}}
                   \usepackage{{tabularray}}
                   \usepackage{{ulem}}
                   \renewcommand{{\ULdepth}}{{7pt}}
                    \renewcommand{{\ULthickness}}{{0.5pt}}
                   \setlength{{\parindent}}{{0pt}}
                   \usepackage{{transparent}}
                   \renewcommand{{\baselinestretch}}{{1.2}}
    \begin{{document}}

    \pagestyle{{empty}}
    \AddToShipoutPictureBG{{\raisebox{{-1mm}}{{\includegraphics[width=\paperwidth, height=\paperheight]{{gfx/gpp4}}}}}}

    \newcolumntype{{b}}{{>{{\centering\arraybackslash}}X}}
    \newcolumntype{{s}}{{>{{\centering\arraybackslash\hsize=0.6\hsize}}X}}

    \begin{{tabularx}}{{1.1\textwidth}}{{sb}}
       \fontsize{{13pt}}{{0pt}}\selectfont UBND TỈNH VĨNH PHÚC                    & \textbf{{\fontsize{{13pt}}{{0pt}}\selectfont CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM}}  \\
       \textbf{{\fontsize{{13pt}}{{0pt}}\selectfont SỞ Y TẾ}}

       \rule[0.8\baselineskip]{{.17\linewidth}}{{.5pt}}                           & \uline{{\textbf{{\fontsize{{14pt}}{{0pt}}\selectfont Độc lập - Tự do - Hạnh phúc}}}} \\[-6pt]
       \fontsize{{14pt}}{{0pt}}\selectfont Số: {{\color{{red}} \bfseries {row["so gpp"]}}} /GPP &                                                                              \\
    \end{{tabularx}}

    \vspace{{0.5cm}}



    \begin{{center}}
       {{\color{{red}}\textbf{{GIẤY CHỨNG NHẬN ĐẠT THỰC HÀNH TỐT CƠ SỞ BÁN LẺ THUỐC\\
             GOOD PHARMACY PRACTICES (GPP)}}}}

       \vspace{{0.5cm}}

       {{\color{{red}} \fontsize{{16pt}}{{0pt}}\selectfont \textbf{{GIÁM ĐỐC SỞ Y TẾ CHỨNG NHẬN}}}}

    \end{{center}}

    \hspace{{2.1cm}}
    \makebox[0pt][l]{{%
       \raisebox{{-\totalheight}}[0pt][0pt]{{%
          \transparent{{0.1}}\includegraphics[width=5in]{{logo_syt.png}}}}}}


Tên cơ sở: {{\color{{blue}} \bfseries {row["ten qt-nt"]}\par}}

    Loại hình cơ sở kinh doanh: {{\color{{blue}}\textbf{{{loai_hinh_bv}}}}}

    Đạt {{\color{{blue}}\textbf{{“Thực hành tốt cơ sở bán lẻ thuốc” (GPP)}}}} đối với {{\color{{blue}}\textbf{{{row["loai hinh"]}}}}}.

    Tại địa chỉ: {diadiem}.

    Người phụ trách chuyên môn: Dược sĩ {row["trinh do tat"]} {{\color{{blue}}\textbf{{{row["ten nguoi ptcm"].title()}.}}}}

    Phạm vi: {{\color{{blue}}\textbf{{{phamvikd}}}}}

    Giấy chứng nhận này có giá trị 03 năm kể từ ngày ký, được cấp theo Quyết định số {row["so qd"]}/QĐ-SYT ngày {row["ngay qd"]} của Giám đốc Sở Y tế tỉnh Phú Thọ./.


    \hfill\begin{{minipage}}{{0.5\textwidth}}\singlespacing
       \begin{{center}}
          \textit{{Phú Thọ, ngày {ngay} tháng {thang} năm {nam}}}\\
\textbf{{KT. GIÁM ĐỐC}}\\
\textbf{{PHÓ GIÁM ĐỐC}}\\

\vspace{{3cm}}
\textbf{{Nguyễn Đắc Ca}}\\
\end{{center}}
    \end{{minipage}}

    \newpage

    \null\vspace{{\stretch{{1}}}}
    \begin{{center}}
       {{\color{{teal}} \bfseries  \LARGE GIẤY CHỨNG NHẬN ĐẠT\\THỰC HÀNH TỐT CƠ SỞ BÁN LẺ THUỐC}}
    \end{{center}}
    \vspace{{\stretch{{1}}}}\null
    \end{{document}}"""
    compile_latex(text, f"{row['ma ho so']}-{row['ten qt-nt']}", 0)
    shutil.copy("mylatex.tex", f"{row['ma ho so']}-{row['ten qt-nt']}.tex")
    df_name.df.loc[row["ma ho so"], "CHỜ TRẢ"] = 1
    df_name.to_csv("name.csv", "ma ho so")
# run_script_dialog(
#     "CẤP TK KN",
#     "Có cấp tài khoản kết nối không?",
#     r"D:\Dropbox\pycharm\gpp_cap tk kn.py",
# )
