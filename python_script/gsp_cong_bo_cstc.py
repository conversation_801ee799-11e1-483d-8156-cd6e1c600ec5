import os

from solid_string_format import DictStringFormatter
from csv_load_and_export import CsvLoaderFactory
from top_most_get_text import input_dialog
from create_snippets import creat_and_get
from god_class import (
    # auto_text_to_ioffice_pd,
    get_current_date,
    # auto_day_vb_phucdap,
    TextProcess,
)

os.chdir("/home/<USER>/Dropbox/hnd/csv_source")

path = "gsp_tiem_chung"
index_val = input_dialog("Title", "NHẬP SỐ GPHĐ (TẮT)", "")
indexs = "so gphd"
df = CsvLoaderFactory.create_fillna_loader().load_df(path, indexs)
list_ngay = ["ngay td", "ngay gphd"]
list_list_phone = ["so dt"]
list_title = ["ten nguoi ptcm", "npt bao quan vac xin"]
include = [col for col in df.columns.tolist() if col not in ["so gphd", "ngay tb syt"]]
values = dict.fromkeys(include, "")
values = creat_and_get(values)
formatter = DictStringFormatter(values)

values = formatter.apply_date_format(list_ngay).apply_title(list_title).get_result()

values["ngay"], values["thang"], values["nam"], _ = get_current_date()
values["ngay tb syt"] = f"{values['ngay']}/{values['thang']}/{values['nam']}"
values["so gphd"] = index_val
text = TextProcess("form_cong_bo_gsp")
text.format_text(values)
text.copy_latex_file(
    "/home/<USER>/Dropbox/hnd/latexall/vb_cho_in/vb_cong_bo_gsp.tex",
)
name = f"THÔNG BÁO Vv đăng tải thông tin của cơ sở tiêm chủng tự công bố đáp ứng Thực hành tốt bảo quản thuốc: {values['ten phong tiem chung']}"
text.auto_day_van_ban_pd(name, values["so tb"], "TB")
