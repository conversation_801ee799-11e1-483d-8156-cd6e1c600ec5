#!/usr/bin/env python3
"""
Script để tạo file espanso.yml từ CSV file.

Module này đọc dữ liệu từ file CSV có 2 cột (xa_cu và full)
và tạo file espanso configuration với format YAML.
"""

import csv
import yaml
from typing import List, Dict
from unidecode import unidecode


def read_csv_to_espanso_data(csv_file_path: str) -> List[Dict[str, str]]:
    """
    Đọc file CSV và chuyển đổi thành dữ liệu espanso format.

    Trigger sẽ được chuyển đổi sang dạng unidecode và lowercase
    để dễ dàng gõ trên bàn phím tiếng Anh.

    Args:
        csv_file_path: Đường dẫn đến file CSV

    Returns:
        List các dictionary chứa trigger (unidecode + lowercase) và replace

    Raises:
        FileNotFoundError: Nếu file CSV không tồn tại
        ValueError: Nếu CSV không có đúng cấu trúc
    """
    espanso_data = []

    try:
        with open(csv_file_path, "r", encoding="utf-8") as csvfile:
            reader = csv.DictReader(csvfile)

            # Kiểm tra các cột cần thiết
            if "xa cu" not in reader.fieldnames or "full" not in reader.fieldnames:
                raise ValueError("CSV phải có cột 'xa cu' và 'full'")

            for row in reader:
                xa_cu = row["xa cu"].strip()
                full = row["full"].strip()

                # Bỏ qua các dòng trống
                if xa_cu and full:
                    # Chuyển đổi trigger sang dạng unidecode và lowercase
                    trigger = unidecode(xa_cu).lower()
                    espanso_data.append({"trigger": trigger, "replace": full})

    except FileNotFoundError:
        raise FileNotFoundError(f"Không tìm thấy file: {csv_file_path}")
    except Exception as e:
        raise ValueError(f"Lỗi khi đọc file CSV: {str(e)}")

    return espanso_data


def create_espanso_yaml(espanso_data: List[Dict[str, str]], output_path: str) -> None:
    """
    Tạo file espanso.yml từ dữ liệu.

    Args:
        espanso_data: List các dictionary chứa trigger và replace
        output_path: Đường dẫn file output

    Raises:
        IOError: Nếu không thể ghi file
    """
    try:
        with open(output_path, "w", encoding="utf-8") as yamlfile:
            yaml.dump(
                espanso_data,
                yamlfile,
                default_flow_style=False,
                allow_unicode=True,
                sort_keys=False,
                indent=2,
            )
        print(f"Đã tạo thành công file espanso.yml tại: {output_path}")
        print(f"Tổng số trigger được tạo: {len(espanso_data)}")

    except Exception as e:
        raise IOError(f"Lỗi khi ghi file YAML: {str(e)}")


def main() -> None:
    """
    Hàm chính để thực hiện việc chuyển đổi CSV sang espanso.yml.
    """
    csv_file_path = "/home/<USER>/Dropbox/hnd/csv_source/cvxa.csv"
    output_path = "/home/<USER>/espanso.yml"

    try:
        # Đọc dữ liệu từ CSV
        print(f"Đang đọc file CSV: {csv_file_path}")
        espanso_data = read_csv_to_espanso_data(csv_file_path)

        # Tạo file espanso.yml
        print("Đang tạo file espanso.yml...")
        create_espanso_yaml(espanso_data, output_path)

        # Hiển thị một vài ví dụ
        print("\nVí dụ một số trigger đã tạo:")
        for i, item in enumerate(espanso_data[:5]):
            print(f"  - trigger: {item['trigger']}")
            print(f"    replace: {item['replace']}")
            if i < 4:
                print()

    except (FileNotFoundError, ValueError, IOError) as e:
        print(f"Lỗi: {str(e)}")
    except Exception as e:
        print(f"Lỗi không xác định: {str(e)}")


if __name__ == "__main__":
    main()
