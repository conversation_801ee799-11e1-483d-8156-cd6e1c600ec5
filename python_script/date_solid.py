from abc import ABC, abstractmethod

from datetime import datetime, timedelta


class IDateFormatter(ABC):
    @abstractmethod
    def format_date(self, date: datetime) -> tuple[str, str, str, str]:
        pass


class DateFormatter(IDateFormatter):
    def format_date(self, date: datetime) -> tuple[str, str, str, str]:  # 1
        ngay, thang, nam = self._format_date_components(date)
        return ngay, thang, nam, f"{ngay}/{thang}/{nam}"

    def _format_date_components(self, date: datetime) -> tuple[str, str, str]:  # 2
        ngay = str(date.day).zfill(2)
        thang = self._format_month(date.month)
        nam = str(date.year)
        return ngay, thang, nam

    def _format_month(self, month: int) -> str:  # 3
        return str(month).zfill(2) if month < 3 else str(month)


class IDateNowCalculator(ABC):
    @abstractmethod
    def get_date(self) -> datetime:
        pass


class DateNowCalculator(IDateNowCalculator):
    def get_date(self) -> datetime:
        current_date = datetime.now()
        return self._adjust_for_weekend(current_date)

    def _adjust_for_weekend(self, date: datetime) -> datetime:
        weekday = date.weekday()
        if weekday == 5:  # Saturday
            return date - timedelta(days=1)
        elif weekday == 6:  # Sunday
            return date - timedelta(days=2)
        return date


class IWeekendAdjuster(ABC):
    @abstractmethod
    def adjust_for_weekend(self, date: datetime) -> datetime:
        pass


class WeekendAdjuster(IWeekendAdjuster):
    def adjust_for_weekend(self, date: datetime) -> datetime:
        if date.weekday() in [5, 6]:
            return date - timedelta(days=2)
        return date


class DateBeforeCalculator(DateNowCalculator, WeekendAdjuster):
    def get_date(self, days: int) -> datetime:
        date_now = super().get_date()
        return self.adjust_for_weekend(date_now - timedelta(days=days))


def get_current_date():
    formater = DateFormatter()
    return formater.format_date(DateNowCalculator().get_date())[3]


def get_date_before(days: int):
    formater = DateFormatter()
    return formater.format_date(DateBeforeCalculator().get_date(days))[3]


def get_ngay_thang_nam_now():
    formater = DateFormatter()
    return (
        formater.format_date(DateNowCalculator().get_date())[0],
        formater.format_date(DateNowCalculator().get_date())[1],
        formater.format_date(DateNowCalculator().get_date())[2],
    )


def get_ngay_thang_nam_before(days: int):
    formater = DateFormatter()
    return (
        formater.format_date(DateBeforeCalculator().get_date(days))[0],
        formater.format_date(DateBeforeCalculator().get_date(days))[1],
        formater.format_date(DateBeforeCalculator().get_date(days))[2],
    )


if __name__ == "__main__":
    print(get_current_date())
    print(get_date_before(1))
    print(get_ngay_thang_nam_now())
    print(get_ngay_thang_nam_before(3))
