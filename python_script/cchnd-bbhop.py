import os
import subprocess

import pandas as pd
from loguru import logger

from god_class import (
    PandasCsv,
    TextProcess,
    compile_latex,
    convert_ngay,
    send_notification,
)
from god_class import show_message, get_day_before
from datetime import datetime


def tach_chuoi(text):
    # Tìm vị trí của dấu hai chấm đầu tiên trong chuỗi
    index = text.find(":")
    # Nếu tìm thấy dấu hai chấm
    if index != -1:
        # Tr<PERSON> về chuỗi từ vị trí sau dấu hai chấm đến cuối chuỗi, loại bỏ khoảng trắng ở đầu và cuối chuỗi
        return text[index + 1 :].strip(" )")
    else:
        return ""


def tao_ds_nguoi_cap_moi(df):
    ds_cap = ""
    count = 1
    for _, row in df.iterrows():
        loai_hinh_xac_nhan = (
            "Công ty"
            if "Công ty" in row["cong ty xac nhan"]
            else (
                "<PERSON> nhánh"
                if "<PERSON> nhánh" in row["cong ty xac nhan"]
                else (
                    "Nhà thuốc"
                    if "Nhà thuốc" in row["cong ty xac nhan"]
                    else (
                        "Quầy thuốc"
                        if "Quầy thuốc" in row["cong ty xac nhan"]
                        else (
                            "Trung tâm Y tế"
                            if "Trung tâm Y tế" in row["cong ty xac nhan"]
                            else (
                                "Bệnh viện"
                                if "Bệnh viện" in row["cong ty xac nhan"]
                                else "Chi nhánh"
                            )
                        )
                    )
                )
            )
        )

        ds_cap += f"""\\textbf{{({count}) Họ và tên: {row["ten nguoi ptcm"].title()}}}; địa chỉ thường trú: {row["dia chi thuong tru"]}; mã hồ sơ: {row["ma ho so"]}:
         
         - Bằng tốt nghiệp {row["trinh do cm"].lower()} do {row["noi tot nghiep"]} cấp ngày {row["ngay tot nghiep"]};

         - Giấy xác nhận thời gian thực hành chuyên môn dược: nơi xác nhận: {row["cong ty xac nhan"]}, nội dung thực hành: {row["noi dung thuc hanh"]}; thời gian thực hành: từ ngày {row["ngay bat dau th"]} đến ngày {row["ngay ket thuc th"]};

         - Phạm vi hành nghề đăng ký: {row["vi tri hanh nghe"]}.
         
        {loai_hinh_xac_nhan} có báo cáo khi {row["gioi tinh"].replace("1", "ông").replace("0", "bà")} {row["ten nguoi ptcm"].title()} bắt đầu đăng ký thực hành.\n\n
        Kết quả: hồ sơ đủ điều kiện để cấp CCHND.\n\n"""
        count += 1
    if count == 1:
        ds_cap = ds_cap.replace("\(1\)", "")

    return ds_cap


def tao_ds_nguoi_cap_lai(df):
    ds_cap = ""
    count = 1
    for _, row in df.df.iterrows():
        ds_cap += f"""\\textbf{{({count}) Họ và tên: {row["ten nguoi ptcm"].title()}}}; địa chỉ thường trú: {row["dia chi thuong tru"]}; trình độ chuyên môn: {row["trinh do cm"]};  {row["gioi tinh"].replace("1", "ông").replace("0", "bà")} {row["ten nguoi ptcm"].title()} đã được cấp CCHND số: {row["so cchnd cu"] if "-" in row["so cchnd cu"] else row["so cchnd cu"] + "/CCHND-SYT-VP"}, ngày cấp: {row["ngay cchnd cu"]}, phạm vi hành nghề: {row["pham vi hanh nghe cu"]}; lý do cấp lại: {row["thu tuc"].replace("CAP LAI CCHND DO LAM MAT", "làm mất CCHND").replace("CAP LAI CCHND DO LOI CQ CAP CCHND", "CCHND bị ghi sai do lỗi của cơ quan cấp CCHND")}; mã hồ sơ: {row["ma ho so"]}.
        
        Kết quả: hồ sơ đủ điều kiện để cấp lại CCHND.\par"""
        count += 1
    if count == 1:
        ds_cap = ds_cap.replace("\(1\)", "")
    return ds_cap


def tao_ds_nguoi_cap_dc(df):
    ds_cap = ""
    count = 1
    for _, row in df.df.iterrows():
        if tach_chuoi(row["loai cap"]) == "Thay đổi phạm vi hoạt động chuyên môn":
            loai_hinh_xac_nhan = (
                "Công ty"
                if "Công ty" in row["cong ty xac nhan"]
                else (
                    "Chi nhánh"
                    if "Chi nhánh" in row["cong ty xac nhan"]
                    else (
                        "Nhà thuốc"
                        if "Nhà thuốc" in row["cong ty xac nhan"]
                        else (
                            "Quầy thuốc"
                            if "Quầy thuốc" in row["cong ty xac nhan"]
                            else (
                                "Trung tâm Y tế"
                                if "Trung tâm Y tế" in row["cong ty xac nhan"]
                                else (
                                    "Bệnh viện"
                                    if "Bệnh viện" in row["cong ty xac nhan"]
                                    else "Chi nhánh"
                                )
                            )
                        )
                    )
                )
            )

            ds_cap += f"""\\textbf{{({count}) Họ và tên: {row["ten nguoi ptcm"].title()}}}; địa chỉ thường trú: {row["dia chi thuong tru"]}, mã hồ sơ: {row["ma ho so"]}; 
            
            - CCHND đã được cấp: {row["so cchnd cu"] if "-" in row["so cchnd cu"] else row["so cchnd cu"] + "/CCHND-SYT-VP"}, ngày cấp: {row["ngay cchnd cu"]}, phạm vi hành nghề: {row["pham vi hanh nghe cu"]};

            - Bằng tốt nghiệp {row["trinh do cm"].lower()} do {row["noi tot nghiep"]} cấp ngày {row["ngay tot nghiep"]};
            
            - Giấy xác nhận thời gian thực hành chuyên môn dược: nơi xác nhận: {row["cong ty xac nhan"]}, nội dung thực hành: {row["noi dung thuc hanh"]}; thời gian thực hành: từ ngày {row["ngay bat dau th"]} đến ngày {row["ngay ket thuc th"]};

            - Phạm vi hành nghề đề nghị điều chỉnh: {row["dang ky pham vi"]}.
            
            
            {loai_hinh_xac_nhan} có báo cáo khi {row["gioi tinh"].replace("1", "ông").replace("0", "bà")} {row["ten nguoi ptcm"].title()} bắt đầu đăng ký thực hành.\n\n
            
            Kết quả: hồ sơ đủ điều kiện để điều chỉnh nội dung trên CCHND.\par"""
        else:
            ds_cap += f"""\\textbf{{({count}) Họ và tên: {row["ten nguoi ptcm"].title()}}}; địa chỉ thường trú: {row["dia chi thuong tru"]}, mã hồ sơ: {row["ma ho so"]}:

            - CCHND đã được cấp: {row["so cchnd cu"] if "-" in row["so cchnd cu"] else row["so cchnd cu"] + "/CCHND-SYT-VP"}, ngày cấp: {row["ngay cchnd cu"]}, phạm vi hành nghề: {row["pham vi hanh nghe cu"]};

            - Bằng tốt nghiệp {row["trinh do cm"].lower()} do {row["noi tot nghiep"]} cấp ngày {row["ngay tot nghiep"]};

            - Lý do cấp điều chỉnh: thay đổi trình độ chuyên môn từ trung cấp dược thành cao đẳng dược.

            Kết quả: hồ sơ đủ điều kiện để điều chỉnh nội dung trên CCHND.\par"""
        count += 1
    if count == 1:
        ds_cap = ds_cap.replace("\(1\)", "")
    return ds_cap


def tao_ds_nguoi_cap_sau_thu_hoi(df):
    ds_cap = ""
    count = 1
    for _, row in df.df.iterrows():
        ds_cap += f"""\\textbf{{({count}) Họ và tên: {row["ten nguoi ptcm"].title()}}}; địa chỉ thường trú: {row["dia chi thuong tru"]}, mã hồ sơ: {row["ma ho so"]}:
         
          - {row["gioi tinh"].replace("0", "Bà").replace("1", "Ông")} {row["ten nguoi ptcm"].title()} đã được Sở Y tế tỉnh Phú Thọ cấp CCHND số {row["so cchnd cu"] if "-" in row["so cchnd cu"] else row["so cchnd cu"] + "/CCHND-SYT-VP"},   ngày cấp: {row["ngay cchnd cu"]} (CCHND đã bị thu hồi do {row["gioi tinh"].replace("0", "bà").replace("1", "ông")} {row["ten nguoi ptcm"].title()} không cập nhật kiến thức chuyên môn dược theo quy định):
        
        + Trình độ chuyên môn: {row["trinh do cm"]};
        
      + Phạm vi hành nghề: {row["vi tri hanh nghe"]}.
       
        - Giấy xác nhận hoàn thành chương trình đào tạo, cập nhật kiến thức chuyên môn về dược do {row["noi cap cnkt"]} cấp ngày {convert_ngay(row["ngay cap cnkt"])}.
        
        Kết quả: hồ sơ đủ điều kiện để cấp CCHND.\par"""
        count += 1
    if count == 1:
        ds_cap = ds_cap.replace("\(1\)", "")
    return ds_cap


@logger.catch
def run_main():
    values = {}
    os.chdir("/home/<USER>/Dropbox/hnd/csv_source")
    # TODO lấy ra ngay qd là ngày cuối cùng
    df_cchnd = PandasCsv("cchnd.csv", "ngay qd")
    df_cchnd.dropna()
    values["ngayqd"] = df_cchnd.get_num_loc(-1)
    df_dsqd = df_cchnd.get_df_by_mask(values["ngayqd"])

    values["ngayqd"] = datetime.strptime(values["ngayqd"], "%d/%m/%Y")
    values["ngaybb"] = get_day_before(1, values["ngayqd"])[3]
    values["ngay"], values["thang"], values["nam"] = values["ngaybb"].split("/")
    # Định dạng ngày trở lại dạng chuỗi
    values["ngaybb"] = datetime.strptime(values["ngaybb"], "%d/%m/%Y")
    if isinstance(df_dsqd.df, pd.Series):
        show_message("THONG BAO", "CHI CO 1 HO SO")
        return

    else:
        values["baogom"] = "bao gồm các hồ sơ sau"
    # TODO các Dataframe tương ứng với các loai cap

    df_landau = df_dsqd.get_df_by_mask(df_dsqd["loai cap"].isnull())
    df_sau_thu_hoi = df_dsqd.get_df_by_mask(
        df_dsqd["loai cap"].str.contains("sau khi thu hồi", na=False)
    )
    df_caplai = df_dsqd.get_df_by_mask(
        df_dsqd["loai cap"].str.contains("\(Cấp lại lần 1\)", na=False)
    )
    df_capdc = df_dsqd.get_df_by_mask(
        df_dsqd["loai cap"].str.contains("điều chỉnh", na=False)
    )
    values["count_landau"] = len(df_landau.df)
    values["count_caplai"] = len(df_caplai.df)
    values["count_capdc"] = len(df_capdc.df)
    values["count_sau_thu_hoi"] = len(df_sau_thu_hoi.df)
    # TODO BIẾN ĐẾM

    values["positive_count"] = sum(
        [
            values["count_landau"] > 0,
            values["count_caplai"] > 0,
            values["count_capdc"] > 0,
            values["count_sau_thu_hoi"] > 0,
        ]
    )

    # TODO CÁC HÀM
    values["result_texts"] = ""
    values["header_count"] = 1
    if len(df_landau.df) > 0:
        number = str(len(df_landau.df)).zfill(2)

        if number == "01":
            values["baogom"] = "cụ thể"
        ds_caplandau = tao_ds_nguoi_cap_moi(df_landau.df)

        values["text"] = (
            f"Tổng số có {number} hồ sơ đề nghị cấp CCHND, {values['baogom']}: \n\par {ds_caplandau} \n\par "
            + rf"""\textbf{{Kết luận}}: {number}/{number} hồ sơ có thành phần hồ sơ hợp lệ và đủ điều kiện cấp CCHND.


        \textbf{{Biểu quyết}}: 5/5 thành viên hội đồng nhất trí {number} hồ sơ trên đủ điều kiện để cấp CCHND (100\%).\par"""
        )
        if values["positive_count"] == 1:
            values["result_texts"] += values["text"]

        else:
            values["text"] = (
                rf"\textbf{{{values['header_count']}. Hồ sơ đề nghị cấp CCHND}} \par "
                + values["text"]
            )

            values["result_texts"] += values["text"]
            values["header_count"] += 1
    if len(df_sau_thu_hoi.df) > 0:
        number = str(len(df_sau_thu_hoi.df)).zfill(2)
        if len(df_sau_thu_hoi.df) == "01":
            values["baogom"] = "cụ thể"

        ds_sau_thu_hoi = tao_ds_nguoi_cap_sau_thu_hoi(df_sau_thu_hoi)
        values["text"] = (
            f"Tổng số có {number} hồ sơ đề nghị cấp CCHND sau khi bị thu hồi do không cập nhật kiến thức chuyên môn dược theo quy định, {values['baogom']}: \n\par {ds_sau_thu_hoi} \n\par "
            + rf"""\textbf{{Kết luận}}: {number}/{number} hồ sơ có thành phần hồ sơ hợp lệ và đủ điều kiện cấp CCHND.

        \textbf{{Biểu quyết}}: 5/5 thành viên hội đồng nhất trí {number} hồ sơ trên đủ điều kiện để cấp CCHND (100\%).\par"""
        )
        if values["positive_count"] == 1:
            values["result_texts"] += values["text"]
        else:
            values["text"] = (
                rf"\textbf{{{values['header_count']}. Hồ sơ đề nghị cấp CCHND sau khi CCHND bị theo hồi theo quy định tại Điều 28, Luật Dược}} \par "
                + values["text"]
            )

            values["result_texts"] += values["text"]
            values["header_count"] += 1
    # TODO CẤP ĐIỀU CHỈNH
    if len(df_capdc.df) > 0:
        number = str(len(df_capdc.df)).zfill(2)
        if len(df_capdc.df) == "01":
            values["baogom"] = "cụ thể"
        ds_capdc = tao_ds_nguoi_cap_dc(df_capdc)

        values["text"] = (
            f"Tổng số có {number} hồ sơ đề nghị điều chỉnh nội dung trên CCHND, {values['baogom']}: \n\par {ds_capdc} \n\par "
            + rf"""\textbf{{Kết luận}}: {number}/{number} hồ sơ có thành phần hồ sơ hợp lệ và đủ điều kiện để điều chỉnh nội dung trên CCHND.

        \textbf{{Biểu quyết}}: 5/5 thành viên hội đồng nhất trí {str(number).zfill(2)} hồ sơ trên đủ điều kiện để điều chỉnh nội dung trên CCHND (100\%).\par"""
        )
        if values["positive_count"] == 1:
            values["result_texts"] += values["text"]
        else:
            values["text"] = (
                rf"\textbf{{{values['header_count']}. Hồ sơ đề nghị điều chỉnh nội dung trên CCHND}} \par "
                + values["text"]
            )

            values["result_texts"] += values["text"]
            values["header_count"] += 1
    # TODO CẤP LẠI

    if len(df_caplai.df) > 0:
        number = str(len(df_caplai.df)).zfill(2)
        if len(df_caplai.df) == "01":
            values["baogom"] = "cụ thể"
        ds_caplai = tao_ds_nguoi_cap_lai(df_caplai)

        values["text"] = (
            f"Tổng số có {number}  hồ sơ đề nghị cấp lại CCHND, {values['baogom']}: \n\par {ds_caplai} \n\par "
            + rf"""\textbf{{Kết luận}}: {number}/{number} hồ sơ có thành phần hồ sơ hợp lệ và lý do cấp lại CCHND phù hợp.

        \textbf{{Biểu quyết}}: 5/5 thành viên hội đồng nhất trí {number}  hồ sơ trên đủ điều kiện để cấp lại CCHND (100\%).\par"""
        )
        if values["positive_count"] == 1:
            values["result_texts"] += values["text"]
        else:
            values["text"] = (
                rf"\textbf{{{values['header_count']}. Hồ sơ đề nghị cấp lại CCHND}} \par "
                + values["text"]
            )

            values["result_texts"] += values["text"]
            values["header_count"] += 1
    # TODO TỔNG SỐ

    values["sumds"] = (
        len(df_caplai.df)
        + len(df_capdc.df)
        + len(df_landau.df)
        + len(df_sau_thu_hoi.df)
    )

    values["sumds"] = str(int(values["sumds"])).zfill(2)
    # TODO cho vào latex
    bb = TextProcess("cchnd_bbhop")
    bb.format_text(values)
    name = f"Biên bản họp Tổ thư ký xét cấp CCHND ngày {values['ngay']}-{values['thang']}-{values['nam']}"
    path = f"/home/<USER>/Dropbox/hnd/latexall/bien_ban_hop/bien_ban_hop_cap_cchnd/{name}.tex"
    bb.copy_latex_file(path)
    command = f"nvim '{path}'"
    subprocess.run("wmctrl -xa terminator.Terminator", shell=True)
    subprocess.run(["tmux", "new-window", "-n", "latex-edit", command])

    send_notification(
        "Đọc kỹ các trường đại học, cao đẳng trong biên bản\n lưu ý các trường hợp không đạt",
        True,
    )


if __name__ == "__main__":
    run_main()
