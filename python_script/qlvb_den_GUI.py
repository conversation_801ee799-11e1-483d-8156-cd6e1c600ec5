import sys
from datetime import datetime

import demjson3 as demjson
import requests
from PyQt5 import QtCore, QtWidgets
from PyQt5.QtCore import QUrl
from PyQt5.QtGui import QFont, QDesktopServices, QBrush, QColor
from PyQt5.QtWidgets import QApplication, QMainWindow, QTableWidget, QTableWidgetItem, QAbstractItemView

from god_class import change_workspace

url = "https://qlvb-api.vinhphuc.gov.vn/api/van-ban-den/danh-sach-van-ban-den-theo-trang-thai-cua-chuyen-vien"

payload = 'co_tep_tin=-1&ma_can_bo=27683&ma_ctcb_nhan=21365&ma_don_vi_quan_tri=1187&ma_loai_ttdh=0&ma_yeu_cau=0&nam=0&nhan_den_ngay=17%2F06%2F2030&nhan_tu_ngay=15%2F06%2F2023&page=1&size=50&trang_thai_ttdh_gui=-1&trang_thai_xu_ly=0'
headers = {
    'Authorization': 'Bearer acKKZDmXqpUmn7D3vuzwk88RN909S7Soc5YlXQhyV6I9lU6UNYaTHtnVEhzxoo6Pul1K2NroQeS6584FZDucIYETRjjn70alDIbOUQ4V3zt8JarwIvOhqZ+z96uFvFuZjsu03k3l5Ou0DhYlEmlQ/15duefP7Ozfa3bfIo3bASLX3cOwnkpUl/pQoUl8aw6UA7KwPJDjnXYn/NR0nW7uuYcPT7ic/DdhkiE08Us6bbSjqfTbHGdR8fC8Nza/I52dVn4RLZDAGfVFhFgZS6qw2j2VTpQ1hpMelekgyrg23fFO2qhb81RllfgYPTifzG9j',
    'Connection': 'keep-alive', 'Content-Type': 'application/x-www-form-urlencoded', }
response = requests.post(url, headers=headers, data=payload)
res = demjson.decode(response.text)
if 'Không tìm thấy dữ liệu' in res['message']:
    print('Không tìm thấy dữ liệu')
    exit()


# %%

def format_date(input_date):
    try:
        # Chuyển đổi ngày tháng từ định dạng ban đầu
        input_date = datetime.strptime(input_date, "%Y-%m-%dT%H:%M:%S")
        # Định dạng lại theo "%d/%m/%Y"
        formatted_date = input_date.strftime("%d/%m/%Y")
        return formatted_date
    except ValueError:
        return "Invalid date format"


# %%


class MainWindow(QMainWindow):
    def them_du_lieu_vao_bang(self, res):
        self.table_widget.setColumnCount(6)
        self.table_widget.setHorizontalHeaderLabels(
            ['CƠ QUAN/ĐƠN VỊ', 'TRÍCH YẾU', 'SỐ KH', 'NGÀY BAN HÀNH', 'NỘI DUNG', 'LINK'])
        self.table_widget.cellClicked.connect(self.open_url)
        self.resize(1700, 900)
        self.table_widget.setFont(QFont('DejaVu', 12))  # Đặt cỡ chữ
        self.setCentralWidget(self.table_widget)
        for item in res['data']:
            row_position = self.table_widget.rowCount()
            self.table_widget.insertRow(row_position)
            self.table_widget.setItem(row_position, 0, QTableWidgetItem(item['ten_co_quan_ban_hanh']))
            self.table_widget.setItem(row_position, 1, QTableWidgetItem(item['trich_yeu']))
            self.table_widget.setItem(row_position, 2, QTableWidgetItem(item['so_ky_hieu']))
            self.table_widget.setItem(row_position, 3, QTableWidgetItem(format_date(item['ngay_ban_hanh'])))
            self.table_widget.setItem(row_position, 4, QTableWidgetItem(item['but_phe_cb_duyet']))
            self.table_widget.setItem(row_position, 5, QTableWidgetItem(
                f"https://iqlvb.vinhphuc.gov.vn/van-ban-den/xem-van-ban-den-chi-tiet?id={int(item['ma_van_ban_den_kc'])}&t=vb_den_chua_xu_ly_cua_cv&v=cv&xld={int(item['ma_xu_ly_den'])}"))
        self.table_widget.setSelectionMode(QAbstractItemView.SingleSelection)
        self.table_widget.setStyleSheet("QTableWidget::item:selected{ background-color: #771b90; }")

    # def open_url(self, row, column):
    #     if column == 5:
    #         url = QUrl(self.table_widget.item(row, column).text())
    #         QDesktopServices.openUrl(url)

    def open_url(self, row, column):
        if column == 5:
            # Định dạng màu sắc cho toàn bộ dòng
            for col in range(self.table_widget.columnCount()):
                # Lấy ra item hiện tại
                item = self.table_widget.item(row, col)
                # Nếu ô đó chưa được khởi tạo, tạo một QTableWidgetItem mới
                if item is None:
                    item = QTableWidgetItem()
                    self.table_widget.setItem(row, col, item)
                # Đặt màu chữ là đỏ
                item.setForeground(QBrush(QColor('red')))

            # Mở URL khi người dùng click vào cột thứ 5
            url = QUrl(self.table_widget.item(row, column).text())
            # subprocess.Popen(['firefox', url])
            QDesktopServices.openUrl(url)
            # change_workspace(1)


    def thay_doi_kich_thuoc_cot(self):
        self.table_widget.setColumnWidth(0, 200)
        self.table_widget.setColumnWidth(1, 500)
        self.table_widget.setColumnWidth(2, 200)
        self.table_widget.setColumnWidth(3, 110)
        self.table_widget.setColumnWidth(4, 150)
        self.table_widget.setColumnWidth(5, 320)  # Đặt kích thước cột thứ 4

    def __init__(self):
        super().__init__()
        # Tạo QTableWidget
        self.table_widget = QTableWidget()
        self.find = QtWidgets.QLineEdit(self.table_widget)
        self.find.setGeometry(QtCore.QRect(1500, 0, 150, 60))
        self.find.textChanged.connect(self.search)
        self.them_du_lieu_vao_bang(res)
        self.thay_doi_kich_thuoc_cot()
        self.table_widget.resizeRowsToContents()

    def search(self, text):
        count = 0
        for i in range(self.table_widget.rowCount()):
            self.table_widget.setRowHidden(i, True)
        for i in range(self.table_widget.rowCount()):
            for j in range(self.table_widget.columnCount()):
                item = self.table_widget.item(i, j)
                match = text.lower() in item.text().lower()
                if match:
                    self.table_widget.setRowHidden(i, False)
                    count += 1
                    break


def main():
    app = QApplication(sys.argv)
    main_window = MainWindow()
    main_window.show()
    change_workspace('pyqt')
    sys.exit(app.exec())


if __name__ == "__main__":
    main()  # %%