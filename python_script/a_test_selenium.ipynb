{"cells": [{"cell_type": "code", "execution_count": 12, "id": "fa01821d", "metadata": {}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": 13, "id": "493deb1b", "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "\n", "os.chdir(\"/home/<USER>/Dropbox/hnd/csv_source\")"]}, {"cell_type": "code", "execution_count": 14, "id": "6bc012d6", "metadata": {}, "outputs": [], "source": ["df=pd.read_csv(\"thu_tuc_kcb.csv\",dtype=str)\n", "df.fillna('',inplace=True)"]}, {"cell_type": "code", "execution_count": 15, "id": "9f73f2d4", "metadata": {}, "outputs": [], "source": ["df_filter=df.loc[df['TÊN thu tuc'].str.contains('gi<PERSON>y phép hoạt động')]\n"]}, {"cell_type": "code", "execution_count": 16, "id": "98dce6a2", "metadata": {}, "outputs": [], "source": ["df_filter['ma thu tuc'].unique().tolist()\n"]}, {"cell_type": "code", "execution_count": 18, "id": "47d26e4e", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'2.000984.000.00.00.H62': '<PERSON><PERSON><PERSON> gi<PERSON>y phép hoạt động đối với cơ sở dịch vụ y tế thuộc thẩm quyền của Sở Y tế',\n", " '1.003628.000.00.00.H62': '<PERSON><PERSON><PERSON> g<PERSON>y phép hoạt động đối với cơ sở khám bệnh, chữa bệnh khi thay đổi tên cơ sở khám bệnh, chữa bệnh thuộc thẩm quyền của Sở Y tế',\n", " '1.003531.000.00.00.H62': '<PERSON><PERSON><PERSON> g<PERSON>y phép hoạt động đối với cơ sở khám bệnh, chữa bệnh khi thay đổi người chịu trách nhiệm chuyên môn của cơ sở khám bệnh, chữ<PERSON> bệnh thuộc thẩm quyền của Sở Y tế',\n", " '1.003516.000.00.00.H62': '<PERSON><PERSON><PERSON> lại giấy phép hoạt động đối với cơ sở khám bệnh, chữ<PERSON> bệnh thuộc thẩm quyền của Sở Y tế do bị mất, hoặc hư hỏng hoặc bị thu hồi do cấp không đúng thẩm quyền',\n", " '1.002205.000.00.00.H62': '<PERSON><PERSON><PERSON> g<PERSON> phép hoạt động khám bệnh, chữa bệnh nhân đạo đối với Phòng chẩn trị y học cổ truyền thuộc thẩm quyền của Sở Y tế',\n", " '1.002140.000.00.00.H62': '<PERSON><PERSON><PERSON> g<PERSON> ph<PERSON>p hoạt độ<PERSON><PERSON><PERSON><PERSON> bệnh, chữa bệnh nhân đạo đối với cơ sở dịch vụ tiêm (ch<PERSON><PERSON>), thay b<PERSON>, đ<PERSON><PERSON> m<PERSON>, đo <PERSON><PERSON><PERSON> độ, đo huy<PERSON><PERSON>',\n", " '1.002111.000.00.00.H62': '<PERSON><PERSON><PERSON> g<PERSON> phép hoạt động khám bệnh, chữa bệnh nhân đạo đối với cơ sở dịch vụ chăm sóc sức khoẻ tại nhà',\n", " '1.002015.000.00.00.H62': '<PERSON><PERSON><PERSON> g<PERSON>y phép hoạt động khám bệnh, chữa bệnh nhân đạo đối với cơ sở khám bệnh, chữa bệnh thuộc thẩm quyền của Sở Y tế khi thay đổi tên cơ sở khám chữa bệnh',\n", " '1.002000.000.00.00.H62': '<PERSON><PERSON><PERSON> lại giấy phép hoạt động khám bệnh, chữa bệnh nhân đạo đối với cơ sở khám bệnh, chữa bệnh thuộc thẩm quyền của Sở Y tế do bị mất hoặc hư hỏng hoặc giấy phép bị thu hồi do cấp không đúng thẩm quyền',\n", " '1.008069.000.00.00.H62': '<PERSON><PERSON><PERSON> g<PERSON> phép hoạt động đối với <PERSON>, điều trị bệnh nghề nghiệp thuộc thẩm quyền của Sở Y tế',\n", " '1.003876.000.00.00.H62': '<PERSON><PERSON><PERSON> g<PERSON>y phép hoạt động đối với Phòng khám đa khoa thuộc thẩm quyền của Sở Y tế',\n", " '1.003848.000.00.00.H62': '<PERSON><PERSON><PERSON> gi<PERSON>y phép hoạt động đối với bệnh viện thuộc Sở Y tế và áp dụng đối với trường hợp khi thay đổi hình thức tổ chức, ch<PERSON>, h<PERSON><PERSON>h<PERSON>, s<PERSON><PERSON> nh<PERSON><PERSON>',\n", " '1.003803.000.00.00.H62': '<PERSON><PERSON><PERSON> g<PERSON>y phép hoạt động đối với Phòng khám chuyên khoa thuộc thẩm quyền của Sở Y tế',\n", " '1.003774.000.00.00.H62': '<PERSON><PERSON><PERSON> g<PERSON>y phép hoạt động đối với nhà hộ sinh thuộc thẩm quyền của Sở Y tế',\n", " '1.003746.000.00.00.H62': '<PERSON><PERSON><PERSON> g<PERSON><PERSON> phép hoạt động đối với trạm xá, trạm y tế xã',\n", " '1.003547.000.00.00.H62': 'Điều chỉnh giấy phép hoạt động đối với cơ sở khám bệnh, chữa bệnh khi thay đổi quy mô giường bệnh hoặc cơ cấu tổ chức hoặc phạm vi hoạt động chuyên môn thuộc thẩm quyền của Sở Y tế',\n", " '1.002230.000.00.00.H62': '<PERSON><PERSON><PERSON> g<PERSON> phép hoạt động khám bệnh, chữa bệnh nhân đạo đối với Phòng khám đa khoa thuộc thẩm quyền của Sở Y tế',\n", " '1.002215.000.00.00.H62': '<PERSON><PERSON><PERSON> g<PERSON> phép hoạt động khám bệnh, chữa bệnh nhân đạo đối với Phòng khám chuyên khoa thuộc thẩm quyền của Sở Y tế',\n", " '1.002191.000.00.00.H62': '<PERSON><PERSON><PERSON> g<PERSON> phép hoạt động khám bệnh, chữa bệnh nhân đạo đối với Nhà Hộ <PERSON> thuộc thẩm quyền của Sở Y tế',\n", " '1.002182.000.00.00.H62': '<PERSON><PERSON><PERSON> g<PERSON> phép hoạt động khám bệnh, chữa bệnh nhân đạo đối với phòng khám chẩn đoán hình ảnh thuộc thẩm quyền của Sở Y tế',\n", " '1.002162.000.00.00.H62': '<PERSON><PERSON><PERSON> g<PERSON> phép hoạt động khám bệnh, chữa bệnh nhân đạo đối với phòng xét nghiệm thuộc thẩm quyền của Sở Y tế',\n", " '1.002073.000.00.00.H62': '<PERSON><PERSON><PERSON> g<PERSON> phép hoạt động kh<PERSON>m bệnh, chữ<PERSON> bệnh nhân đạo đối với cơ sở dịch vụ cấp cứu, hỗ trợ vận chuyển người bệnh',\n", " '1.002037.000.00.00.H62': '<PERSON><PERSON><PERSON> g<PERSON> ph<PERSON>p hoạt động khám bệnh, chữa bệnh nhân đạo đối với cơ sở khám bệnh, chữa bệnh thuộc thẩm quyền của Sở Y tế khi thay đổi địa điểm',\n", " '1.001987.000.00.00.H62': 'Điều chỉnh gi<PERSON>y phép hoạt động khám bệnh, chữa bệnh nhân đạo đối với cơ sở khám bệnh, chữa bệnh truc thuoc Sở Y tế khi thay đổi quy mô giường bệnh hoặc cơ cấu tổ chức hoặc phạm vi hoạt động chuyên môn',\n", " '1.001907.000.00.00.H62': '<PERSON><PERSON><PERSON> gi<PERSON>y phép hoạt động khám, chữa bệnh nhân đạo đối với bệnh viện trên địa bàn quản lý của Sở Y tế (trừ các bệnh viện thuộc thẩm quyền của Bộ trưởng Bộ Y tế và Bộ Quốc phòng) và áp dụng đối với trường hợp khi thay đổi hình thức tổ chức, chia tách, hợ<PERSON> nh<PERSON>t, sáp nhập'}"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "dict_thu_tuc_cham=dict(zip(df_filter['ma thu tuc'],df_filter['TÊN thu tuc']))\n", "dict_thu_tuc_cham"]}, {"cell_type": "code", "execution_count": null, "id": "bd0bacd8", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "python313", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.1"}}, "nbformat": 4, "nbformat_minor": 5}