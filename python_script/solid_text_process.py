from abc import ABC, abstractmethod
from pathlib import Path
import shutil
import subprocess
from typing import Dict
from god_class import TelegramSend
from dataclasses import dataclass

@dataclass
class DocumentConfig:
    template_path: str
    output_path: str
    pdf_path: str

class ITextReader(ABC):
    @abstractmethod
    def read(self, path: str) -> str:
        pass

class ITextWriter(ABC):
    @abstractmethod  
    def write(self, content: str, path: str) -> None:
        pass

class ITextFormatter(ABC):
    @abstractmethod
    def format(self, template: str, values: Dict) -> str:
        pass

class FileReader(ITextReader):
    def read(self, path: str) -> str:
        return self.read_file(path)
        
    @staticmethod
    def read_file(file_path: str) -> str:
        with open(file_path, "r") as file:
            return file.read()

class FileWriter(ITextWriter):
    @staticmethod 
    def write_file(content: str, file_path: str) -> None:
        with open(file_path, "w", encoding="utf-8") as file:
            file.write(content)

class TextFormatter(ITextFormatter):
    def format(self, template: str, values: Dict) -> str:
        return self.format_text(template, values)
        
    @staticmethod
    def format_text(template: str, values: Dict[str, str]) -> str:
        return template.format(**values)

class IFileHandler(ABC):
    @abstractmethod
    def copy_file(self, source: str, destination: str) -> None:
        pass

class FileHandler(IFileHandler):
    def copy_file(self, source: str, destination: str) -> None:
        shutil.copy(source, destination)

class ILatexCompiler(ABC):
    @abstractmethod
    def compile(self, tex_file: str, output_dir: str) -> None:
        pass

class LatexCompiler(ILatexCompiler):
    def compile(self, tex_file: str, output_dir: str) -> None:
        subprocess.run([
            "pdflatex",
            f"-output-directory={output_dir}", 
            tex_file
        ])

class IPdfViewer(ABC):
    @abstractmethod
    def open_pdf(self, pdf_file: str) -> None:
        pass

class ZathuraPdfViewer(IPdfViewer):
    def open_pdf(self, pdf_file: str) -> None:
        subprocess.Popen(["zathura", pdf_file])

class TelegramNotifier:
    def __init__(self, channel: str):
        self.telegram = TelegramSend(channel)

    def send_file(self, file_path: str) -> None:
        self.telegram.send_file_warp(file_path)

class IDocumentProcessor(ABC):
    @abstractmethod
    def process(self, input_path: str, output_path: str, params: Dict) -> None:
        pass

class IDocumentStorage(ABC):
    @abstractmethod 
    def store(self, file_path: str, destination: str) -> None:
        pass

class LatexDocumentProcessor(IDocumentProcessor):
    def __init__(self, reader: FileReader, writer: FileWriter, formatter: TextFormatter):
        self.reader = reader
        self.writer = writer 
        self.formatter = formatter

    def process(self, template_path: str, output_path: str, values: Dict[str, str]) -> None:
        template = self.reader.read_file(template_path)
        formatted = self.formatter.format_text(template, values)
        self.writer.write_file(formatted, output_path)

class PdfFileManager(IDocumentStorage):
    def __init__(self, file_handler: IFileHandler):
        self.file_handler = file_handler
        
    def copy_to_destination(self, source: str, destination: str) -> None:
        self.file_handler.copy_file(source, destination)

class DocumentWorkflow:
    def __init__(
        self,
        latex_processor: IDocumentProcessor,
        latex_compiler: ILatexCompiler,
        pdf_manager: IDocumentStorage,
        pdf_viewer: IPdfViewer
    ):
        self.latex_processor = latex_processor
        self.latex_compiler = latex_compiler
        self.pdf_manager = pdf_manager
        self.pdf_viewer = pdf_viewer

class DocumentService:
    def __init__(
        self,
        processor: IDocumentProcessor,
        storage: IDocumentStorage,
        viewer: IPdfViewer,
        config: DocumentConfig  # Tách config ra riêng
    ):
        self.processor = processor
        self.storage = storage
        self.viewer = viewer
        self.config = config

class SimpleTextFormatter:
    def __init__(self):
        self.reader = FileReader()
        self.formatter = TextFormatter()
    
    def format_content(self, template_path: str, values: Dict[str, str]) -> str:
        template_content = self.reader.read_file(template_path)
        return self.formatter.format_text(template_content, values)

# Usage example:
def main():
    processor = DocumentWorkflow(
        LatexDocumentProcessor(
            FileReader(),
            FileWriter(),
            TextFormatter()
        ),
        LatexCompiler(),
        PdfFileManager(FileHandler()),
        ZathuraPdfViewer()
    )
    
    # Process text
    processor.latex_processor.process(
        str(Path("/home/<USER>/Dropbox/hnd/latexall/source_latex/sample_file.tex")),
        str(Path("/home/<USER>/Dropbox/hnd/latexall/mylatex/mylatex.tex")),
        {"key": "value"}
    )
    
    # View results
    processor.pdf_viewer.open_pdf(str(Path("/home/<USER>/Dropbox/hnd/latexall/mylatex/mylatex.pdf")))
