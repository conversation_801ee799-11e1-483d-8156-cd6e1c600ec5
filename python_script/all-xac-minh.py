import datetime as dt
import os
import sys

from top_most_get_text import input_dialog


from csv_load_and_export import CsvLoaderFactory
from god_class import convert_ngay, show_message, TextProcess, change_xa


def load_template(template_file, values):
    """Load template file and replace placeholders with values"""
    with open(template_file, "r", encoding="utf-8") as file:
        template = file.read()
    return template.format(**values)


os.chdir("/home/<USER>/Dropbox/hnd/csv_source")


df_name = CsvLoaderFactory.create_fillna_loader().load_df("name")
dstt = df_name.copy()
dstt = dstt[
    dstt["van de hs"].str.contains("hộ khẩu")
    | dstt["van de hs"].str.contains("tỉnh khác")
    | dstt["van de hs"].str.contains("Hộ khẩu")
]
if len(dstt) == 0:
    show_message("THONG BAO", "KHONG CO")
    sys.exit()
values = {}
text = TextProcess("/xac_minh/xac_minh")
for index, values in dstt.iterrows():
    values["ongba"] = "ông" if values["gioi tinh"] == "1" else "bà"
    values["dia chi thuong tru"] = change_xa(values["dia chi thuong tru"])
    values["trinh do cm"] = values["trinh do cm"].lower()
    if "GPP" in values["thu tuc"] or "GDP" in values["thu tuc"]:
        values["lydo"] = "xác minh thông tin người đăng ký hành nghề dược"
        values["kinhgui"] = values["noi cap cchnd"]
        values["tinhtp"] = values["noi cap cchnd"][8:]
        today = dt.datetime.today()
        values["ngayhan"] = convert_ngay(
            (today + dt.timedelta(days=7)).strftime("%d%m%Y")
        )  # THÊM 3 NGÀY LÀM VIEC
        df_name.loc[
            df_name.index[df_name["ma ho so"] == values["ma ho so"]].tolist()[0],
            "van de hs",
        ] = "Hạn xác minh " + values["ngayhan"] + "\n" + values["kinhgui"]
        # df_name.to_csv("name.csv", index=False)
        ngay = str(dt.date.today().day).zfill(2)
        thang = dt.date.today().month
        if thang < 3:
            thang = str(thang).zfill(2)
        nam = dt.date.today().year
        template_path = (
            "/home/<USER>/Dropbox/hnd/latexall/source_latex/xac_minh/template_gpp.txt"
        )
        values["noidung"] = load_template(template_path, values)
        tieude = f"xác minh thông tin người đăng ký hành nghề dược: {values['ten nguoi ptcm']}"
    else:
        values["lydo"] = "xác minh thông tin người đề nghị cấp Chứng chỉ hành nghề dược"
        values["ngay"] = str(dt.date.today().day).zfill(2)
        values["thang"] = dt.date.today().month
        if values["thang"] < 3:
            values["thang"] = str(values["thang"]).zfill(2)
        values["nam"] = dt.date.today().year
        if "Phú Thọ" in values["dia chi thuong tru"]:
            values["nhap_noi_xm"] = input_dialog("Title", "Nhap tinh/thanh pho", "")
            values["kinhgui"] = "Sở Y tế " + values["nhap_noi_xm"]
        else:
            values["kinhgui"] = (
                "Sở Y tế " + values["dia chi thuong tru"].rsplit(" ", 1)[-1]
            )
        values["ten nguoi ptcm"] = values["ten nguoi ptcm"].title()
        values["tinhtp"] = values["dia chi thuong tru"].rsplit(", ", 1)[-1]
        # Thêm các biến cần thiết cho template_cchnd.txt
        today = dt.datetime.today()
        values["ngayhan"] = convert_ngay(
            (today + dt.timedelta(days=7)).strftime("%d%m%Y")
        )  # THÊM 3 NGÀY LÀM VIEC
        # df_name.loc[
        #     df_name.index[df_name["ma ho so"] == values["ma ho so"]].tolist()[0],
        #     "van de hs",
        # ] = "Hạn xác minh " + values["ngayhan"]
        # df_name.to_csv("name.csv", index=False)
        template_path = (
            "/home/<USER>/Dropbox/hnd/latexall/source_latex/xac_minh/template_cchnd.txt"
        )
        values["noidung"] = load_template(template_path, values)
        tieude = f"xác minh thông tin người đề nghị cấp chứng chỉ hành nghề dược: {values['ten nguoi ptcm']}"
    text.format_text(values)
    text.auto_day_van_ban(tieude, "CV", "0")
