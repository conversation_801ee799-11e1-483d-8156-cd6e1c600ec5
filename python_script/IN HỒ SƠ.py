import sys

from PyQt5.QtGui import QFont
from PyQt5.QtWidgets import (
    QApplication,
    QMainWindow,
    QTableWidget,
    QTableWidgetItem,
    QVBoxLayout,
    QWidget,
    QPushButton,
    QMessageBox,
)

global window, values

import subprocess
import pikepdf
import pandas as pd
import os

os.chdir("/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pdf_documents/lam_ho_so/")
file_csv_cosobanle_path = "/home/<USER>/Dropbox/hnd/csv_source/co_so_ban_le.csv"
df_co_so_ban_le = pd.read_csv(file_csv_cosobanle_path, dtype=str)


def print_by_printer_and_filename(printer_name, file_pdf):
    output = subprocess.run(
        ["lpstat", "-p"], check=True, text=True, stdout=subprocess.PIPE
    )
    printer_list = output.stdout.split("\n")
    # <PERSON><PERSON>m tra xem máy in cần tìm có tồn tại không
    if any(printer_name in printer for printer in printer_list):
        # In các tệp PDF
        subprocess.run(["lpr", "-P", printer_name, file_pdf], check=True)


class MyWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("DANH SÁCH HỒ SƠ")
        # Load Excel file
        df_co_so_ban_le.fillna("", inplace=True)
        df_last_30_rows = df_co_so_ban_le.loc[
            :,
            [
                "ten nguoi ptcm",
                "ten qt-nt",
                "noi nhan",
                "IN CHỮ KÝ",
                "IN SỔ SÁCH",
                "IN BÌA CỨNG",
                "IN HỒ SƠ VÀ SOP",
            ],
        ].tail(30)
        self.last_30_rows_multi_list = df_last_30_rows.values.tolist()
        # Initialize UI components
        self.table = QTableWidget()
        self.print_hs_sop = QPushButton("1. IN HỒ SƠ VÀ QUY TRÌNH (MÁY ĐEN)")
        self.print_biacung = QPushButton("2. IN BÌA CỨNG (MÁY TRẮNG)")
        self.print_chuky = QPushButton("3. IN CHỮ KÝ (MÁY ĐEN)")
        self.name_qt_name_ptcm_key = None
        self.fullname = None
        self.mask = None
        self.row_index = None
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout()
        layout.addWidget(self.table)
        layout.addWidget(self.print_hs_sop)
        layout.addWidget(self.print_biacung)
        layout.addWidget(self.print_chuky)
        container = QWidget()
        container.setLayout(layout)
        self.setCentralWidget(container)
        # Set window and font size
        self.setGeometry(200, 100, 1400, 850)
        font = QFont("Arial", 17)
        self.setFont(font)
        # Populate the table
        self.table.setColumnCount(7)
        self.table.setHorizontalHeaderLabels(
            [
                "ten nguoi ptcm",
                "ten qt-nt",
                "noi nhan",
                "IN CHỮ KÝ",
                "IN SỔ SÁCH",
                "IN BÌA CỨNG",
                "IN HỒ SƠ VÀ SOP",
            ]
        )
        self.table.setRowCount(len(self.last_30_rows_multi_list))
        for row_index, list_1_csbl in enumerate(self.last_30_rows_multi_list):
            for col_index, col_data in enumerate(list_1_csbl):
                self.table.setItem(
                    row_index, col_index, QTableWidgetItem(str(col_data))
                )
        # Connect signals
        self.table.cellClicked.connect(self.on_cell_clicked)
        self.print_hs_sop.clicked.connect(self.on_print_hs_sop)
        self.print_biacung.clicked.connect(self.on_inbiacung)
        self.print_chuky.clicked.connect(self.on_chuky_clicked)
        self.table.resizeColumnsToContents()

    def on_cell_clicked(self, row):
        self.name_qt_name_ptcm_key = f"{self.last_30_rows_multi_list[row][1]}-{self.last_30_rows_multi_list[row][0]}"
        self.mask = (
            df_co_so_ban_le["ten qt-nt"] == self.name_qt_name_ptcm_key.split("-")[0]
        ) & (
            df_co_so_ban_le["ten nguoi ptcm"]
            == self.name_qt_name_ptcm_key.split("-")[1]
        )
        self.row_index = row

    def on_inbiacung(self):
        self.table.setItem(self.row_index, 5, QTableWidgetItem("X"))
        in_bia(self.name_qt_name_ptcm_key)
        df_temp = pd.read_csv(file_csv_cosobanle_path, dtype=str)
        df_temp.loc[self.mask, "IN BÌA CỨNG"] = "X"
        df_temp.to_csv(file_csv_cosobanle_path, index=False)

    def on_print_hs_sop(self):
        try:
            self.table.setItem(self.row_index, 6, QTableWidgetItem("X"))
            in_hs_qt_nt(self.name_qt_name_ptcm_key)
            df_temp = pd.read_csv(file_csv_cosobanle_path, dtype=str)
            df_temp.loc[self.mask, "IN HỒ SƠ VÀ SOP"] = "X"
            df_temp.to_csv(file_csv_cosobanle_path, index=False)
        except AttributeError:
            QMessageBox.warning(self, "THÔNG BÁO", "HÃY CHỌN ten qt-nt")

    def on_sosach_clicked(self):
        try:
            self.table.setItem(self.row_index, 4, QTableWidgetItem("X"))
            so_sach(self.name_qt_name_ptcm_key)
            df_temp = pd.read_csv(file_csv_cosobanle_path, dtype=str)
            df_temp.loc[self.mask, "IN SỔ SÁCH"] = "X"
            df_temp.to_csv(file_csv_cosobanle_path, index=False)
        except AttributeError:
            QMessageBox.warning(self, "THÔNG BÁO", "HÃY CHỌN ten qt-nt")

    def on_chuky_clicked(self):
        self.table.setItem(self.row_index, 3, QTableWidgetItem("X"))
        pdf_filepath = f"chu ky-{self.name_qt_name_ptcm_key}.pdf"
        # printer_method('SOSACH', pdf_filepath)
        print_by_printer_and_filename("HAIMAT", pdf_filepath)
        df_temp = pd.read_csv(file_csv_cosobanle_path, dtype=str)
        df_temp.loc[self.mask, "IN CHỮ KÝ"] = "X"
        df_temp.to_csv(file_csv_cosobanle_path, index=False)


def in_bia(name):
    printer_name = "BIACUNG"
    print_by_printer_and_filename(printer_name, f"bia-{name}.pdf")


def in_hs_qt_nt(name):
    printer_name = "HAIMAT"
    print_by_printer_and_filename(printer_name, f"hs-{name}.pdf")
    print_by_printer_and_filename(printer_name, f"dm thuoc-{name}.pdf")
    print_by_printer_and_filename(printer_name, f"sop-{name}.pdf")


def so_sach(name):
    with pikepdf.Pdf.open(f"sosach-{name}.pdf") as pdf:
        new_pdf = pikepdf.Pdf.new()
        for page in pdf.pages:
            for _ in range(15):
                new_pdf.pages.append(page)
        new_pdf.save("file_ghep_15_lan.pdf")
    # show_message('THONG BAO', 'BAM ALT + X NGAY')
    # printer_method('SOSACH', 'replicated.pdf')
    subprocess.Popen(["zathura", "file_ghep_15_lan.pdf"])


#

app = QApplication(sys.argv)
window = MyWindow()
window.show()
sys.exit(app.exec())