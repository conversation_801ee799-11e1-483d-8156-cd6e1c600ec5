#!/usr/bin/env python3
import os
import re
import sys


def get_cursor_pipe_id():
    # Tìm tất cả các file trong /tmp có tên bắt đầu bằng fzf-pipe-
    try:
        tmp_files = os.listdir("/tmp")
        pipe_files = [f for f in tmp_files if f.startswith("fzf-pipe-")]

        if not pipe_files:
            print(
                "Không tìm thấy pipe ID của Cursor. Hãy đảm bảo Cursor đang chạy.",
                file=sys.stderr,
            )
            sys.exit(1)

        # Lấy số ID từ tên file đầu tiên tìm thấy
        pipe_id = re.search(r"fzf-pipe-(\d+)", pipe_files[0])
        if pipe_id:
            print(pipe_id.group(1))
            return pipe_id.group(1)

    except Exception as e:
        print(f"Lỗi khi tìm pipe ID: {str(e)}", file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    pipe_id = get_cursor_pipe_id()
    os.system(
        f"tmux new-window -n open_file \"cd '/home/<USER>/Dropbox/hnd/python_script' && fzf | '$TOPIPE_SCRIPT' open /tmp/fzf-pipe-{pipe_id} && wmctrl -xa Cursor\""
    )
