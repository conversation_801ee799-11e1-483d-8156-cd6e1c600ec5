{"cells": [{"cell_type": "code", "execution_count": 1, "id": "9e8e8122782dad35", "metadata": {}, "outputs": [], "source": ["import pandas as pd "]}, {"cell_type": "code", "execution_count": 2, "id": "cf0a155d", "metadata": {}, "outputs": [], "source": ["import os\n", "\n"]}, {"cell_type": "code", "execution_count": 3, "id": "484fe673", "metadata": {}, "outputs": [], "source": ["df_all=pd.read_csv('/home/<USER>/Dropbox/hnd/csv_source/du_lieu_gpp_all.csv', dtype=str)"]}, {"cell_type": "code", "execution_count": 4, "id": "c4310a55", "metadata": {}, "outputs": [{"ename": "ValueError", "evalue": "time data \"22/04/2019\" doesn't match format \"%m/%d/%Y\", at position 4. You might want to try:\n    - passing `format` if your strings have a consistent format;\n    - passing `format='ISO8601'` if your strings are all ISO8601 but not necessarily in exactly the same format;\n    - passing `format='mixed'`, and the format will be inferred for each element individually. You might want to use `dayfirst` alongside this.", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[4], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m df_all[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mngay qd\u001b[39m\u001b[38;5;124m'\u001b[39m]\u001b[38;5;241m=\u001b[39m\u001b[43mpd\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mto_datetime\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdf_all\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mngay qd\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/.local/lib/python3.13/site-packages/pandas/core/tools/datetimes.py:1063\u001b[0m, in \u001b[0;36mto_datetime\u001b[0;34m(arg, errors, dayfirst, yearfirst, utc, format, exact, unit, infer_datetime_format, origin, cache)\u001b[0m\n\u001b[1;32m   1061\u001b[0m             result \u001b[38;5;241m=\u001b[39m arg\u001b[38;5;241m.\u001b[39mtz_localize(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mutc\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m   1062\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(arg, ABCSeries):\n\u001b[0;32m-> 1063\u001b[0m     cache_array \u001b[38;5;241m=\u001b[39m \u001b[43m_maybe_cache\u001b[49m\u001b[43m(\u001b[49m\u001b[43marg\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mformat\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcache\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mconvert_listlike\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1064\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m cache_array\u001b[38;5;241m.\u001b[39mempty:\n\u001b[1;32m   1065\u001b[0m         result \u001b[38;5;241m=\u001b[39m arg\u001b[38;5;241m.\u001b[39mmap(cache_array)\n", "File \u001b[0;32m~/.local/lib/python3.13/site-packages/pandas/core/tools/datetimes.py:247\u001b[0m, in \u001b[0;36m_maybe_cache\u001b[0;34m(arg, format, cache, convert_listlike)\u001b[0m\n\u001b[1;32m    245\u001b[0m unique_dates \u001b[38;5;241m=\u001b[39m unique(arg)\n\u001b[1;32m    246\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(unique_dates) \u001b[38;5;241m<\u001b[39m \u001b[38;5;28mlen\u001b[39m(arg):\n\u001b[0;32m--> 247\u001b[0m     cache_dates \u001b[38;5;241m=\u001b[39m \u001b[43mconvert_listlike\u001b[49m\u001b[43m(\u001b[49m\u001b[43munique_dates\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mformat\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[1;32m    248\u001b[0m     \u001b[38;5;66;03m# GH#45319\u001b[39;00m\n\u001b[1;32m    249\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n", "File \u001b[0;32m~/.local/lib/python3.13/site-packages/pandas/core/tools/datetimes.py:433\u001b[0m, in \u001b[0;36m_convert_listlike_datetimes\u001b[0;34m(arg, format, name, utc, unit, errors, dayfirst, yearfirst, exact)\u001b[0m\n\u001b[1;32m    431\u001b[0m \u001b[38;5;66;03m# `format` could be inferred, or user didn't ask for mixed-format parsing.\u001b[39;00m\n\u001b[1;32m    432\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mformat\u001b[39m \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28mformat\u001b[39m \u001b[38;5;241m!=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmixed\u001b[39m\u001b[38;5;124m\"\u001b[39m:\n\u001b[0;32m--> 433\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43m_array_strptime_with_fallback\u001b[49m\u001b[43m(\u001b[49m\u001b[43marg\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mname\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mutc\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mformat\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mexact\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43merrors\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    435\u001b[0m result, tz_parsed \u001b[38;5;241m=\u001b[39m objects_to_datetime64(\n\u001b[1;32m    436\u001b[0m     arg,\n\u001b[1;32m    437\u001b[0m     dayfirst\u001b[38;5;241m=\u001b[39mdayfirst,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    441\u001b[0m     allow_object\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m,\n\u001b[1;32m    442\u001b[0m )\n\u001b[1;32m    444\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m tz_parsed \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m    445\u001b[0m     \u001b[38;5;66;03m# We can take a shortcut since the datetime64 numpy array\u001b[39;00m\n\u001b[1;32m    446\u001b[0m     \u001b[38;5;66;03m# is in UTC\u001b[39;00m\n", "File \u001b[0;32m~/.local/lib/python3.13/site-packages/pandas/core/tools/datetimes.py:467\u001b[0m, in \u001b[0;36m_array_strptime_with_fallback\u001b[0;34m(arg, name, utc, fmt, exact, errors)\u001b[0m\n\u001b[1;32m    456\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21m_array_strptime_with_fallback\u001b[39m(\n\u001b[1;32m    457\u001b[0m     arg,\n\u001b[1;32m    458\u001b[0m     name,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    462\u001b[0m     errors: \u001b[38;5;28mstr\u001b[39m,\n\u001b[1;32m    463\u001b[0m ) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m Index:\n\u001b[1;32m    464\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m    465\u001b[0m \u001b[38;5;124;03m    Call array_strptime, with fallback behavior depending on 'errors'.\u001b[39;00m\n\u001b[1;32m    466\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[0;32m--> 467\u001b[0m     result, tz_out \u001b[38;5;241m=\u001b[39m \u001b[43marray_strptime\u001b[49m\u001b[43m(\u001b[49m\u001b[43marg\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mfmt\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mexact\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mexact\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43merrors\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43merrors\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mutc\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mutc\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    468\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m tz_out \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m    469\u001b[0m         unit \u001b[38;5;241m=\u001b[39m np\u001b[38;5;241m.\u001b[39mdatetime_data(result\u001b[38;5;241m.\u001b[39mdtype)[\u001b[38;5;241m0\u001b[39m]\n", "File \u001b[0;32mstrptime.pyx:501\u001b[0m, in \u001b[0;36mpandas._libs.tslibs.strptime.array_strptime\u001b[0;34m()\u001b[0m\n", "File \u001b[0;32mstrptime.pyx:451\u001b[0m, in \u001b[0;36mpandas._libs.tslibs.strptime.array_strptime\u001b[0;34m()\u001b[0m\n", "File \u001b[0;32mstrptime.pyx:583\u001b[0m, in \u001b[0;36mpandas._libs.tslibs.strptime._parse_with_format\u001b[0;34m()\u001b[0m\n", "\u001b[0;31mValueError\u001b[0m: time data \"22/04/2019\" doesn't match format \"%m/%d/%Y\", at position 4. You might want to try:\n    - passing `format` if your strings have a consistent format;\n    - passing `format='ISO8601'` if your strings are all ISO8601 but not necessarily in exactly the same format;\n    - passing `format='mixed'`, and the format will be inferred for each element individually. You might want to use `dayfirst` alongside this."]}], "source": ["\n", "df_all['ngay qd']=pd.to_datetime(df_all['ngay qd'])"]}, {"cell_type": "code", "execution_count": 21, "id": "5747f3f9", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_93305/134501222.py:1: FutureWarning: A value is trying to be set on a copy of a DataFrame or Series through chained assignment using an inplace method.\n", "The behavior will change in pandas 3.0. This inplace method will never work because the intermediate object on which we are setting values always behaves as a copy.\n", "\n", "For example, when doing 'df[col].method(value, inplace=True)', try using 'df.method({col: value}, inplace=True)' or df[col] = df[col].method(value) instead, to perform the operation inplace on the original object.\n", "\n", "\n", "  df_all['ngay qd'].fillna(df_all['ngay qd'].min(),inplace=True)\n"]}], "source": ["df_all['ngay qd'].fillna(df_all['ngay qd'].min(),inplace=True)\n", "df_all=df_all[df_all['ngay qd']<=pd.to_datetime('2024-08-31')]"]}, {"cell_type": "code", "execution_count": 22, "id": "89172fc3", "metadata": {}, "outputs": [], "source": ["df_dk=pd.read_csv('/home/<USER>/Dropbox/hnd/csv_source/dkkd.csv',dtype=str)"]}, {"cell_type": "code", "execution_count": 23, "id": "b92c41cc", "metadata": {}, "outputs": [], "source": ["import god_class\n", "df_all=god_class.update_multi_with_vlookup(df_all,df_dk,'so dkkd',['pham vi kd', 'bao quan lanh'])"]}, {"cell_type": "code", "execution_count": 24, "id": "3e581d92", "metadata": {}, "outputs": [], "source": ["df_all.sort_values(by='loai hinh',inplace=True)\n", "df_all['ten qt-nt']=df_all['ten qt-nt'].str.upper()\n", "df_all['ngay qd']=df_all['ngay qd'].dt.strftime('%d/%m/%Y')\n", "df_all['ten nguoi ptcm']=df_all['ten nguoi ptcm'].str.title()\n", "df_all=df_all[['ten qt-nt','ten nguoi ptcm','dia chi co so','pham vi kd','ngay qd','so dt chu hs']]\n", "df_all.to_excel('co_so_ban_le.xlsx',index=False)\n"]}], "metadata": {"kernelspec": {"display_name": "python313", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.1"}}, "nbformat": 4, "nbformat_minor": 5}