import os

import pandas as pd

from god import get_current_date

os.chdir("/home/<USER>/Dropbox/hnd/csv_source")
df_traloi_xm = pd.read_csv("danh-sach-thieu-nhi-syt-2024.csv", dtype=str)

_, _, nam,_ = get_current_date()


def format_number(num):
    return "{:,.0f}".format(num).replace(",", ".")


desired_order = [
    "Lãnh đạo Sở",
    "CĐ ngành",
    "P.Thanh Tra",
    "P.NVYD",
    "P.TCCB",
    "P.NVD",
    "P.KHTC",
    "P.ATTP",
    "VPS",
    "BBVSK",
]

# Chuyển cột 'values' thành kiểu Categorical với thứ tự mong muốn
df_traloi_xm["PHÒNG"] = pd.Categorical(df_traloi_xm["PHÒNG"], categories=desired_order, ordered=True)
df_traloi_xm = df_traloi_xm.sort_values(by=["PHÒNG", "TÊN BỐ, MẸ"])
# %%
df_traloi_xm.reset_index(drop=True, inplace=True)
df_traloi_xm.insert(0, "stt", df_traloi_xm.index + 1)
tong_tien = format_number(len(df_traloi_xm) * 300000)
ds = df_traloi_xm.astype(str).apply("&".join, axis=1)
clip = ds.str.cat(sep=r"\\" + "\n") + r"\\"
# %%

import shutil
import subprocess


def compile_latex(text, ten_file_pdf):
    os.chdir("/home/<USER>/Dropbox/hnd/latexall/mylatex")
    f = open("mylatex.tex", "w", encoding="utf-8")
    f.write(text)
    f.close()
    subprocess.run(["pdflatex", "mylatex.tex"])
    shutil.copy("mylatex.pdf", f"/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pdf_documents/kq/{ten_file_pdf}.pdf")
    # shutil.copy("mylatex.pdf", f"/home/<USER>/Dropbox/{ten_file_pdf}.pdf")
    # open_with_foxit(f"Z:/lediem/Dropbox/{ten_file_pdf}.pdf")
    subprocess.Popen(["zathura", f"/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pdf_documents/kq/{ten_file_pdf}.pdf"])


text = rf"""
\documentclass{{article}} 
\usepackage[T5]{{fontenc}}
\nonstopmode
\usepackage[fontsize=14pt]{{scrextend}}
\usepackage[a4paper,vmargin=2cm,right=2cm,left=3cm,]{{geometry}}
\usepackage{{mathptmx}}
\usepackage{{ulem}}
\renewcommand{{\ULdepth}}{{7pt}}
\renewcommand{{\ULthickness}}{{0.5pt}}
\usepackage{{setspace}}
\usepackage{{tabularray}}
\usepackage{{pdflscape}}
\usepackage{{indentfirst}}
\setlength{{\parindent}}{{1.27cm}}
\pagenumbering{{gobble}}
\begin{{document}}

\noindent
\setstretch{{1}}
\noindent
\begin{{minipage}}{{\textwidth}}
\begin{{tblr}}{{width=1\linewidth,
colspec={{X[1,c] X[2,c]}},
colsep=0pt,
rowsep=0pt,
row{{2}} = {{font=\bfseries}}}}
\fontsize{{13pt}}{{0pt}}\selectfont UBND TỈNH VĨNH PHÚC & \fontsize{{13pt}}{{0pt}}\selectfont \textbf{{CỘNG HÒA XÃ HỘI CHỦ NGHĨA VIỆT NAM}}\\
{{\fontsize{{13pt}}{{0pt}}\selectfont SỞ Y TẾ\\[3pt]
\rule[0.6\baselineskip]{{.14\linewidth}}{{.5pt}}}} & \fontsize{{14pt}}{{0pt}}\selectfont \uline{{Độc lập - Tự do - Hạnh phúc}}\\[12pt]
\end{{tblr}}
\end{{minipage}}

\begin{{center}} \bfseries

DANH SÁCH PHÁT QUÀ NGÀY QUỐC TẾ THIẾU NHI 01/6/{nam}

\end{{center}}



\DefTblrTemplate{{contfoot-text}}{{default}}{{}}
\DefTblrTemplate{{conthead-text}}{{default}}{{}}
\DefTblrTemplate{{caption}}{{default}}{{}}
\DefTblrTemplate{{conthead}}{{default}}{{}}
\DefTblrTemplate{{capcont}}{{default}}{{}}
\SetTblrInner{{rowsep=0pt}}

\hspace{{10.9cm}} Đơn vị tính: đồng

\vspace{{-0.3cm}} 

\noindent
\begin{{longtblr}}{{width=1\linewidth,hlines,vlines,
colspec={{X[0.2,c] X[0.8,l]X[0.3,c]X[0.8,l]X[0.7,c]X[0.9,c]}},
colsep=3pt,
rowsep=1pt,rows={{m,1cm}},row{{1}}={{font=\small\bfseries,c}}}}
STT  &  Họ tên con  &  Năm sinh  &  Tên bố/mẹ  &  Phòng  &  Số tiền  \\
{clip}
\SetCell[c=4]{{c}} \textbf{{TỔNG TIỀN}} &  &  &  &   \SetCell[c=2]{{c}}    \textbf{{{tong_tien}}} & \\
\end{{longtblr}}

\vspace{{0.5cm}} 

\noindent
Tổng tiền bằng chữ: \textit{{\bfseries Hai mươi sáu triệu, bốn trăm nghìn đồng chẵn}}\

\vspace{{0.5cm}}

\noindent
\begin{{minipage}}{{\textwidth}}
\begin{{tblr}}{{width=1\linewidth,
colspec={{X[1,c]X[1,c]X[1,c] X[1,c]}},
colsep=0pt,
rowsep=0pt,rows={{font=\bfseries}}
}}

\noindent
Người lập biểu   &  Kế toán trưởng  &  Lãnh đạo duyệt \\
 \\
 \\
 \\
 \\
 \\
 Lê Tùng Sơn   &  Đỗ Thị Hồng Tươi &

\end{{tblr}}
\end{{minipage}}
\end{{document}}
"""
compile_latex(text, "ds phat qua 1-6")
