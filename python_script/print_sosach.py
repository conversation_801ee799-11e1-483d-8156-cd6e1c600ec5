import subprocess

output = subprocess.run(['lpstat', '-p'], check=True, text=True, stdout=subprocess.PIPE)
printers = output.stdout.split('\n')


def printer_method(printer_name, file_pdf):
    # Ki<PERSON>m tra xem máy in cần tìm có tồn tại không
    if any(printer_name in printer for printer in printers):
        # In các tệp PDF
        subprocess.run(['lp', '-d', printer_name, file_pdf], check=True)


printer_method('SOSACH', '/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pdf_documents/lam_ho_so/replicated.pdf')