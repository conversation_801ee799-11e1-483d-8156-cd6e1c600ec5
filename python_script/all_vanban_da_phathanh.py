import datetime
import glob
import os
import pickle
import shutil
import sys

import demjson3 as demjson
import pandas as pd
import requests
from god_class import TelegramSend
from god_class import send_notification

os.chdir("/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pickle_file")


# TODO CÁC HÀM
def save_qd(qd, file):
    with open(file, "wb") as f:
        pickle.dump(qd, f)


def extract_number(file_path):
    try:
        file_name = os.path.basename(file_path)
        if "0-" in file_name:
            return file_name.split("-")[1]
        else:
            return file_name.split("-")[0]
    except IndexError:
        return "****"


def save_csv(list_title, filename, col_name, date_name):
    if list_title in ten_vb:
        ds = pd.read_csv(f"/home/<USER>/Dropbox/hnd/csv_source/{filename}", dtype="str")
        mask = (ds[col_name].isna()) & (
            ds[date_name]
            .replace("/3", "03")
            .replace("/4", "04")
            .replace("/5", "05")
            .replace("/6", "06")
            .replace("/7", "07")
            .replace("/8", "08")
            .replace("/9", "09")
            == van_ban["ngay_ban_hanh"][:10]
        )
        if not ds.loc[mask].empty:
            ds.loc[mask, col_name] = so_kh
            ds.to_csv(filename, index=False)


def save_excel_ntbv(filename, sovb):
    if (
        "QUYẾT ĐỊNH Về việc cấp giấy chứng nhận đạt Thực hành tốt cơ sở bán lẻ thuốc cho"
        in ten_vb
    ):
        ds = pd.read_csv(filename, dtype="str")
        mask = ds["van de hs"] == "gpp"
        if not ds.loc[mask].empty:
            ds.loc[mask, "so qd"] = sovb
            ds.to_csv(filename, index=False)


def remove_files(directory, extensions):
    """Xóa tất cả các file có phần mở rộng nằm trong danh sách 'extensions' trong thư mục chỉ định"""
    for ext in extensions:
        filelist = glob.glob(os.path.join(directory, f"*.{ext}"))
        for file in filelist:
            try:
                os.remove(file)
                print(f"Đã xóa file: {file}")
            except OSError as e:
                print(f"Không thể xóa file: {file}. Lỗi: {e.strerror}")


telegram = TelegramSend("hnd")


def get_response():
    global url, payload, headers, response, res
    url = "https://qlvb-api.vinhphuc.gov.vn/api/van-ban-di/danh-sach-van-ban-di-da-phat-hanh-cua-chuyen-vien"
    nam = datetime.datetime.now().year
    payload = f"ma_ctcb_cv=21365&ma_don_vi_quan_tri=1187&nam={nam}&ngay_di_den_ngay=20%2F06%2F2030&ngay_di_tu_ngay=20%2F05%2F2023&page=1&size=50"
    headers = {
        "Authorization": "Bearer acKKZDmXqpUmn7D3vuzwk88RN909S7Soc5YlXQhyV6I9lU6UNYaTHtnVEhzxoo6Pul1K2NroQeS6584FZDucIYETRjjn70alDIbOUQ4V3zt8JarwIvOhqZ+z96uFvFuZjsu03k3l5Ou0DhYlEmlQ/15duefP7Ozfa3bfIo3bASLX3cOwnkpUl/pQoUl8aw6UE8xFVZgKSskn/NR0nW7uuYcPT7ic/DdhkiE08Us6bbSjqfTbHGdR8fC8Nza/I52dVn4RLZDAGfVFhFgZS6qw2j2VTpQ1hpMelekgyrg23fFO2qhb81RllfgYPTifzG9j",
        "Connection": "keep-alive",
        "Content-Type": "application/x-www-form-urlencoded",
    }
    response = requests.post(url, headers=headers, data=payload)
    return demjson.decode(response.text)


# TODO TẢI DỮ LIỆU VỀ FETCHXHR
# remove_files("/home/<USER>/Dropbox/PDF", ['pdf'])
res = get_response()
# TODO 2. nếu bị lỗi thì thoát
if "Không tìm thấy dữ liệu" in res["message"]:
    telegram.send_message_normal("Không tìm thấy dữ liệu")
    sys.exit()
file_latex_cho_in = glob.glob("/home/<USER>/Dropbox/hnd/latexall/vb_cho_in/*.tex")
# file_latex_cho_in = glob.glob('/home/<USER>/Dropbox/hnd/latexall/vb_cho_in/*.tex')
condition_met = False
# TODO 2.1 lặp qua từng phần từ trong demjson
list_vb_ph = [
    res["data"][i]
    for i in range(len(res["data"]))
    if res["data"][i]["can_bo_soan"] == "Lê Tùng Sơn"
]
message = "ĐÃ PHÁT HÀNH:"
phathanh = False
for van_ban in list_vb_ph:
    so_kh = van_ban["so_ky_hieu"].replace("/QĐ-SYT", "")
    file_vb = van_ban["file_van_ban"]
    ten_vb = van_ban["trich_yeu"].replace("V/v", "Vv")
    list_vanban = [
        file_path
        for file_path in file_latex_cho_in
        if extract_number(file_path) in van_ban["file_van_ban"]
    ]
    if len(list_vanban) > 0:
        phathanh = True
        message += f"\n\n{van_ban['trich_yeu']}"
        shutil.move(
            list_vanban[0],
            "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/Latex/backup_vb_da_phat_hanh",
        )
        save_csv(
            "QUYẾT ĐỊNH Về việc thu hồi, hủy bỏ giấy chứng nhận đạt Thực hành tốt cơ sở bán lẻ thuốc và giấy chứng nhận đủ điều kiện kinh doanh dược",
            "ds_da_cham_dut.csv",
            "so qd cham dut",
            "ngay qd cham dut",
        )
        save_csv(
            "QUYẾT ĐỊNH Về việc cấp điều chỉnh giấy chứng nhận đạt Thực hành tốt phân phối thuốc",
            "gdp.csv",
            "so qd",
            "ngay qd",
        )
        save_csv(
            "QUYẾT ĐỊNH Về việc cấp giấy chứng nhận đạt Thực hành tốt phân phối thuốc",
            "gdp.csv",
            "so qd",
            "ngay qd",
        )
        if (
            "QUYẾT ĐỊNH Về việc cấp giấy chứng nhận đạt Thực hành tốt cơ sở bán lẻ thuốc cho"
            in ten_vb
        ):
            save_qd(so_kh, "soqd_ntbv.pk")
            save_excel_ntbv("dkkd.csv", so_kh)
            telegram.send_message_normal(
                f'ĐÃ LƯU so qd GPP NTBV {so_kh} NGÀY {van_ban["ngay_ban_hanh"][:10]}',
            )
        elif "QUYẾT ĐỊNH Về việc cấp chứng chỉ hành nghề dược" in ten_vb:
            save_qd(so_kh, "soqd_cc.pk")
            telegram.send_message_normal(
                f'ĐÃ LƯU so qd CC {so_kh} NGÀY {van_ban["ngay_ban_hanh"][:10]}'
            )
        elif (
            "QUYẾT ĐỊNH Về việc cấp giấy chứng nhận đạt Thực hành tốt cơ sở bán lẻ thuốc và"
            in ten_vb
        ):
            save_qd(so_kh, "soqd_gpp.pk")
            telegram.send_message_normal(
                f'ĐÃ LƯU so qd GPP {so_kh} NGÀY {van_ban["ngay_ban_hanh"][:10]}',
            )
if phathanh:
    telegram.send_message_normal(message)
else:
    send_notification("không có văn bản phát hành")
url = "https://qlvb-api.vinhphuc.gov.vn/api/van-ban-di/danh-sach-van-ban-di-da-xu-ly-cua-chuyen-vien"

payload = "co_tep_tin=-1&ma_can_bo=27683&ma_ctcb_cv=21365&ma_don_vi_quan_tri=1187&ma_loai_ttdh=0&nam=0&ngay_tao_den_ngay=19%2F05%2F2030&ngay_tao_tu_ngay=19%2F04%2F2024&page=1&size=20&trang_thai_ttdh_gui=-1"
headers = {
    "Content-Type": "application/x-www-form-urlencoded",
    "Authorization": "Bearer acKKZDmXqpUmn7D3vuzwk88RN909S7Soc5YlXQhyV6I9lU6UNYaTHtnVEhzxoo6Pul1K2NroQeS6584FZDucIYETRjjn70alDIbOUQ4V3zt8JarwIvOhqZ+z96uFvFuZjsu03k3l5Ou0DhYlEmlQ/15duefP7Ozfa3bfIo3bASLX3cOwnkpUlxFUw++QofrTa0z0CGF60oIn/NR0nW7uuYcPT7ic/DdhkiE08Us6bbSjqfTbHGdR8fC8Nza/I52dVn4RLZDAGfVFhFgZS6qw2j2VTpQ1hpMelekgyrg23fFO2qhb81RllfgYPTifzG9j",
}

response = requests.request("POST", url, headers=headers, data=payload)
res = demjson.decode(response.text)
so_choxuly = len(res["data"])

url = "https://qlvb-api.vinhphuc.gov.vn/api/van-ban-di/danh-sach-van-ban-di-cho-phat-hanh-cua-chuyen-vien"

payload = "co_tep_tin=-1&ma_can_bo=27683&ma_ctcb_cv=21365&ma_don_vi_quan_tri=1187&ma_loai_ttdh=0&nam=0&ngay_tao_den_ngay=22%2F05%2F2030&ngay_tao_tu_ngay=22%2F04%2F2024&page=1&size=20&trang_thai_ttdh_gui=-1&trang_thai_xu_ly=3"
headers = {
    "Content-Type": "application/x-www-form-urlencoded",
    "Authorization": "Bearer acKKZDmXqpUmn7D3vuzwk88RN909S7Soc5YlXQhyV6I9lU6UNYaTHtnVEhzxoo6Pul1K2NroQeS6584FZDucIYETRjjn70alDIbOUQ4V3zt8JarwIvOhqZ+z96uFvFuZjsu03k3l5Ou0DhYlEmlQ/15duefP7Ozfa3bfIo3bASLX3cOwnkpUlxFUw++QofrTNoc0/zrpo7gn/NR0nW7uuYcPT7ic/DdhkiE08Us6bbSjqfTbHGdR8fC8Nza/I52dVn4RLZDAGfVFhFgZS6qw2j2VTpQ1hpMelekgyrg23fFO2qhb81RllfgYPTifzG9j",
}

response = requests.request("POST", url, headers=headers, data=payload)
res = demjson.decode(response.text)
so_chophathanh = len(res["data"])
if so_chophathanh + so_choxuly == 0:
    file_move = glob.glob("/home/<USER>/Dropbox/hnd/latexall/vb_cho_in/*")
    for f in file_move:
        try:
            shutil.move(
                f,
                "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/Latex/backup_vb_da_phat_hanh",
            )
        except shutil.Error as e:
            if "Destination path" in str(e) and "already exists" in str(e):
                os.remove(f)
            else:
                raise
