import os
import random
import shutil
import subprocess
import pikepdf

from god_class import (
    TelegramSend,
    copy_file_to_clip,
    print_file_by_printer,
    replace_variables,
)
from god_class import send_notification

os.chdir("/home/<USER>/Dropbox/hnd/csv_source")
path_csbl = "co_so_ban_le.csv"
path_dshn = "dshn_duoc.csv"
path = "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pdf_documents"

list_bold = [r"\textbf", "", ""]
list_tit = [r"\textit", r"\textit", r"\textit", ""]


def chen_checklist_truoc_trangcuoi(checklist_path, hs_path):
    # Mở các file PDF
    file_checklist = pikepdf.open(checklist_path)
    file_hs = pikepdf.open(hs_path)

    # Lấy số trang trong pdf2 và tính vị trí chèn trang từ pdf1
    trang_truoc_trang_cuoi = len(file_hs.pages) - 1

    # <PERSON><PERSON><PERSON> tra nếu pdf2 có ít hơn 2 trang

    # Chèn từng trang từ pdf1 vào vị trí chèn trong pdf2
    for page in reversed(file_checklist.pages):
        # Chèn từng trang từ pdf1 trước trang cuối cùng của pdf2
        file_hs.pages.insert(trang_truoc_trang_cuoi, page)
    file_hs.save("temp.pdf")
    shutil.copy("temp.pdf", hs_path)

    # Lưu file PDF sau khi đã nối

    # Đóng các file PDF
    file_checklist.close()
    file_hs.close()


def extract_pdf_from_page_numbers(file_pdf_origin, file_pdf_extracted, page_to_extract):
    with pikepdf.Pdf.open(file_pdf_origin) as pdf:
        extracted_pages = pikepdf.Pdf.new()
        for page_number in page_to_extract:
            if page_number == -1:
                page_number = len(pdf.pages)
            extracted_pages.pages.append(pdf.pages[page_number - 1])

        extracted_pages.save(file_pdf_extracted)


def ghep_file_pdfs(list_file_pdfs, file_pdf_merged):
    merged_pdf = pikepdf.Pdf.new()

    for input_path in list_file_pdfs:
        with pikepdf.Pdf.open(input_path) as pdf:
            for page in pdf.pages:
                merged_pdf.pages.append(page)

    merged_pdf.save(file_pdf_merged)


def tach_trang(pagehs, pagesop):
    extract_pdf_from_page_numbers("hs.pdf", "check_last_page.pdf", [-1])
    extract_pdf_from_page_numbers("hs.pdf", "hs_1_4_5_6.pdf", pagehs)
    extract_pdf_from_page_numbers("sop.pdf", "sop_2_8_17_23_30_42.pdf", pagesop)
    # Ghép các trang tách ra vào file chu_ky.pdf
    ghep_file_pdfs(
        ["check_last_page.pdf", "hs_1_4_5_6.pdf", "sop_2_8_17_23_30_42.pdf"],
        "chu ky.pdf",
    )


def convert_word_to_pdf(input_file_path, output_file_path):
    output_dir = os.path.dirname(output_file_path)
    try:
        subprocess.call(
            [
                "libreoffice",
                "--headless",
                "--convert-to",
                "pdf",
                input_file_path,
                "--outdir",
                output_dir,
            ]
        )
    except Exception as e:
        print(f"Lỗi khi chuyển đổi tệp: {e}")


def determine_values(dieu_kien_bao_quan):
    storage_conditions = {
        "0": " bảo quản ở điều kiện thường.",
        "1": r" bảo quản ở điều kiện thường, bảo quản lạnh.",
        "2": " bảo quản ở điều kiện thường, bảo quản mát, bảo quản lạnh.",
    }
    tulanh_values = {
        "1": "6&Tủ lạnh&Cái&01&Sử dụng tốt\\",
        "2": "6&Tủ lạnh&Cái&01&Sử dụng tốt\\",
    }
    if dieu_kien_bao_quan != "0":
        sotb = "6"
    else:
        sotb = "5"
    return (
        storage_conditions.get(dieu_kien_bao_quan, ""),
        tulanh_values.get(dieu_kien_bao_quan, ""),
        sotb,
    )


def replace_latex_hs(file_txt, variables, latex_file):
    os.chdir("/home/<USER>/Dropbox/hnd/latexall/mylatex")
    with open(
        f"/home/<USER>/Dropbox/hnd/latexall/source_latex/{file_txt}",
        "r",
        encoding="utf-8",
    ) as file:
        content = file.read()
    formatted_content = replace_variables(content, variables)
    with open(latex_file, "w", encoding="utf-8") as latex:
        latex.write(formatted_content)
    subprocess.check_call(["xelatex", latex_file])


def format_latex_hs(file_txt, variables, latex_file):
    os.chdir("/home/<USER>/Dropbox/hnd/latexall/mylatex")
    with open(
        f"/home/<USER>/Dropbox/hnd/latexall/source_latex/{file_txt}",
        "r",
        encoding="utf-8",
    ) as file:
        content = file.read()
    formatted_content = content.format(**variables)
    with open(latex_file, "w", encoding="utf-8") as latex:
        latex.write(formatted_content)
    try:
        subprocess.check_call(["pdflatex", latex_file])
    except:
        pass


def func1(values):
    default_val = {}
    assert "s" not in values["trinh do cm"], (
        "Có lỗi ở giá trị trình độ chuyên môn: phải bắt đầu bằng đại/trung/cao"
    )
    values["trinh do cm"] = values["trinh do cm"].replace(" dược", "")
    if values["trinh do cm"] == "Đại học":
        default_val["loai hinh"] = "Nhà thuốc"
        default_val["vi tri hanh nghe"] = "Nhà thuốc"
    else:
        default_val["loai hinh"] = "Quầy thuốc"
        default_val["vi tri hanh nghe"] = "Quầy thuốc"
    if values["so nhan vien"] != "0":
        for i in range(int(values["so nhan vien"])):
            dict = {
                "ten nhan vien": "",
                "trinh do cm": "Cao đẳng dược",
                "dia chi thuong tru": "",
                "so cchnd": "Chưa có CCHND",
                "vi tri hanh nghe": "Chưa có CCHND",
                "thoi gian dang ky lam viec": "08h - 17h00",
                "vi tri chuyen mon": "Nhân viên bán thuốc",
            }
            for key, value in dict.items():
                default_val[f"{key} {i + 1}"] = value
    return [], {}, [], default_val, list(default_val.keys()), values


def func2(values):
    os.chdir("/home/<USER>/Dropbox/hnd/latexall/source_latex")
    text = rf"""
\documentclass{{article}}
\usepackage[T5]{{fontenc}}
\usepackage{{parskip}}
\nonstopmode
\usepackage[fontsize=16pt]{{scrextend}}
\usepackage[a4paper,margin=2cm]{{geometry}}
\usepackage{{mathptmx}}
\usepackage{{graphicx}}
\usepackage[strict]{{changepage}}
\usepackage{{ulem}}
\usepackage[none]{{hyphenat}}
\renewcommand{{\ULdepth}}{{7pt}}
\renewcommand{{\ULthickness}}{{0.5pt}}
\usepackage{{setspace}}
\usepackage{{tabularray}}
\usepackage{{indentfirst}}
\setlength{{\parindent}}{{0cm}}
\setlength{{\parskip}}{{6pt}}
\thispagestyle{{empty}}
\begin{{document}}

\setstretch{{1}}



\begin{{center}} {{\Large
    SƠ ĐỒ {values["loai hinh"].upper()} {values["ten qt-nt"]}}}

Chiều dài: +dai+m\\
Chiều rộng: +rong+m\\


\end{{center}}
 
\vspace{{0.5cm}} 



\begin{{minipage}}{{\textwidth}}
\begin{{tblr}}{{width=1\linewidth,colspec={{X[1,l] X[1,l]}},
colsep=0pt,
rowsep=0pt}}
1: Khu ra lẻ  & 2: Thuốc không kê đơn\\
3: Thuốc kê đơn  & 4: Thực phẩm chức năng\\
5: Điều hòa & 6: Cửa ra vào\\
\end{{tblr}}
\end{{minipage}}




\begin{{center}}
\begin{{minipage}}{{0.7\linewidth}}
    \includegraphics[width=\linewidth]{{/home/<USER>/print.pdf}}
\end{{minipage}}
\end{{center}}
\end{{document}}
"""
    with open("/home/<USER>/Dropbox/hnd/latexall/khac/ban_ve.tex", "w") as f:
        f.write(text)
    hn = TelegramSend("hnd")
    path = f"/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pdf_documents/lam_ho_so/biangang-{values['ten qt-nt']}-{values['ten nguoi ptcm']}.pdf"
    hn.send_file_warp(path)  #
    copy_file_to_clip(path)
    print_file_by_printer("sop.pdf", "HAIMAT")
    print_file_by_printer("bia.pdf", "BIACUNG")
    send_notification("xong\nvẽ sơ đồ luôn")


def is_gnht(values):
    return values["pham vi kd"] == "3"


def is_tructhuoc(values):
    return len(values["co quan chu quan"]) > 6


def compile_hs_sop_sosach_biangang_normal(values):
    compile_sosach_biangang(values)
    format_latex_hs("/hs_mission_pyqt/gpp_sop.tex", values, "sop.tex")
    if values["noi nhan"] == "LAN":
        values["random_bold"] = random.choice(list_bold)
        values["random_tit"] = random.choice(list_tit)
    if values["loai hinh"] == "Nhà thuốc":
        replace_latex_hs("/hs_mission_pyqt/gpp_hsnt.tex", values, "hs.tex")
    else:
        replace_latex_hs("/hs_mission_pyqt/gpp_hsqt.tex", values, "hs.tex")


def creat_checklist_dmthuoc(values):
    if values["loai hinh"] == "Nhà thuốc":
        creat_checklist_nt()
        creat_dm_thuoc_nt()
    else:
        creat_checklist_qt()
        creat_dm_thuoc_qt()


def compile_sosach_biangang(values):
    format_latex_hs(
        "/hs_mission_pyqt/gpp_so_sach.tex",
        values,
        "sosach.tex",
    )

    format_latex_hs(
        "/hs_mission_pyqt/gpp_bia_ngang.tex",
        values,
        "biangang.tex",
    )


def compile_bia_dung(values):
    format_latex_hs("/hs_mission_pyqt/gpp_bia1.tex", values, "bia.tex")


def compile_hs_sop_sosach_biangang_gnht(values):
    replace_latex_hs(
        "/hs_mission_pyqt/gpp_hs_truc_thuoc_gnht.tex",
        values,
        "hs.tex",
    )

    format_latex_hs(
        "/hs_mission_pyqt/gpp_sop_truc_thuoc_gnht.tex",
        values,
        "sop.tex",
    )

    format_latex_hs(
        "/hs_mission_pyqt/gpp_so_sach_gnht.tex",
        values,
        "sosach.tex",
    )

    format_latex_hs(
        "/hs_mission_pyqt/gpp_bia_ngang_gnht.tex",
        values,
        "biangang.tex",
    )


def compile_hs_sop_tructhuoc(values):
    format_latex_hs(
        "/hs_mission_pyqt/gpp_hs_truc_thuoc.tex",
        values,
        "hs.tex",
    )

    format_latex_hs(
        "/hs_mission_pyqt/gpp_sop_truc_thuoc.tex",
        values,
        "sop.tex",
    )


def creat_dm_thuoc_qt():
    shutil.copy(
        f"{path}/dm thuoc qt.pdf",
        "dm thuoc.pdf",
    )


def creat_dm_thuoc_nt():
    shutil.copy(
        f"{path}/dm thuoc nha thuoc.pdf",
        "dm thuoc.pdf",
    )


def creat_checklist_qt():
    pdf1_path = f"{path}/body_checklist_qt.pdf"
    pdf2_path = "hs.pdf"
    chen_checklist_truoc_trangcuoi(pdf1_path, pdf2_path)


def creat_checklist_nt():
    checklist = f"{path}/body_checklist_nt.pdf"
    hs = "hs.pdf"
    chen_checklist_truoc_trangcuoi(checklist, hs)


def normal_form_hs(values):
    values["random_bold"] = r"\textbf"
    values["random_tit"] = r"\textbf"
    values["font"] = "times"
    values["bonus_ttb"] = ""
    values["left"] = "3"
    pagesop = [2, 8, 17, 23, 30, 42]
    values["tinh_trang"] = "Tốt"
    return pagesop


def random_form_hs(values):
    values["font"] = "helvet"
    values["left"] = "2"
    values["tinh_trang"] = "Sử dụng tốt"
    values["sotb"] = "07"
    pagesop = [2, 8, 17, 23, 30, 42]
    values["bonus_ttb"] = r"""6&Tài liệu tra cứu&Bộ&01&Mới\\
                7&Tài liệu quy chế&Bộ&01&Mới\\"""
    values["random_bold"] = random.choice(list_bold)
    values["random_tit"] = random.choice(list_tit)
    return pagesop
