#!/usr/bin/env python3
"""
Script để tự động kết nối ADB với thiết bị Android dựa trên thông tin phần cứng.
"""

import subprocess
import re
from typing import Optional, Dict, Tuple


def get_system_info() -> Dict[str, str]:
    """L<PERSON>y thông tin hệ thống để phân biệt máy tính."""
    info = {}

    # Lấy thông tin CPU
    try:
        with open("/proc/cpuinfo", "r") as f:
            cpu_info = f.read()
            model_name = re.search(r"model name\s*:\s*(.*)", cpu_info)
            if model_name:
                info["cpu"] = model_name.group(1).strip()
    except Exception:
        info["cpu"] = "unknown"

    # Lấy thông tin RAM
    try:
        with open("/proc/meminfo", "r") as f:
            mem_info = f.read()
            total_ram = re.search(r"MemTotal:\s*(\d+)\s*kB", mem_info)
            if total_ram:
                info["ram"] = f"{int(total_ram.group(1)) // 1024}GB"
    except Exception:
        info["ram"] = "unknown"

    return info


def identify_machine() -> str:
    """Xác định máy tính dựa trên thông tin phần cứng."""
    info = get_system_info()

    # Thêm thông tin máy của bạn vào đây
    machine_profiles = {
        "machine1": {
            "cpu": "Intel(R) Core(TM) i7-xxxx",  # Thay thế bằng CPU của máy 1
            "ram": "16GB",  # Thay thế bằng RAM của máy 1
        },
        "machine2": {
            "cpu": "Intel(R) Core(TM) i5-xxxx",  # Thay thế bằng CPU của máy 2
            "ram": "8GB",  # Thay thế bằng RAM của máy 2
        },
    }

    for machine, profile in machine_profiles.items():
        if (
            info["cpu"].startswith(profile["cpu"].split("-")[0])
            and info["ram"] == profile["ram"]
        ):
            return machine

    return "unknown"


def check_adb_connection(ip: str) -> bool:
    """Kiểm tra kết nối ADB với IP cụ thể."""
    try:
        result = subprocess.run(
            ["adb", "connect", f"{ip}:5555"], capture_output=True, text=True, check=True
        )
        return "connected" in result.stdout.lower()
    except subprocess.CalledProcessError:
        return False


def main() -> None:
    """Hàm chính để thực hiện kết nối ADB."""
    # Xác định máy tính
    machine = identify_machine()
    print(f"Thông tin hệ thống:")
    info = get_system_info()
    print(f"CPU: {info['cpu']}")
    print(f"RAM: {info['ram']}")
    print(f"Đã xác định: {machine}")

    # Thiết lập IP tương ứng với từng máy
    machine_ips = {"machine1": "*************", "machine2": "**********"}

    if machine in machine_ips:
        target_ip = machine_ips[machine]
        print(f"Đang thử kết nối với IP {target_ip}...")
        if check_adb_connection(target_ip):
            print(f"✅ Đã kết nối thành công với {target_ip}")
            return

    print("❌ Không thể xác định máy tính hoặc kết nối thất bại")


if __name__ == "__main__":
    main()
