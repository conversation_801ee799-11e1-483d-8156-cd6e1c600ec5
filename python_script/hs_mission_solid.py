import os
import re
import shutil
import sys
from abc import ABC, abstractmethod
from typing import Dict, List

import csv_load_and_export as dl
import pandas as pd
import solid_string_format as dt
from class_hoso import (
    compile_bia_dung,
    compile_hs_sop_sosach_biangang_gnht,
    compile_hs_sop_sosach_biangang_normal,
    compile_hs_sop_tructhuoc,
    compile_sosach_biangang,
    creat_checklist_dmthuoc,
    determine_values,
    is_gnht,
    is_tructhuoc,
    normal_form_hs,
    random_form_hs,
    tach_trang,
)
from god_class import (
    TelegramSend,
    TextProcess,
    copy_file_to_clip,
    get_bottom_values,
    get_current_date,
    get_dict_from_index_df,
    print_file_by_printer,
    send_notification,
    setup_logging,
    show_message,
    update_df_from_dict_by_index,
)
from create_snippets import creat_and_get
from solid_string_format import format_unix_style
from solid_text_process import SimpleTextFormatter
from top_most_get_text import input_dialog

loader = dl.CsvLoaderFactory()
df_gpp_all = loader.create_basic_loader().load_df("du_lieu_gpp_all")
df_cchnd = loader.create_basic_loader().load_df("cchnd", "so cchnd")
df_dshn = loader.create_basic_loader().load_df("dshn_duoc", "so cchnd")
df_cham_dut = loader.create_basic_loader().load_df("ds_da_cham_dut")
df_co_so_ban_le = loader.create_basic_loader().load_df("co_so_ban_le", "so cchnd")

pvkdqt = r"Mua và bán lẻ thuốc thuộc Danh mục thuốc thiết yếu và Danh mục thuốc không kê đơn (có bao gồm: thuốc dạng phối hợp có chứa dược chất gây nghiện; thuốc dạng phối hợp có chứa dược chất hướng thần; thuốc dạng phối hợp có chứa tiền chất; thuốc độc; thuốc trong danh mục thuốc, dược chất thuộc danh mục chất bị cấm sử dụng trong một số ngành, lĩnh vực), trừ vắc xin;"

pvkdnt = r"Mua thuốc để bán lẻ (có bao gồm: thuốc dạng phối hợp có chứa dược chất gây nghiện; thuốc dạng phối hợp có chứa dược chất hướng thần; thuốc dạng phối hợp có chứa tiền chất; thuốc độc; thuốc trong danh mục thuốc, dược chất thuộc danh mục chất bị cấm sử dụng trong một số ngành, lĩnh vực), trừ vắc xin;"


def backup_file(key, lists_file: list, backup_folder: str) -> None:
    for a_file in lists_file:
        base_name = os.path.basename(a_file)
        name, ext = os.path.splitext(base_name)
        backup_name = f"{name}-{key}{ext}"
        backup_path = os.path.join(backup_folder, backup_name)
        shutil.copy(a_file, backup_path)
        print(f"Backup of '{a_file}' saved as '{backup_name}' in '{backup_folder}'")


def move_hs_to_last_line(values):
    if values["so cchnd"] in df_co_so_ban_le.index:
        df_co_so_ban_le.drop(values["so cchnd"], inplace=True)
        df_co_so_ban_le.loc[values["so cchnd"]] = values
        df_co_so_ban_le.to_csv(
            "/home/<USER>/Dropbox/hnd/csv_source/co_so_ban_le.csv",
            index_label="so cchnd",
        )


class DataValidator(ABC):
    @abstractmethod
    def validate(self) -> bool:
        pass


class CCHNDDateValidator:
    def __init__(self, df_dshn: pd.DataFrame, df_cchnd: pd.DataFrame):
        self.df_dshn = df_dshn
        self.df_cchnd = df_cchnd

    def get_ngay_cap_cchnd(self, so_cchnd: str) -> str:
        if self._is_in_cchnd(so_cchnd):
            return get_bottom_values(self.df_cchnd, so_cchnd, "ngay qd")
        elif self._is_in_dshn_duoc(so_cchnd):
            return get_bottom_values(self.df_dshn, so_cchnd, "ngay cap cchnd")
        return ""

    def _is_in_dshn_duoc(self, so_cchnd: str) -> bool:
        return so_cchnd in self.df_dshn.index

    def _is_in_cchnd(self, so_cchnd: str) -> bool:
        return so_cchnd in self.df_cchnd.index


class CheckTrungTenTrongXa(DataValidator):
    def __init__(self, ten_cs: str, xa: str, df: pd.DataFrame, socc: str):
        self.ten_cs = ten_cs
        self.xa = xa
        self.df = df
        self.socc = socc

    def validate(self) -> bool:
        mask1 = self._check_name_match()
        mask2 = self._check_address_match()
        mask3 = self._check_different_socc()

        filtered_df = self.df[mask1 & mask2 & mask3]
        return len(filtered_df) > 0, filtered_df

    def _check_name_match(self) -> pd.Series:
        return (
            self.df["ten qt-nt"]
            .str.upper()
            .str.replace("QUẦY THUỐC ", "")
            .str.replace("NHÀ THUỐC ", "")
            == self.ten_cs.upper()
        )

    def _check_address_match(self) -> pd.Series:
        return (
            self.df["dia chi co so"]
            .str.lower()
            .str.contains(re.sub(r"\s+", " ", self.xa.lower()))
        )

    def _check_different_socc(self) -> pd.Series:
        return self.df["so cchnd"] != self.socc


class CheckDangDungTen(DataValidator):
    def __init__(self, socc: str, df_current: pd.DataFrame, df_dachamdut: pd.DataFrame):
        self.socc = socc
        self.df_current = df_current
        self.df_dachamdut = df_dachamdut

    def validate(self) -> Dict:
        result = {}
        if self.socc in self.df_current["so cchnd"].values:
            result["current"] = {
                "name": self._get_value(self.df_current, "ten qt-nt"),
                "phone": self._get_value(self.df_current, "so dt chu hs"),
            }
        if self.socc in self.df_dachamdut["so cchnd"].values:
            result["terminated"] = {
                "name": self._get_value(self.df_dachamdut, "ten qt-nt"),
                "date": self._get_value(self.df_dachamdut, "ngay qd"),
            }

        return result

    def _get_value(self, df: pd.DataFrame, column: str) -> str:
        return df.loc[df["so cchnd"] == self.socc, column].values[0]


class CheckNgayCapCchndActive(DataValidator):
    def __init__(self, validator: CCHNDDateValidator, so_cchnd: str):
        self.validator = validator
        self.so_cchnd = so_cchnd

    def validate(self) -> Dict:
        result = {}
        ngay_cap = self.validator.get_ngay_cap_cchnd(self.so_cchnd)
        if not ngay_cap:
            promt = "Nhập ngày cấp cchnd"
        else:
            promt = "Ngày cấp cchnd có đúng không"
        ngay_cap_check = input_dialog("Title", promt, ngay_cap)
        if not ngay_cap_check:
            send_notification("Kiểm tra lại so CCHND, ngày có vấn đề")
            sys.exit()
        result["ngay cap cchnd"] = ngay_cap_check
        return result


class ValidationManager:
    def __init__(self):
        self.validators: List[DataValidator] = []

    def add_validator(self, validator: DataValidator):
        self.validators.append(validator)

    def validate_all(self) -> Dict:
        results = {}
        for validator in self.validators:
            results[validator.__class__.__name__] = validator.validate()
        return results


class CCHNDStatusChecker:
    def __init__(self, socc: str, name: str, address: str):
        self.socc = socc
        self.name = name.replace("QUẦY THUỐC ", "").replace("NHÀ THUỐC ", "")
        self.xa = address[::-1].split(",")[2][::-1]
        self.tele = TelegramSend("hnd")
        self.validation_manager = ValidationManager()

    def check_status(self) -> None:
        # Thêm các validator liên quan đến status
        self.validation_manager.add_validator(
            CheckTrungTenTrongXa(self.name, self.xa, df_gpp_all, self.socc)
        )
        self.validation_manager.add_validator(
            CheckDangDungTen(self.socc, df_gpp_all, df_cham_dut)
        )

        checked_results = self.validation_manager.validate_all()
        self._process_duplicate_check(checked_results)
        self._process_cchnd_status(checked_results)

    def _process_duplicate_check(self, checked_results: dict) -> None:
        if checked_results["CheckTrungTenTrongXa"][0]:
            duplicate_df = checked_results["CheckTrungTenTrongXa"][1]
            self.tele.send_message_warp(
                f"Đã có cơ sở tên {self.name.upper()} ở cùng {self.xa}"
            )
            duplicate_df.to_csv("check_duplicate.csv", index=False)

    def _process_cchnd_status(self, checked_results: dict) -> None:
        cchnd_results = checked_results["CheckDangDungTen"]
        if "current" in cchnd_results:
            current = cchnd_results["current"]
            self.tele.send_message_warp(
                f"ĐANG ĐỨNG TÊN Ở {current['name']} SĐT: {current['phone']}"
            )

        if "terminated" in cchnd_results:
            terminated = cchnd_results["terminated"]
            send_notification(
                f"ĐÃ cham dut: {terminated['name']}\nngay qd: {terminated['date']}"
            )


class CCHNDDateChecker:
    def __init__(self, socc: str):
        self.socc = socc
        self.validation_manager = ValidationManager()
        self.validator = CCHNDDateValidator(df_dshn, df_cchnd)

    def check_and_get_date(self) -> str:
        self.validation_manager.add_validator(
            CheckNgayCapCchndActive(self.validator, self.socc)
        )
        checked_results = self.validation_manager.validate_all()
        return checked_results["CheckNgayCapCchndActive"]["ngay cap cchnd"]


@setup_logging("check_ten.log")
def check_dung_ten_and_trung_ten(values: dict) -> dict:
    # Bước 1: Kiểm tra trạng thái CCHND
    status_checker = CCHNDStatusChecker(
        values["so cchnd"], values["ten qt-nt"], values["dia chi co so"]
    )
    status_checker.check_status()


def check_and_get_ngay_cap_cchnd(result: dict) -> dict:
    # Bước 2: Kiểm tra và lấy ngày cấp CCHND
    date_checker = CCHNDDateChecker(result["so cchnd"])
    result["ngay cap cchnd"] = date_checker.check_and_get_date()

    return result


class CCHNDDataCollector:
    def __init__(self, df_cchnd, df_dshn):
        self.df_cchnd = df_cchnd
        self.df_dshn = df_dshn
        self.default_dict = self._create_base_default_dict()

    def _is_in_dshn_duoc(self, so_cchnd: str) -> bool:
        return so_cchnd in self.df_dshn.index

    def _is_in_cchnd(self, so_cchnd: str) -> bool:
        return so_cchnd in self.df_cchnd.index

    def _create_base_default_dict(self):
        """Tạo dictionary mặc định cơ bản"""
        return {
            "ten nguoi ptcm": "",
            "gioi tinh": "",
            "noi cap cchnd": "",
            "trinh do cm": "",
            "so dt chu hs": "",
            "dia chi thuong tru": "",
            "ngay cap cmnd": "",
            "noi cap cmnd": "Cục Cảnh sát Quản lý hành chính về trật tự xã hội",
            "so nhan vien": "0",
            "co nhan vien khong": "0",
            "pham vi kd": "default",
            "dieu kien bao quan": "0",
            "co quan chu quan": "",
            "dia chi co quan chu quan": "",
            "truc thuoc": "",
            "ten co so dang ky hn": "",
            "so tu thuoc": "05",
            "noi nhan": "LAN",
        }

    def _create_dict_from_hnd(self, dict_hnd):
        """Tạo dictionary từ dữ liệu HND"""
        return {
            "so dt chu hs": dict_hnd.get("so dt chu hs", ""),
            "co nhan vien khong": dict_hnd.get("co nhan vien khong", ""),
            "so nhan vien": dict_hnd.get("so nhan vien", "") or "0",
            "so tu thuoc": dict_hnd.get("so tu thuoc", "") or "05",
            "pham vi kd": "default",
            "dieu kien bao quan": "0",
            "ten co so dang ky hn": dict_hnd.get("ten co so dang ky hn", ""),
            "co quan chu quan": dict_hnd.get("co quan chu quan", ""),
            "dia chi co quan chu quan": dict_hnd.get("dia chi co quan chu quan", ""),
            "noi nhan": dict_hnd.get("noi nhan", "") or "LAN",
        }

    def _collect_existing_cchnd_data(self, so_cchnd: str):
        values_got = {}
        dict_cchnd = get_dict_from_index_df(self.df_cchnd, so_cchnd)
        values_got.update(
            {k: v for k, v in dict_cchnd.items() if k in self.default_dict and v}
        )
        values_got["noi cap cchnd"] = "Sở Y tế tỉnh Phú Thọ"
        dict_hnd = get_dict_from_index_df(self.df_dshn, so_cchnd)
        default_dict = self._create_dict_from_hnd(dict_hnd)
        return default_dict, values_got

    def _collect_new_cchnd_data(self, so_cchnd: str):
        if self._is_in_dshn_duoc(so_cchnd):
            dict_dshn = get_dict_from_index_df(self.df_dshn, so_cchnd)
            # loại bỏ các trường không cần update vi da co san
            list_fields_not_get = [
                "so dt chu hs",
                "ten nguoi ptcm",
                "dia chi thuong tru",
                "cmnd",
                "ngay sinh",
            ]
            self.default_dict.update(
                {
                    k: v
                    for k, v in dict_dshn.items()
                    if k in self.default_dict and (not k in list_fields_not_get and v)
                }
            )
            addition_dict = {
                k: "" for k, v in dict_dshn.items() if k in self.default_dict and not v
            }

            # luôn luon dat gia tri mac dinh cua pham vi kd va dieu kien bao quan
            self.default_dict.update(addition_dict)
            self.default_dict["pham vi kd"] = "default"
            self.default_dict["dieu kien bao quan"] = "0"
            self.default_dict["noi cap cmnd"] = (
                "Cục Cảnh sát Quản lý hành chính về trật tự xã hội"
            )
            list_to_add = ["ten qt-nt", "dia chi co so"]
            for item in list_to_add:
                if item not in self.default_dict:
                    self.default_dict = {item: ""} | self.default_dict
            values_got = {}
            return self.default_dict, values_got
        else:
            values_got = {}
            return self.default_dict, values_got

    def collect_cchnd_data(self, so_cchnd):
        if self._is_in_cchnd(so_cchnd):
            return self._collect_existing_cchnd_data(so_cchnd)
        else:
            return self._collect_new_cchnd_data(so_cchnd)


def collect_data(so_cchnd):
    collector = CCHNDDataCollector(df_cchnd, df_dshn)
    return collector.collect_cchnd_data(so_cchnd)


class UpdateNhanvien:
    def __init__(self, values):
        self.values = values

    def update_with_employees(self):
        self.values["co nhan vien khong"] = "1"
        default_values = {}
        for i in range(int(self.values["so nhan vien"])):
            values_dict_nv = {
                "ten nhan vien": "",
                "trinh do cm": "Cao đẳng dược",
                "dia chi thuong tru": "",
                "so cchnd": "Chưa có CCHND",
                "vi tri hanh nghe": "Chưa có CCHND",
                "thoi gian dang ky lam viec": "08h - 17h00",
                "vi tri chuyen mon": "Nhân viên bán thuốc",
            }
            for key, value in values_dict_nv.items():
                default_values[f"{key} {i + 1}"] = value
        values_nhan_vien = creat_and_get(default_values)
        self.values.update(values_nhan_vien)

    def update_without_employees(self):
        self.values["co nhan vien khong"] = "0"

    def update(self):
        if (
            self.values["so nhan vien"].isdigit()
            and int(self.values["so nhan vien"]) > 0
        ):
            self.update_with_employees()
        else:
            self.update_without_employees()
        return self.values


class UpdateNhansuAndGetPageHs:
    def __init__(self, values):
        self.values = values
        self.formatter = SimpleTextFormatter()

    def khong_nhan_vien(self):
        if "đại học" in self.values["trinh do lower"]:
            self.values["so cd-tc"] = ""
            self.values["so dai hoc"] = "- Số cán bộ là dược sỹ đại học trở lên: 01"
        else:
            self.values["so cd-tc"] = (
                "- Số cán bộ là dược sỹ cao đẳng, dược sỹ trung cấp: 01"
            )
            self.values["so dai hoc"] = ""
        self.values["nhansu"] = ""
        return [1, 4, 5]

    def co_nhan_vien(self):
        self.values["dsnhanvien"] = ""
        for i in range(int(self.values["so nhan vien"])):
            latex_line = "1&"
            list_nv = [
                "ten nhan vien",
                "trinh do cm",
                "dia chi thuong tru",
                "so cchnd",
                "vi tri hanh nghe",
                "thoi gian dang ky lam viec",
                "vi tri chuyen mon",
            ]
            list_real = [f"{gt} {i + 1}" for gt in list_nv]
            for gtt in list_real:
                if "so cchnd " in gtt:
                    self.values[gtt] = "{" + self.values[gtt].replace("/", r"\\/") + "}"
                elif "ten nhan vien " in gtt:
                    self.values[gtt] = self.values[gtt].title()
                latex_line += self.values[gtt] + " &"
            latex_line = latex_line.rstrip(" &") + r"\\"
            self.values["dsnhanvien"] += latex_line

        if self.values["trinh do lower"] == "đại học":
            self.values["so dai hoc"] = f"- số cán bộ là dược sỹ đại học trở lên: 0{
                1 + self.values['dsnhanvien'].count('đại học')
            }"
            self.values["so cd-tc"] = (
                f"- số cán bộ là dược sỹ cao đẳng, dược sỹ trung cấp: 0{
                    self.values['dsnhanvien'].count('cao đẳng')
                    + self.values['dsnhanvien'].count('trung cấp')
                }"
                if self.values["dsnhanvien"].count("cao đẳng")
                + self.values["dsnhanvien"].count("trung cấp")
                > 0
                else ""
            )
        else:
            self.values["so dai hoc"] = (
                f"- số cán bộ là dược sỹ đại học trở lên: 0{
                    self.values['dsnhanvien'].count('đại học')
                }"
                if self.values["dsnhanvien"].count("đại học") > 0
                else ""
            )
            self.values[
                "so cd-tc"
            ] = f"- số cán bộ là dược sỹ cao đẳng, dược sỹ trung cấp: 0{
                self.values['dsnhanvien'].count('cao đẳng')
                + self.values['dsnhanvien'].count('trung cấp')
                + 1
            }"

        if self.values["co quan chu quan"]:
            self.values["nhansu"] = self.formatter.format_content(
                "/home/<USER>/Dropbox/hnd/latexall/source_latex/hs_mission_pyqt/nhan_su_truc_thuoc.tex",
                self.values,
            )
        else:
            self.values["nhansu"] = self.formatter.format_content(
                "/home/<USER>/Dropbox/hnd/latexall/source_latex/hs_mission_pyqt/nhan_su_ko_truc_thuoc.tex",
                self.values,
            )
        return [1, 4, 5, 6]

    def process(self):
        if self.values["so nhan vien"] == "0" or self.values["so nhan vien"] == "":
            return self.khong_nhan_vien()
        else:
            return self.co_nhan_vien()


class RandomLan:
    def __init__(self, values):
        self.values = values

    def get_pages(self):
        if self.values["noi nhan"] == "LAN":
            pagesop = random_form_hs(self.values)
        else:
            pagesop = normal_form_hs(self.values)
        return pagesop


class CompileHsSopSosachBia:
    def __init__(self, values: dict):
        self.values = values

    def format(self):
        if is_tructhuoc(self.values):
            if is_gnht(self.values):
                compile_hs_sop_sosach_biangang_gnht(self.values)
            else:
                compile_hs_sop_tructhuoc(self.values)
                compile_sosach_biangang(self.values)
            show_message(
                "LƯU Ý", "CHECKLIST CHỈ CHO NHÀ THUỐC, NẾU QUẦY THUỐC THÌ PHẢI SỬA"
            )
        else:
            compile_hs_sop_sosach_biangang_normal(self.values)


class BackUp:
    def __init__(self, values: dict):
        self.values = values
        self.files = [
            "biangang.pdf",
            "sosach.pdf",
            "sop.pdf",
            "hs.pdf",
            "bia.pdf",
            "chu ky.pdf",
            "dm thuoc.pdf",
        ]
        self.backup_dir = (
            "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pdf_documents/lam_ho_so/"
        )

    def backup_files(self):
        key = f"{self.values['ten qt-nt']}-{self.values['ten nguoi ptcm']}"
        for file in self.files:
            backup_file(key, [file], self.backup_dir)


class SendTelegram:
    def __init__(self, values: dict):
        self.values = values
        self.hs_telegram = TelegramSend("hs")
        self.hnd_telegram = TelegramSend("hnd")

    def send(self):
        if self.values["noi nhan"] == "LAN":
            self._send_lan_telegram()
        self._send_hnd_telegram()

    def _send_lan_telegram(self):
        hashtag = format_unix_style(self.values["ten qt-nt"]).replace("_", "")
        file_path = f"/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pdf_documents/lam_ho_so/chu ky-{self.values['ten qt-nt']}-{self.values['ten nguoi ptcm']}.pdf"
        self.hs_telegram.send_file_warp(file_path)
        self.hs_telegram.send_message_warp(
            f"chữ ký #{hashtag},\n SỐ dien thoai: {self.values['so dt chu hs']}"
        )

    def _send_hnd_telegram(self):
        file_path = f"/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pdf_documents/lam_ho_so/biangang-{self.values['ten qt-nt']}-{self.values['ten nguoi ptcm']}.pdf"
        copy_file_to_clip(file_path)
        self.hnd_telegram.send_file_warp(file_path)


class UpdateValuesMore:
    def __init__(self, values: dict, so_cchnd: str):
        self.values = values
        self.so_cchnd = so_cchnd

    def update(self):
        self._update_loai_hinh()
        self._update_cchnd()
        self._update_dates()
        self._update_formats()
        self._update_truc_thuoc()
        self._format_strings()
        return self.values

    def _update_loai_hinh(self):
        if "đại học" in self.values["trinh do cm"].lower():
            self.values["loai hinh"] = "Nhà thuốc"
            self.values["vi tri hanh nghe"] = "Nhà thuốc"
        else:
            self.values["loai hinh"] = "Quầy thuốc"
            self.values["vi tri hanh nghe"] = "Quầy thuốc"

    def _update_cchnd(self):
        self.values["so cchnd"] = (
            self.so_cchnd if "-" in self.so_cchnd else self.so_cchnd + "/CCHND-SYT-VP"
        )

    def _update_dates(self):
        self.values["ngay"], self.values["thang"], self.values["nam"], _ = (
            get_current_date()
        )
        self.values["ngay_lam"] = (
            f"{self.values['ngay']}/{self.values['thang']}/{self.values['nam']}"
        )

    def _update_formats(self):
        self.values["loai_hinh_upper"] = self.values["loai hinh"].upper()
        self.values["trinh do lower"] = (
            self.values["trinh do cm"].lower().replace("dược", "")
        )
        self.values["ten qt-nt"] = (
            self.values["ten qt-nt"]
            .upper()
            .replace("NHÀ THUỐC", "")
            .replace("QUẦY THUỐC", "")
            .strip()
        )

    def _update_truc_thuoc(self):
        """Thiết lập giá trị truc thuoc dựa trên co quan chu quan"""
        if self.values["co quan chu quan"] and len(self.values["co quan chu quan"]) > 2:
            self.values["truc thuoc"] = self.values["co quan chu quan"]
        else:
            self.values["truc thuoc"] = "KHÔNG"

    def _format_strings(self):
        list_upper = ["ten qt-nt"]
        list_ngay = ["ngay cap cchnd", "ngay cap cmnd"]
        list_phone = ["so dt chu hs"]
        list_title = ["ten nguoi ptcm"]
        string_formatter = dt.DictStringFormatter(self.values)
        self.values = (
            string_formatter.apply_title(list_title)
            .apply_upper(list_upper)
            .apply_phone_format(list_phone)
            .apply_date_format(list_ngay)
            .get_result()
        )


class UpdatePvkd:
    def __init__(self, values: dict):
        self.values = values

    def update(self):
        if "dieu kien bao quan" not in self.values:
            self.values["dieu kien bao quan"] = "0"
        condition, self.values["tulanh"], self.values["sotb"] = determine_values(
            self.values["dieu kien bao quan"]
        )

        if self.values["pham vi kd"] == "default":
            self.values["pham vi kd"] = (
                pvkdqt if self.values["loai hinh"] == "Quầy thuốc" else pvkdnt
            )
        dict_dkbq = {
            "0": " bảo quản ở điều kiện thường.",
            "1": " bảo quản ở điều kiện thường, bảo quản lạnh.",
        }
        self.values["dieu kien bao quan"] = dict_dkbq[self.values["dieu kien bao quan"]]
        self.values["pham vi kd"] += self.values["dieu kien bao quan"]
        return self.values


class ExportToCsv:
    def __init__(self, values: dict, so_cchnd: str):
        self.values = values
        self.so_cchnd = so_cchnd

    def export(self):
        move_hs_to_last_line(self.values)
        update_df_from_dict_by_index(
            df_dshn, "dshn_duoc.csv", self.values, self.so_cchnd
        )
        update_df_from_dict_by_index(
            df_co_so_ban_le, "co_so_ban_le.csv", self.values, self.so_cchnd
        )
        #########################################################
        # Cập nhật ngày qd cho df_cchnd theo ngày cấp chuẩn với các cchnd cấp mà bị lệch ngày
        if self.so_cchnd in df_cchnd.index:
            self.values["ngay qd"] = self.values["ngay cap cchnd"]
            update_df_from_dict_by_index(
                df_cchnd, "cchnd.csv", self.values, self.so_cchnd
            )


@setup_logging("hs.log")
def main():
    nhap_dau_vao = {
        "so cchnd": "",
        "ten qt-nt": "",
        "dia chi co so": "",
        "Mã 4772": "",
        "Chủ hộ có phải người ptcm": "",
    }
    result = creat_and_get(nhap_dau_vao)
    if "" in result:
        return
    result = check_and_get_ngay_cap_cchnd(result)
    so_cchnd = result["so cchnd"]
    check_dung_ten_and_trung_ten(result)
    default_dict, values_got = collect_data(so_cchnd)
    values_got.update(result)
    list_not_update = [
        "dia chi thuong tru",
        "ngay cap cmnd",
        "noi cap cmnd",
        "ten qt-nt",
        "dia chi co so",
    ]

    # Sửa lại thành
    default_dict["chieu_dai"] = ""
    default_dict["chieu_rong"] = ""
    default_dict = {k: v for k, v in default_dict.items() if k not in list_not_update}
    default_dict["Co in khong"] = "co"
    default_dict["noi nhan"] = "LAN"

    values = creat_and_get(default_dict)
    # Xóa bỏ key 'so dt chu hs' khỏi values_got nếu tồn tại
    if "so dt chu hs" in values_got:
        del values_got["so dt chu hs"]
    values.update(values_got)

    updater = UpdateNhanvien(values)
    values = updater.update()

    updater = UpdateValuesMore(values, so_cchnd)
    values = updater.update()

    updater = UpdatePvkd(values)
    values = updater.update()

    update_nhan_su = UpdateNhansuAndGetPageHs(values)
    pagehs = update_nhan_su.process()

    text = TextProcess("hs_mission_pyqt/ban_ve")
    values["ten qt-nt-full"] = values["loai hinh"] + " " + values["ten qt-nt"].title()
    values["id_column"] = values["ten qt-nt"] + "-" + values["ten nguoi ptcm"]
    values["ve_so_do"] = "0"
    values["da_ghep"] = "0"

    text.format_text(values)
    shutil.copy(
        "/home/<USER>/Dropbox/hnd/latexall/mylatex/mylatex.tex",
        "/home/<USER>/Dropbox/hnd/latexall/mylatex/ban_ve.tex",
    )
    if values["noi nhan"].lower() == "son":
        values["ghi_chu"] = input_dialog("Title", "Thanh toán", "")

    export_csv = ExportToCsv(values, so_cchnd)
    export_csv.export()

    random_lan = RandomLan(values)
    pagesop = random_lan.get_pages()

    compiler = CompileHsSopSosachBia(values)
    compiler.format()

    creat_checklist_dmthuoc(values)
    compile_bia_dung(values)
    tach_trang(pagehs, pagesop)

    # TODO TẠO DM THUỐC QT HAY NT

    backup = BackUp(values)
    backup.backup_files()
    send_telegram = SendTelegram(values)
    send_telegram.send()

    if values["Co in khong"] == "co":
        print_file_by_printer("sop.pdf", "HAIMAT")
        print_file_by_printer("bia.pdf", "BIACUNG")
    send_notification("xong")


if __name__ == "__main__":
    main()
