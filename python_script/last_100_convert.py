from abc import ABC, abstractmethod
import pandas as pd
import os

os.chdir("/home/<USER>/Dropbox/hnd/csv_source")


def get_numbers_list(df: pd.DataFrame, column_name: str) -> list:
    num_rows = len(df)
    df_tail = df.tail(min(100, num_rows)).copy()
    df_tail.fillna("", inplace=True)
    return df_tail[column_name].astype(str).tolist()


# Interface cơ bản cho column
class IColumn(ABC):
    @abstractmethod
    def get_value(self, df: pd.DataFrame) -> tuple:
        pass


# Base class cho xử lý số đăng ký
class BaseRegistrationNumberColumn(IColumn):
    def __init__(self, column_name: str):
        self.column_name = column_name

    @abstractmethod
    def process_numbers(self, numbers_list: list) -> tuple:
        pass

    def get_value(self, df: pd.DataFrame) -> tuple:
        numbers_list = get_numbers_list(df, self.column_name)
        return self.process_numbers(numbers_list)


class GetSoDkkd(BaseRegistrationNumberColumn):
    def __init__(self):
        super().__init__("so dkkd")

    def process_numbers(self, numbers_list):
        numbers_list = [str(x) for x in numbers_list]
        so_dh = list(filter(lambda x: x.startswith("00"), numbers_list))
        so_tc_cd = [item for item in numbers_list if item not in so_dh and item != ""]

        if not so_dh or not so_tc_cd:
            return 0, 0

        max_nt = max(int(x.lstrip("0")) for x in so_dh)
        max_qt = max(int(x.lstrip("0")) for x in so_tc_cd)
        return max_nt, max_qt


class SoCchndColumn(BaseRegistrationNumberColumn):
    def __init__(self):
        super().__init__("so cchnd")

    def process_numbers(self, numbers_list):
        numbers_list = [str(x) for x in numbers_list]
        so_tc = list(filter(lambda x: x.startswith("00"), numbers_list))
        so_dh = [item for item in numbers_list if item not in so_tc and item != ""]
        max_tc = max(int(x.lstrip("0")) for x in so_tc)
        max_dh = max(int(x.lstrip("0")) for x in so_dh)
        return max_dh, max_tc


# Cách sử dụng
if __name__ == "__main__":
    # Đọc dữ liệu
    df_dkkd = pd.read_csv("dkkd.csv", dtype=str)
    df_cchnd = pd.read_csv("cchnd.csv", dtype=str)

    # Sử dụng
    print(GetSoDkkd().get_value(df_dkkd))
    print(SoCchndColumn().get_value(df_cchnd))
