import os
import sys

import pandas as pd


from god_class import TextProcess, dataframe_to_latex, get_current_date, show_message

os.chdir("/home/<USER>/Dropbox/hnd/csv_source")
data_path = "gdp.csv"
data = pd.read_csv(data_path, dtype="str", index_col="ma ho so")


def all_or_nothing(string, var):
    if var == "00":
        texts = ""
    else:
        texts = string.format(var)
    return texts


values = {}

values["ngay"], values["thang"], values["nam"], values["ngayqd"] = get_current_date()

# Tìm chỉ số thỏa điều kiện
index = data[
    (data["so gdp"].notnull())
    & (data["so qd"].isnull())
    & (data["loai cap"].str.contains("đầu"))
].index

# Tính toán ngày hết hạn mới
new_gdp_han_date = values["ngayqd"][:-4] + str(int(values["ngayqd"][-4:]) + 3)

# Cập nhật các cột "ngay het han gdp" và "ngay qd"
data.loc[index, "ngay het han gdp"] = new_gdp_han_date
data.loc[index, "ngay qd"] = values["ngayqd"]
data.loc[index, "so dkkd"] = data.loc[index, "so gdp"]
dsqd = data.loc[index]
if dsqd.empty:
    show_message(
        "Thong bao",
        "Xem lai xem da dien so gdp chua, khong co cong ty nao thoa man, xem lai dieu kien o dong 34",
    )
    sys.exit()


def replace_slash_with_par(column):
    return column.str.replace("/", r"\par/")


dsqd["ten nguoi ptcm"] = dsqd["ten nguoi ptcm"].str.title()
dsqd["so dkkd"] = dsqd["so dkkd"] + r"\par/ĐKKD-VP"
dsqd["so gdp"] = dsqd["so gdp"] + r"\par/GDP"
dsqd["so cchnd"] = dsqd["so cchnd"].apply(
    lambda x: x + "/CCHND-SYT-VP" if "/" not in x else x
)

columns_to_replace = ["so cchnd"]
dsqd[columns_to_replace] = dsqd[columns_to_replace].apply(replace_slash_with_par)
dsqd.reset_index(inplace=True)
dsqd.index += 1
dsqd["stt"] = dsqd.index
dsqd = dsqd[
    [
        "stt",
        "ten cong ty",
        "tru so",
        "ten nguoi ptcm",
        "so cchnd",
        "ngay cap cchnd",
        "noi cap cchnd",
        "so dkkd",
        "ngay qd",
        "pham vi kd",
        "so gdp",
        "ngay qd",
        "ngay het han gdp",
    ]
]
values["clip"] = dataframe_to_latex(dsqd)
values["clip"] = values["clip"].replace("SYT-VP", r"\par SYT-VP")
values["ten cong ty"] = data.loc[index, "ten cong ty"][0]
data.to_csv("gdp.csv", index=True, index_label="ma ho so")
text = TextProcess("gdp_qd_new")
text.format_text(values)
tieude = f"QUYẾT ĐỊNH Về việc cấp Giấy chứng nhận đạt Thực hành tốt phân phối thuốc và giấy chứng nhận đủ điều kiện kinh doanh dược: {values['ten cong ty']}"
text.auto_day_van_ban(tieude, "QĐ", 0)
