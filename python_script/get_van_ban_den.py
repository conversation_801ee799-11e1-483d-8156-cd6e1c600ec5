from datetime import datetime

import demjson3 as demjson
import pandas as pd
import requests

from god_class import send_notification


def run_main():
    url = "https://qlvb-api.vinhphuc.gov.vn/api/van-ban-den/danh-sach-van-ban-den-theo-trang-thai-cua-chuyen-vien"
    year_now = datetime.now().year

    payload = f"co_tep_tin=-1&ma_can_bo=27683&ma_ctcb_nhan=21365&ma_don_vi_quan_tri=1187&ma_loai_ttdh=0&ma_yeu_cau=0&nam=0&nhan_den_ngay=31%2F12%2F{year_now}&nhan_tu_ngay=01%2F01%2F{year_now}&page=1&size=4000&trang_thai_ttdh_gui=-1&trang_thai_xu_ly=0"
    headers = {
        "Authorization": "Bearer acKKZDmXqpUmn7D3vuzwk88RN909S7Soc5YlXQhyV6I9lU6UNYaTHtnVEhzxoo6Pul1K2NroQeS6584FZDucIYETRjjn70alDIbOUQ4V3zt8JarwIvOhqZ+z96uFvFuZjsu03k3l5Ou0DhYlEmlQ/15duefP7Ozfa3bfIo3bASLX3cOwnkpUl/pQoUl8aw6UA7KwPJDjnXYn/NR0nW7uuYcPT7ic/DdhkiE08Us6bbSjqfTbHGdR8fC8Nza/I52dVn4RLZDAGfVFhFgZS6qw2j2VTpQ1hpMelekgyrg23fFO2qhb81RllfgYPTifzG9j",
        "Connection": "keep-alive",
        "Content-Type": "application/x-www-form-urlencoded",
    }
    response = requests.post(url, headers=headers, data=payload)
    res = demjson.decode(response.text)

    if "Không tìm thấy dữ liệu" in res["message"]:
        print("Không tìm thấy dữ liệu")
        return

    row_list = []
    for item in res["data"]:
        col1 = item["ten_co_quan_ban_hanh"]
        col2 = item["trich_yeu"]
        col3 = item["so_ky_hieu"]
        col4 = format_date(item["ngay_ban_hanh"])
        col5 = item["but_phe_cb_duyet"]
        col6 = f"https://iqlvb.vinhphuc.gov.vn/van-ban-den/xem-van-ban-den-chi-tiet?id={int(item['ma_van_ban_den_kc'])}&t=vb_den_chua_xu_ly_cua_cv&v=cv&xld={int(item['ma_xu_ly_den'])}"
        row = [col1, col2, col3, col4, col5, col6]
        row_list.append(row)
    df = pd.DataFrame(
        row_list,
        columns=[
            "noi_gui_vb",
            "trich_yeu",
            "so_kh",
            "ngay_ban_hanh",
            "noi_dung",
            "link",
        ],
    )
    df.to_csv("/home/<USER>/Dropbox/hnd/csv_source/vanbanden.csv", index=False)
    send_notification(f"đã lấy được {len(df)} văn bản đến")


def format_date(input_date):
    try:
        # Chuyển đổi ngày tháng từ định dạng ban đầu
        input_date = datetime.strptime(input_date, "%Y-%m-%dT%H:%M:%S")
        # Định dạng lại theo "%d/%m/%Y"
        formatted_date = input_date.strftime("%d/%m/%Y")
        return formatted_date
    except ValueError:
        return "Invalid date format"


if __name__ == "__main__":
    run_main()
