{"cells": [{"cell_type": "code", "execution_count": 53, "id": "9e8e8122782dad35", "metadata": {}, "outputs": [], "source": ["import pandas as pd "]}, {"cell_type": "code", "execution_count": 54, "id": "cf0a155d", "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "os.chdir(\"/home/<USER>/Dropbox/hnd/csv_source\")"]}, {"cell_type": "code", "execution_count": 55, "id": "484fe673", "metadata": {}, "outputs": [], "source": ["df_all=pd.read_csv('/home/<USER>/Dropbox/hnd/csv_source/du_lieu_gpp_all.csv',dtype=str)"]}, {"cell_type": "code", "execution_count": 56, "id": "3f7ed4cf", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_316437/99636595.py:1: FutureWarning: A value is trying to be set on a copy of a DataFrame or Series through chained assignment using an inplace method.\n", "The behavior will change in pandas 3.0. This inplace method will never work because the intermediate object on which we are setting values always behaves as a copy.\n", "\n", "For example, when doing 'df[col].method(value, inplace=True)', try using 'df.method({col: value}, inplace=True)' or df[col] = df[col].method(value) instead, to perform the operation inplace on the original object.\n", "\n", "\n", "  df_all['co quan chu quan'].fillna('0',inplace=True)\n"]}], "source": ["df_all['co quan chu quan'].fillna('0',inplace=True)"]}, {"cell_type": "code", "execution_count": 57, "id": "7b880817", "metadata": {}, "outputs": [], "source": ["df_tt=df_all[df_all['co quan chu quan']!='0']"]}, {"cell_type": "code", "execution_count": 58, "id": "98f9e69e", "metadata": {}, "outputs": [{"data": {"text/plain": ["(52, 19)"]}, "execution_count": 58, "metadata": {}, "output_type": "execute_result"}], "source": ["df_tt.shape"]}, {"cell_type": "code", "execution_count": 59, "id": "6fb82411", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_316437/2834056041.py:1: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  df_tt['ten nguoi ptcm']=df_tt['ten nguoi ptcm'].str.title()\n", "/tmp/ipykernel_316437/2834056041.py:2: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  df_tt['ten qt-nt']=df_tt['ten qt-nt'].str.title().str.replace('Thuốc', 'thuốc')\n"]}], "source": ["df_tt['ten nguoi ptcm']=df_tt['ten nguoi ptcm'].str.title()\n", "df_tt['ten qt-nt']=df_tt['ten qt-nt'].str.title().str.replace('Thuốc', 'thuốc')"]}, {"cell_type": "code", "execution_count": 60, "id": "d32e25aa", "metadata": {}, "outputs": [], "source": ["df_tt.to_csv('co_so_ban_le_truc_thuoc.csv')"]}, {"cell_type": "code", "execution_count": 61, "id": "db37d50f", "metadata": {}, "outputs": [], "source": ["df2=pd.read_csv('co_so_ban_le_truc_thuoc.csv')\n", "df2 = df2[\n", "    [\n", "        [\n", "            \"ten qt-nt\",\n", "            \"dia chi co so\",\n", "            \"co quan chu quan\",\n", "            \"dia chi co quan chu quan\",\n", "            \"ten nguoi ptcm\",\n", "            \"so dt chu hs\",\n", "        ]\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": 64, "id": "9051fbd6", "metadata": {}, "outputs": [{"ename": "ModuleNotFoundError", "evalue": "No module named 'god_class'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mModuleNotFoundError\u001b[0m                       <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[64], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mgod_class\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m insert_stt\n", "\u001b[0;31mModuleNotFoundError\u001b[0m: No module named 'god_class'"]}], "source": ["\n", "from god_class import insert_stt"]}, {"cell_type": "code", "execution_count": null, "id": "5125cf9b", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "python313", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.1"}}, "nbformat": 4, "nbformat_minor": 5}