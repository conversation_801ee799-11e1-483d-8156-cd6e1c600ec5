#!/usr/bin/env python3
"""
Script để di chuyển cửa sổ MuPDF sang sát cạnh ngoài cùng bên phải của màn hình.
Sử dụng wmctrl để điều khiển cửa sổ.
"""

import subprocess
from typing import Optional, <PERSON><PERSON>


def get_screen_resolution() -> Tuple[int, int]:
    """
    Lấy độ phân giải màn hình hiện tại.

    Returns:
        Tuple[int, int]: <PERSON><PERSON> phân giải màn hình (width, height)
    """
    try:
        output = subprocess.check_output(["xrandr"]).decode("utf-8")
        for line in output.split("\n"):
            if "*" in line:  # Dòng chứa độ phân giải hiện tại
                resolution = line.split()[0]
                width, height = map(int, resolution.split("x"))
                return width, height
    except subprocess.CalledProcessError as e:
        print(f"Lỗi khi lấy độ phân giải màn hình: {e}")
        return 1920, 1080  # Gi<PERSON> trị mặc định nếu không lấy đ<PERSON>


def get_window_id(class_name: str) -> Optional[str]:
    """
    Lấy ID của cửa sổ dựa trên class name.

    Args:
        class_name (str): Tên class của cửa sổ cần tìm

    Returns:
        Optional[str]: ID của cửa sổ hoặc None nếu không tìm thấy
    """
    try:
        output = subprocess.check_output(["wmctrl", "-l", "-x"]).decode("utf-8")
        for line in output.split("\n"):
            if class_name.lower() in line.lower():
                return line.split()[0]
    except subprocess.CalledProcessError as e:
        print(f"Lỗi khi tìm cửa sổ: {e}")
    return None


def move_window_to_right(window_id: str, screen_width: int, screen_height: int) -> None:
    """
    Di chuyển cửa sổ sang sát cạnh phải của màn hình.

    Args:
        window_id (str): ID của cửa sổ
        screen_width (int): Chiều rộng màn hình
        screen_height (int): Chiều cao màn hình
    """
    try:
        # Lấy thông tin cửa sổ hiện tại
        output = subprocess.check_output(["wmctrl", "-l", "-G"]).decode("utf-8")
        for line in output.split("\n"):
            if window_id in line:
                parts = line.split()
                if len(parts) >= 7:
                    # Tính toán vị trí mới
                    x = screen_width - int(parts[5])  # Đặt cửa sổ sát cạnh phải
                    y = 0  # Đặt ở trên cùng
                    width = int(parts[5])  # Giữ nguyên chiều rộng
                    height = screen_height  # Chiều cao bằng màn hình

                    # Di chuyển cửa sổ
                    subprocess.run(
                        [
                            "wmctrl",
                            "-i",
                            "-r",
                            window_id,
                            "-e",
                            f"0,{x},{y},{width},{height}",
                        ]
                    )
                    break
    except subprocess.CalledProcessError as e:
        print(f"Lỗi khi di chuyển cửa sổ: {e}")


def main() -> None:
    """Hàm chính thực thi chương trình."""
    class_name = "MuPDF"

    # Lấy ID cửa sổ
    window_id = get_window_id(class_name)
    if not window_id:
        print(f"Không tìm thấy cửa sổ có class name: {class_name}")
        return

    # Lấy độ phân giải màn hình
    screen_width, screen_height = get_screen_resolution()

    # Di chuyển cửa sổ
    move_window_to_right(window_id, screen_width, screen_height)
    print(f"Đã di chuyển cửa sổ {class_name} sang cạnh phải màn hình")


if __name__ == "__main__":
    main()
