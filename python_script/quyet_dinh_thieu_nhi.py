import os
import pandas as pd
from god_class import dataframe_to_latex, TextProcess, insert_stt, send_notification

from datetime import datetime


def number_to_vietnamese(number: int) -> str:
    """
    Chuyển đổi số thành chữ tiếng Việt.

    Args:
        number: Số nguyên cần chuyển đổi

    Returns:
        Chuỗi số bằng chữ tiếng Việt
    """
    if number == 0:
        return "không"

    # Các từ cơ bản
    ones = ["", "một", "hai", "ba", "bốn", "năm", "sáu", "bảy", "tám", "chín"]
    tens = [
        "",
        "",
        "hai mươi",
        "ba mươi",
        "bốn mươi",
        "năm mươi",
        "sáu mươi",
        "bảy mươ<PERSON>",
        "tám mươi",
        "ch<PERSON> mươ<PERSON>",
    ]

    def convert_hundreds(n: int) -> str:
        """Chuyển đổi số từ 0-999 thành chữ."""
        if n == 0:
            return ""

        result = ""

        # Hàng trăm
        if n >= 100:
            hundreds_digit = n // 100
            result += ones[hundreds_digit] + " trăm"
            n %= 100
            if n > 0:
                result += " "

        # Hàng chục và đơn vị
        if n >= 20:
            tens_digit = n // 10
            result += tens[tens_digit]
            n %= 10
            if n > 0:
                if n == 1:
                    result += " một"
                elif n == 5 and tens_digit > 1:
                    result += " lăm"
                else:
                    result += " " + ones[n]
        elif n >= 10:
            result += "mười"
            n %= 10
            if n > 0:
                if n == 5:
                    result += " lăm"
                else:
                    result += " " + ones[n]
        elif n > 0:
            result += ones[n]

        return result

    def convert_group(n: int) -> str:
        """Chuyển đổi nhóm 3 chữ số."""
        if n == 0:
            return ""

        result = convert_hundreds(n)

        # Xử lý trường hợp đặc biệt cho số lẻ
        if n < 10 and n > 0:
            return result
        elif n < 100 and n >= 10:
            return result
        else:
            return result

    if number < 0:
        return "âm " + number_to_vietnamese(-number)

    # Xử lý các nhóm số
    groups = []
    group_names = ["", "nghìn", "triệu", "tỷ"]
    group_index = 0

    while number > 0 and group_index < len(group_names):
        group = number % 1000
        if group > 0:
            group_text = convert_group(group)
            if group_index > 0:
                group_text += " " + group_names[group_index]
            groups.append(group_text)

        number //= 1000
        group_index += 1

    # Ghép các nhóm lại
    result = " ".join(reversed(groups))

    # Làm sạch kết quả
    result = " ".join(result.split())  # Loại bỏ khoảng trắng thừa

    return result


df = pd.read_csv("/home/<USER>/Dropbox/hnd/csv_source/thieunhi2025.csv")
# Lấy năm hiện tại
nam_hien_tai = datetime.now().year

# Kiểm tra tuổi của các cháu
df["tuoi"] = nam_hien_tai - df["NĂM SINH"].astype(int)
mask = df["tuoi"] > 15
chau_tren_15 = df[mask]

if len(chau_tren_15) > 0:
    message = f"Có {len(chau_tren_15)} cháu trên 15 tuổi:\n"
    for _, row in chau_tren_15.iterrows():
        message += (
            f"- {row['HỌ TÊN CON']}: {row['tuoi']} tuổi (sinh năm {row['NĂM SINH']})\n"
        )
    send_notification(message, True)
df = df[~mask]
df = df.drop(columns=["Ký nhận", "STT", "tuoi"])
insert_stt(df)


values = {}
values["danh_sach"] = dataframe_to_latex(df)
values["so_chau"] = len(df)
values["so_tien"] = len(df) * 300000
values["so_tien"] = f"{values['so_tien']:,}".replace(",", ".")
values["bang_chu"] = (
    number_to_vietnamese(int(values["so_tien"].replace(".", ""))) + " đồng chẵn"
)

te = TextProcess("qd-1-6")
te.format_text(values)
