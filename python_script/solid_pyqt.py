from PyQt5.QtWidgets import (
    QApplication,
    QWidget,
    QLineEdit,
    QVBoxLayout,
    QPushButton,
    QFormLayout,
    QLabel,
    QScrollArea,
)
from PyQt5.QtCore import Qt
import sys


class DynamicFormWidget(QWidget):
    """Widget chứa form động được tạo từ danh sách và từ điển"""

    def __init__(self, default_fields=None):
        super().__init__()
        self.default_fields = default_fields or {}
        self.inputs = {}
        self._init_ui()

    def _init_ui(self):
        """Khởi tạo giao diện"""
        form_layout = QFormLayout()

        # Tạo các trường input với font size 14
        for field in self.default_fields:
            input_field = QLineEdit()
            input_field.setText(str(self.default_fields[field]))
            # Thiết lập font size 14
            font = input_field.font()
            font.setPointSize(13)
            input_field.setFont(font)
            self.inputs[field] = input_field

            # Tạo label với font size 14
            label = QLabel(field)
            label.setFont(font)
            form_layout.addRow(label, input_field)

        self.setLayout(form_layout)


class FormWindow(QWidget):
    """Cửa sổ chính chứa form và các nút điều khiển"""

    def __init__(self, default_fields=None, width=600, height=600):
        super().__init__()
        self.form = DynamicFormWidget(default_fields)
        self.width = width
        self.height = height
        self.submitted = False
        self._init_ui()

    def _init_ui(self):
        """Khởi tạo giao diện cửa sổ chính"""
        main_layout = QVBoxLayout()

        # Tạo scroll area
        scroll = QScrollArea()
        scroll.setWidget(self.form)
        scroll.setWidgetResizable(True)
        scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # Thêm scroll area vào layout chính
        main_layout.addWidget(scroll)

        submit_btn = QPushButton("Submit")
        # Thiết lập font size 14 cho nút Submit
        font = submit_btn.font()
        font.setPointSize(13)
        submit_btn.setFont(font)

        submit_btn.clicked.connect(self._on_submit)
        main_layout.addWidget(submit_btn)

        self.setLayout(main_layout)
        self.setWindowTitle("Dynamic Form")
        self.setGeometry(300, 300, self.width, self.height)

        # Set window to always on top
        self.setWindowFlags(self.windowFlags() | Qt.WindowStaysOnTopHint)

        # Thêm xử lý sự kiện Enter cho mỗi QLineEdit trong form
        for input_field in self.form.inputs.values():
            input_field.returnPressed.connect(self._on_submit)

    def showEvent(self, event):
        """Xử lý sự kiện khi cửa sổ được hiển thị"""
        super().showEvent(event)
        # Focus vào ô input đầu tiên nếu có
        if self.form.inputs:
            first_input = next(iter(self.form.inputs.values()))
            first_input.setFocus()

    def keyPressEvent(self, event):
        """Xử lý sự kiện bàn phím cho cả form"""
        if event.key() == Qt.Key_Return or event.key() == Qt.Key_Enter:
            self._on_submit()
        else:
            super().keyPressEvent(event)

    def _on_submit(self):
        """Xử lý sự kiện khi nhấn Submit hoặc Enter"""
        self.submitted = True  # Đánh dấu là đã submit
        self.close()

    def get_form_data(self):
        """Lấy dữ liệu từ form dưới dạng dictionary"""
        return {
            field: input_field.text() for field, input_field in self.form.inputs.items()
        }

    def closeEvent(self, event):
        """Xử lý sự kiện đóng cửa sổ"""
        if not self.submitted:  # Nếu form chưa được submit
            sys.exit()
        event.accept()


class DynamicFormManager:
    _app = None  # Biến static để lưu QApplication

    def __init__(self, default_fields=None, width=400, height=100):
        self.default_fields = default_fields
        self.width = width
        self.height = height
        self.window = None

    def show_form(self):
        """Hiển thị form và trả về dữ liệu"""
        if DynamicFormManager._app is None:
            DynamicFormManager._app = QApplication(sys.argv)

        self.window = FormWindow(
            self.default_fields,
            self.width,
            self.height,
        )
        self.window.show()
        DynamicFormManager._app.exec_()

        # Kiểm tra xem form đã được submit hay chưa
        if not self.window.submitted:
            sys.exit()

        return self.window.get_form_data()


def creat_and_get(default_fields=None, width=600, height=500):
    """Hàm tiện ích để lấy input từ người dùng"""
    form_manager = DynamicFormManager(
        default_fields=default_fields,
        width=width,
        height=height,
    )
    return form_manager.show_form()


# Ví dụ sử dụng:
if __name__ == "__main__":
    # Form đầu tiên để lấy thông tin cá nhân
    default_fields_1 = {
        "Họ tên": "Nguyễn Văn A",
        "Tuổi": "25",
        "Email": "<EMAIL>",
        "Địa chỉ": "",
    }

    # Lấy dữ liệu từ form thứ nhất
    result_1 = creat_and_get(
        default_fields_1,
        width=400,
        height=300,
    )

    # Form thứ hai với dữ liệu từ form 1
    default_fields_2 = {
        "Tên người liên hệ": result_1["Họ tên"],
        "Quan hệ": "",
        "Số điện thoại liên hệ": "",
        "Địa chỉ liên hệ": "",
        "Ghi chú": f"Liên hệ cho người {result_1['Tuổi']} tuổi",
    }

    # Lấy dữ liệu từ form thứ hai
    result_2 = creat_and_get(
        default_fields=default_fields_2,
        width=500,
        height=400,
    )

    # Kết hợp kết quả từ cả hai form
    final_result = {"Thông tin cá nhân": result_1, "Thông tin liên hệ": result_2}

    # In kết quả
