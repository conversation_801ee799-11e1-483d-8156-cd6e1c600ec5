import datetime as dt
import os
import subprocess
import sys

import pandas as pd

from top_most_get_text import input_dialog

os.chdir("/home/<USER>/Dropbox/LÊ DIỄM/")


def dataframe_to_latex(df):
    latex_str = ""
    for _, row in df.iterrows():
        row_str = " & ".join(map(str, row.values))
        row_str += " \\\\"
        latex_str += row_str + "\n"
    return latex_str


def get_lastest_excel_file_in_folder(folder_path):
    # Get all the Excel files in the folder
    excel_files = [f for f in os.listdir(folder_path) if f.endswith(".xlsx")]

    # Sort the list of Excel files by modified date
    excel_files.sort(key=lambda x: os.path.getmtime(os.path.join(folder_path, x)))

    # Return the full path of the latest Excel file
    return os.path.join(folder_path, excel_files[-1])


def get_next_week_number():
    # <PERSON><PERSON><PERSON> bắt đầu của năm
    start_date = dt.date(2024, 1, 1)  # Thay đổi năm tương ứng với năm hiện tại

    # <PERSON><PERSON><PERSON> bắt đầu của tuần tiếp theo
    today = dt.date.today()
    days_to_next_week = (7 - today.weekday()) % 7 + 1
    next_week_start_date = today + dt.timedelta(days=days_to_next_week)

    # Thứ tự của tuần tiếp theo trong năm
    week_number = (next_week_start_date - start_date).days // 7 

    return week_number


def get_nearest_weekday():
    today = dt.date.today()
    one_day = dt.timedelta(days=1)
    mon_day = today + one_day * ((7 - today.weekday()) % 7)
    return (
        mon_day,
        mon_day + dt.timedelta(days=1),
        mon_day + dt.timedelta(days=2),
        mon_day + dt.timedelta(days=3),
        mon_day + dt.timedelta(days=4),
    )


def convert_ngay(ngay):
    if len(ngay) > 1:
        if "/" not in ngay:
            if str(ngay)[2:4] in ["01", "02", "10", "11", "12"]:
                a = str(ngay)[2:4]
            else:
                a = str(ngay)[3:4]
            ngay = str(ngay)[:2] + "/" + a + "/" + str(ngay)[-4:]
    else:
        ngay = ""
    return ngay


monday, tuesday, wedday, thursday, friday = get_nearest_weekday()
ngaykts = monday + dt.timedelta(days=4)
ngayky = convert_ngay(monday.strftime("%d%m%Y"))
ngaykts = convert_ngay(ngaykts.strftime("%d%m%Y"))


def find_first_data_row(dframe, column):
    return next((i for i, row in dframe.iterrows() if pd.notnull(row[column])), None)


file_path = "/home/<USER>/Dropbox/LÊ DIỄM/lbg.xlsx"
xls = pd.ExcelFile(file_path)
rightmost_sheet_name = xls.sheet_names[-1]

# Read only the first few rows to find the header
df_temp = xls.parse(sheet_name=rightmost_sheet_name, header=None, nrows=10)
header_row = find_first_data_row(df_temp, 2)

# Now read the full data starting from the header row
df = xls.parse(sheet_name=rightmost_sheet_name, dtype=str, header=header_row)
df = df.dropna(subset=["Môn"]).reset_index(drop=True).iloc[:, :7]
def send_notification(notify):
    print(notify)
    notify='@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@\n\n'+notify
    notify_command = ['notify-send', notify]
    subprocess.run(notify_command)

def fill_down(dframe, columns):
    dframe[columns] = dframe[columns].fillna(method="ffill")
    return dframe


df_fill = fill_down(df, ["Thứ", "Buổi"])


def replace_repeated_values(dframe):
    for col in dframe.columns:
        if col == "Thứ" or col == "Buổi":
            i = 0
            while i < len(dframe):
                count = 1
                while (
                    (i + count < len(dframe))
                    and (dframe.loc[i, col] == dframe.loc[i + count, col])
                    and (dframe.loc[i, col] != "")
                ):
                    count += 1
                if count > 1:
                    dframe.loc[i, col] = (
                        f"\\SetCell[r={count}]{{c}} {dframe.loc[i, col]}"
                    )
                i += count
    return dframe


df_fill.loc[df_fill["Thứ"].str.contains("Hai"), "Thứ"] = "Hai\n" + monday.strftime(
    "%d/%m"
)
df_fill.loc[df_fill["Thứ"].str.contains("Ba"), "Thứ"] = "Ba\n" + tuesday.strftime(
    "%d/%m"
)
df_fill.loc[df_fill["Thứ"].str.contains("Tư"), "Thứ"] = "Tư\n" + wedday.strftime(
    "%d/%m"
)
df_fill.loc[df_fill["Thứ"].str.contains("Năm"), "Thứ"] = "Năm\n" + thursday.strftime(
    "%d/%m"
)
df_fill.loc[df_fill["Thứ"].str.contains("Sáu"), "Thứ"] = "Sáu\n" + friday.strftime(
    "%d/%m"
)
df_fill2 = replace_repeated_values(df_fill)


def replace_non_setcell_values(df, columns):
    for col in columns:
        df[col] = df[col].apply(lambda x: "" if "\\SetCell" not in str(x) else x)
    return df


index_val = input_dialog("NHẬP", "NHẬP GIÃN DÒNG", "0.85")
if not index_val:
    sys.exit()
tut = input_dialog("NHẬP", "NHẬP SỐ TUẦN GIẢM ĐI", "0")
df_fill2 = replace_non_setcell_values(df_fill2, ["Thứ", "Buổi"])
df_fill2.fillna("", inplace=True)
clip = dataframe_to_latex(df_fill2).replace("máy", "Máy")
tuan = str(get_next_week_number() - 35 - int(tut)).zfill(2)
ngaybd = ngayky.split("/")[0]
thangbd = ngayky.split("/")[1]
ngaykt = ngaykts.split("/")[0]
thangkt = ngaykts.split("/")[1]
nam = ngayky.split("/")[2]

text = rf"""\documentclass{{article}}
\usepackage[T5]{{fontenc}}
\nonstopmode
\usepackage[fontsize=14pt]{{scrextend}}
\usepackage[a4paper,vmargin=0.7cm,hmargin=1cm]{{geometry}}
\usepackage{{mathptmx}}
\usepackage{{setspace}}
\usepackage{{tabularray}}
\pagenumbering{{gobble}}
\begin{{document}}


\begin{{center}}

    \textbf{{TUẦN {tuan}}}

\textit{{Từ ngày {ngaybd} tháng {thangbd} đến ngày {ngaykt} tháng {thangkt} năm {nam}}}

\end{{center}}

\setstretch{{{index_val}}}

 \vspace{{-0.2cm}}
\noindent
\begin{{minipage}}{{\textwidth}}
\begin{{tblr}}{{width=1\linewidth,rowhead=1,hlines,vlines,
colspec={{X[0.3,c]X[0.3,c]X[0.3,c]X[0.7,c]X[2.8,c]X[1.1,c]X[0.3,c]}},
colsep=1pt,
rowsep=0.5pt,
rows={{font=\footnotesize,m}},
row{{1}}={{font=\footnotesize\bfseries}}}}
Thứ&Buổi&Tiết&Môn&Tên bài dạy&Tên thiết bị - đồ dùng dạy học&Ghi chú\\
{clip}
\end{{tblr}}
\end{{minipage}}
 
\noindent
\begin{{minipage}}{{\textwidth}}
\begin{{tblr}}{{width=1\linewidth,
colspec={{X[1.3,c] X[1,c]}},
colsep=1pt,
rowsep=1pt,
rows={{font=\small,m}}}}
&\textit{{Duyệt ngày  \hspace{{1cm}}  tháng  \hspace{{1cm}}  năm \hspace{{1cm}}}}\\
&\textbf{{Người duyệt	}}\\
 \\
 \\   
 &\textbf{{Phan Thị Thanh Hà	}}\\
\end{{tblr}}
\end{{minipage}}

\end{{document}}
"""
text = (
    text.replace("tiết", "Tiết")
    .replace(" .", ".")
    .replace(" ,", ",")
    .replace(" Tiết", "Tiết")
)
f = open("mylatex.tex", "w", encoding="utf-8")
f.write(text)
f.close()


def compile_latex():
    send_notification("Đang chạy")
    os.system("pdflatex mylatex.tex")


compile_latex()
subprocess.Popen(
    ["zathura", "/home/<USER>/Dropbox/LÊ DIỄM/mylatex.pdf"]
)
send_notification("Đã hoàn thành")
