import os
import shutil
import subprocess

import pandas as pd


from all_pickle import load_pickle
from god_class import (
    TextProcess,
    format_name,
    send_notification,
    setup_logging,
    trim_text,
    convert_ngay,
    yes_no,
    change_xa,
    lower_first_char_pd,
)
from playwright_class import CapTaiKhoan<PERSON>et<PERSON>oi


def not_is_da_co_so_qd(df_dkkd_tail_100, so_qd):
    return so_qd not in set(df_dkkd_tail_100["so qd"])


def append_df2_to_df1(df_orgin, df_append, csv_origin):
    list_cols = df_orgin.columns.tolist()
    df_orgin = pd.concat([df_orgin, df_append], ignore_index=True)
    df_orgin = df_orgin[list_cols]
    df_orgin.to_csv(
        f"/home/<USER>/Dropbox/hnd/csv_source/{csv_origin}.csv", index=False
    )


def sort_dkkd_and_upper(df):
    df["ngay qd"] = pd.to_datetime(df["ngay qd"], format="%d/%m/%Y", errors="coerce")

    # Bước 2: Tạo cột tạm thời để đánh dấu các giá trị rỗng
    df["is_na"] = df["ngay qd"].isna()

    # Bước 3: Sắp xếp DataFrame, đầu tiên theo cột 'is_na' (để các giá trị rỗng lên đầu), sau đó theo cột 'ngay qd'
    df.sort_values(by=["is_na", "ngay qd"], ascending=[False, True], inplace=True)

    df["ngay qd"] = df["ngay qd"].dt.strftime("%d/%m/%Y")
    # Bước 4: Xóa cột tạm thời nếu không cần thiết
    df.drop(columns=["is_na"], inplace=True)
    df["ten qt-nt"] = df["ten qt-nt"].str.upper()


def process_certificate(gcn, prefix_type, template_name, row, values):
    """
    Xử lý và tạo file giấy chứng nhận
    """
    name_file = f"{prefix_type}_{format_name(row['ten qt-nt'])}_{row['ma ho so']}"

    # Process the certificate
    gcn.format_text(values)

    # Copy tex file to archive directory
    PDF_DIR = "/home/<USER>/Dropbox/hnd/latexall/gpp_da_cap"
    dst_tex = os.path.join(PDF_DIR, name_file + ".tex")
    gcn.copy_latex_file(dst_tex)

    return dst_tex, name_file


@setup_logging("gpp_in_kq.log")
def run_main():
    os.chdir("/home/<USER>/Dropbox/hnd/csv_source")
    values = {}
    df_dkkd = pd.read_csv("dkkd.csv", dtype="str")
    df_dkkd_tail_100 = df_dkkd.tail(100)
    # df_dkkd_tail_100 = df_dkkd
    df_name = pd.read_csv("name.csv", dtype="str", index_col="ma ho so")

    isnull_soqd = df_dkkd_tail_100["so qd"].isnull()
    notnull_ngayqd = df_dkkd_tail_100["ngay qd"].notnull()
    not_vande = df_dkkd_tail_100["van de hs"] == "đạt"

    # TODO 1. LỌC RA CÁC HỒ SƠ THỎA MÃN ĐỂ CẤP GCN ĐĐKKDD (DSCC)
    values["so_qd"] = load_pickle(
        "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pickle_file/soqd_gpp.pk"
    )

    condition = isnull_soqd & notnull_ngayqd & not_vande
    if values["so_qd"] not in df_dkkd_tail_100["so qd"].values:
        list_mhs = df_dkkd_tail_100[condition]["ma ho so"].tolist()
    else:
        list_mhs = df_dkkd_tail_100[df_dkkd_tail_100["so qd"] == values["so_qd"]][
            "ma ho so"
        ].tolist()
    df_dkkd.loc[df_dkkd["ma ho so"].isin(list_mhs), "so qd"] = values["so_qd"]
    df_dsqd = df_dkkd[df_dkkd["ma ho so"].isin(list_mhs)]

    values["ngayqd"] = df_dsqd["ngay qd"].unique()[0]
    values["ngayqd"] = convert_ngay(values["ngayqd"])
    values["ngay"] = values["ngayqd"].split("/")[0]
    values["thang"] = values["ngayqd"].split("/")[1]
    values["nam"] = values["ngayqd"].split("/")[2]

    df_gpp_all = pd.read_csv("du_lieu_gpp_all.csv", dtype="str")

    if values["so_qd"] not in df_gpp_all.tail(100)["so qd"].values:
        append_df2_to_df1(df_gpp_all, df_dsqd, "du_lieu_gpp_all")

    def cho_tra():
        df_name.loc[df_name.index.isin(df_dsqd["ma ho so"].tolist()), "CHỜ TRẢ"] = "1"
        df_name.to_csv("name.csv", index_label="ma ho so")

    cho_tra()
    # TODO 3. SORT THEO ngay qd CHO DKKD.csv
    sort_dkkd_and_upper(df_dkkd)
    df_dkkd.to_csv("dkkd.csv", index=False)

    # TODO 4. COMPILE FILE PDF THEO DS ĐÃ LỌC (DSCC)
    df_dsqd = df_dsqd.fillna("")
    pdf_files_dkkd = []
    pdf_files_gpp = []
    latex_dkkd = []
    latex_gpp = []
    gcn_dkkd_template = TextProcess("gpp_in_kq_gcn")
    gcn_gpp_template = TextProcess("gpp_in_kq_gpp")
    df_dsqd["dia chi co so"] = df_dsqd["dia chi co so"].apply(lower_first_char_pd)
    df_dsqd["dia chi co so"] = df_dsqd["dia chi co so"].apply(change_xa)

    for row in df_dsqd.iterrows():
        values["so_dkkd"] = row[1]["so dkkd"]
        values["so_gpp"] = row[1]["so gpp"] + "-25"
        values["pham vi kd"] = (
            row[1]["pham vi kd"] + "."
            if "." not in row[1]["pham vi kd"]
            else row[1]["pham vi kd"]
        )
        # Kiểm tra số lần xuất hiện của từ 'bảo quản' trong phạm vi kinh doanh
        bao_quan_count = values["pham vi kd"].lower().count("bảo quản")
        dot_count = values["pham vi kd"].lower().count(".")
        if bao_quan_count >= 2 or dot_count >= 2:
            # send_notification(
            #     f"Phạm vi kinh doanh của {row[1]['ten qt-nt']} có vấn đề: {values['pham vi kd']}"
            # )
            values["pham vi kd"] = values["pham vi kd"].split(".")[0] + "."

        values["ten qt-nt"] = row[1]["ten qt-nt"].upper()
        values["diachi1"] = trim_text(row[1]["dia chi co so"])
        if 70 < len(row[1]["dia chi co so"]) < 78:
            values["diachi"] = rf"{{{values['diachi1']}}}"
            values["diadiem"] = rf"{{{values['diachi1']}}}"
        elif len(row[1]["dia chi co so"]) > 64:
            values["diachi"] = rf"{{{values['diachi1']}}}"
            values["diadiem"] = values["diachi"]
        else:
            values["diadiem"] = values["diachi1"]

        values["diachi"] = values["diachi1"]
        values["lan_cap"] = row[1]["Lần cấp"]
        values["hangpp"] = row[1]["ngay het han gpp"]
        if "/" in row[1]["ngay dkkd cu"]:
            ngay_cu, thang_cu, nam_cu = row[1]["ngay dkkd cu"].split("/")
        values["dieu"] = "Điều 48" if row[1]["loai hinh"] == "Quầy thuốc" else "Điều 47"

        if "CHINH" in row[1]["thu tuc"]:
            values["lydo"] = (
                row[1]["thu tuc"].split(" - ")[1][0:1].upper()
                + row[1]["thu tuc"].split(" - ")[1][1:].lower()
            )

            values["thutucgpp"] = (
                f"(Điều chỉnh lần {values['lan_cap']} - {values['lydo']})"
            )
            values["thutucdkkd"] = rf"{{\small {values['thutucgpp']}}}"
            values["thaythe"] = (
                rf"\parThay thế cho Giấy chứng nhận đủ điều kiện kinh doanh dược số {row[1]['so dkkd']}/ĐKKDD-PT ngày {ngay_cu} tháng {thang_cu} năm {nam_cu}"
            )
        elif "LAI" in row[1]["thu tuc"]:
            values["lydo"] = (
                row[1]["thu tuc"].split(" - ")[1][0:1].upper()
                + row[1]["thu tuc"].split(" - ")[1][1:].lower()
            )

            values["thutucgpp"] = "(Cấp lại lần 1)"
            values["thutucdkkd"] = rf"{{\small {values['thutucgpp']}}}"

            values["thaythe"] = (
                rf"\parThay thế cho Giấy chứng nhận đủ điều kiện kinh doanh dược số {row[1]['so dkkd']}/ĐKKDD-PT ngày {ngay_cu} tháng {thang_cu} năm {nam_cu}"
            )
        else:
            values["thutucgpp"] = ""
            values["thutucdkkd"] = ""
            values["hangpp"] = (
                f"{values['ngay']}/{values['thang']}/{int(values['nam']) + 3}"
            )
            values["thaythe"] = ""
        values["thoihangpp"] = (
            f"Giấy chứng nhận này có giá trị đến ngày {values['hangpp']}./."
        )
        values["nguoiptcm"] = row[1]["ten nguoi ptcm"].title()
        values["loaihinh"] = row[1]["loai hinh"]
        values["ngaycapcc"] = row[1]["ngay cap cchnd"]

        values["trinhdocm"] = row[1]["trinh do cm"]
        values["trinh do tat"] = row[1]["trinh do tat"]
        values["socchnd"] = (
            row[1]["so cchnd"] + "/CCHND-SYT-VP"
            if row[1]["so cchnd"].isdigit()
            else row[1]["so cchnd"]
        )

        values["noicapcchnd"] = row[1]["noi cap cchnd"]
        values["giandong"] = "1"
        # if 'LONG CHÂU' in row[1]["co quan chu quan"]:

        # assert 'lạnh' in row[1]["pham vi kd"], "LONG CHÂU THIẾU PHẠM VI bao quan lanh"

        if row[1]["co quan chu quan"] == "0" or row[1]["co quan chu quan"] == "":
            values[
                "thongtincs"
            ] = rf"""Tên/loại hình cơ sở kinh doanh: {{\color{{blue}}\textbf{{{values["ten qt-nt"]}}}}}



              Địa chỉ kinh doanh: {values["diachi"]}."""
            values["giandong"] = "1.1"
            values["giandonggpp"] = "1"
            values["thongtincsgpp"] = (
                rf"Cơ sở: {{\color{{blue}}\textbf{{{values['ten qt-nt']}}}}}"
            )

            values["space1"] = r"""
            \vspace{1\baselineskip}

            \setstretch{1.1}
            """
            values["space2"] = ""
        # TODO 4.1 VỚI TRƯỜNG HỢP CÓ co quan chu quan
        elif "CÔNG TY" in row[1]["co quan chu quan"].upper():
            values["space1"] = ""
            values["space2"] = ""

            diachicq = row[1]["dia chi co quan chu quan"]
            diachicq = diachicq[0:1].lower() + diachicq[1:]
            values[
                "thongtincs"
            ] = rf"""{{Tên/loại hình cơ sở kinh doanh: {{\color{{blue}}\textbf{{{row[1]["co quan chu quan"].upper()}}}}}}}


                           {{Trụ sở chính: {diachicq}.}}

                           Tên địa điểm kinh doanh: {{\color{{blue}}\textbf{{{values["ten qt-nt"]}}}}}


               Địa chỉ kinh doanh: {values["diachi"]}."""

            values[
                "thongtincsgpp"
            ] = rf"""{{Tên/loại hình cơ sở kinh doanh: {{\color{{blue}}\textbf{{{row[1]["co quan chu quan"].upper()}}}}}}}



                           {{Trụ sở chính: {diachicq}.}}

                           Tên địa điểm kinh doanh: {{\color{{blue}}\textbf{{{values["ten qt-nt"]}}}}}"""
            values["giandong"] = "1"
            values["giandonggpp"] = "1.1"

        dst_tex, name_file = process_certificate(
            gcn=gcn_dkkd_template,
            prefix_type="gcn_dkkd",
            template_name="gpp_in_kq_gcn",
            row=row[1],
            values=values,
        )
        latex_dkkd.append(dst_tex)
        pdf_files_dkkd.append(name_file)

        dst_tex, name_file = process_certificate(
            gcn=gcn_gpp_template,
            prefix_type="gcn_gpp",
            template_name="gpp_in_kq_gpp",
            row=row[1],
            values=values,
        )
        latex_gpp.append(dst_tex)
        pdf_files_gpp.append(name_file)

    path = "/home/<USER>/Dropbox/hnd/latexall/mylatex/mylatex.tex"
    send_notification("Wait! Start Compiling")
    for index, latex_file in enumerate(latex_dkkd):
        shutil.copy(latex_file, path)
        gcn_dkkd_template.compile_latex_confirm()
        gcn_dkkd_template.copy_pdf_to_kq(pdf_files_dkkd[index])

    for index, latex_file in enumerate(latex_gpp):
        shutil.copy(latex_file, path)
        gcn_gpp_template.compile_latex_confirm()
        gcn_gpp_template.copy_pdf_to_kq(pdf_files_gpp[index])

    if yes_no("Thông báo", "Có cấp tài khoản kết nối không"):
        send_notification("đợi thao tác cấp thủ công", True)
        cap = CapTaiKhoanKetNoi(values["ngayqd"])
        cap.process_all_gpp()
        send_notification("ĐÃ XONG")
    subprocess.run(
        [
            "kitty",
            "-e",
            "yazi",
            "/home/<USER>/Pcloud_ssd/Pcloud/HND_Document/pdf_documents/kq",
        ]
    )


if __name__ == "__main__":
    run_main()
