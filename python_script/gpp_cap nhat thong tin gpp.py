from god import *

os.chdir("/home/<USER>/Dropbox/hnd/csv_source")
path = "dshn_duoc.csv"


def get_excel_values(index_val, dft):
    try:
        selected_values = dft.loc[dft.index == index_val].tail(1)
        return selected_values.to_dict(orient="records")[0]
    except:
        empty_dict = dict.fromkeys(dft.columns, "")
        return empty_dict


def get_df_from_excel(path_df, index_col):
    dff = pd.read_csv(path_df, dtype=str)
    dff.set_index(index_col, inplace=True)
    dff.fillna("", inplace=True)
    return dff


def get_last_loc(df, index, col_name):
    result = df.loc[index, col_name]
    if isinstance(result, pd.Series):
        result = result.iloc[-1]
    else:
        result = df.at[index, col_name]
    return result


df = get_df_from_excel(path, "cmnd")

exclude = ["so dkkd"]

# excel_values, df = get_excel_values(index_val, path, "cmnd")
# TODO 3. SETUP CÁC NHÓM

list_ngay = [
    "ngay tot nghiep",
    "ngay sinh",
    "ngay cap cmnd",
    "ngay bat dau th",
    "ngay ket thuc th",
    "ngay han",
]

list_phone = ["so dt chu hs"]

list_upper = ["ten nhan vien", "TÊN CƠ SỞ ĐĂNG KÝ TH"]

default_values = {
    "trinh do cm": "trung cấp",
    "gioi tinh": "0",
    "noi cap cmnd": "Cục Cảnh sát Quản lý hành chính về trật tự xã hội",
}
exclude_list = [
    "TÊN CƠ SỞ ĐĂNG KÝ TH",
    "so dkkd",
    "ngay qd",
    "ngay han",
    "ngay tot nghiep",
    "ngay bat dau th",
    "ngay ket thuc th",
    "trinh do cm",
]  # danh sach này sẽ không update để đánh lại cho nhanh
multiline = ""

layout = [
    [
        sg.Text("cmnd", size=(20, 1)),
        sg.Input(default_text="", key="input_cmnd", size=(35, 1)),
    ],
    [
        sg.Text("so dkkd", size=(20, 1)),
        sg.Input(default_text="", key="input_dkkd", size=(35, 1)),
    ],
]

fields = [col for col in df.columns.tolist() if col not in exclude]
for col in fields:
    layout.append([sg.Text(col, size=(20, 1)), sg.Input("", key=col, size=(35, 1))])
layout.append([sg.Button("OK", size=(10, 3)), sg.Button("Cancel", size=(10, 3))])
window = sg.Window(
    f"NHẬP THÔNG TIN-KHÔNG CÓ ĐUÔI /ĐKKDD-VP",
    layout,
    location=(1450, 100),
    keep_on_top=True,
    finalize=True,
)
window["input_cmnd"].bind("<Return>", "_Enter")
window["input_dkkd"].bind("<Return>", "_Enter")
window.bind("<F1>", "F1")
dkth = []
while True:
    event, values = window.read()
    if event == sg.WINDOW_CLOSED or event == "Cancel":
        break
    if event == "input_cmnd" + "_Enter":
        window["input_dkkd"].set_focus()
        dkth = get_excel_values(values["input_cmnd"], df)
        for column in fields:
            if column not in exclude_list:  # TODO CÁ THỂ HOÁ
                # TODO 4.1 append các giá trị mặc định nếu nó rỗng
                if column in default_values and dkth.get(column) == "":
                    window[column].update(value=default_values[column])
                else:
                    if column not in exclude:
                        window[column].update(value=dkth.get(column))
    if event == "input_dkkd" + "_Enter":
        window["ten nhan vien"].set_focus()
        if values["input_dkkd"].startswith("000"):
            gdp = get_df_from_excel("gdp.csv", "so dkkd")
            excel_values = get_excel_values(values["input_dkkd"], gdp)
            window["TÊN CƠ SỞ ĐĂNG KÝ TH"].update(value=excel_values.get("ten cong ty"))
            window["ngay qd CẤP"].update(value=excel_values.get("ngay qd"))
            window["ngay han"].update(value=excel_values.get("ngay het han gdp"))
        else:
            dkkd = get_df_from_excel("dkkd.csv", "so dkkd")
            excel_values = get_excel_values(values["input_dkkd"], dkkd)
            window["TÊN CƠ SỞ ĐĂNG KÝ TH"].update(value=excel_values.get("ten qt-nt"))
            window["ngay qd CẤP"].update(value=excel_values.get("ngay qd"))
            window["ngay han"].update(value=excel_values.get("ngay het han gpp"))

    if event == "OK" or event == "F1":
        values["cmnd"] = values["input_cmnd"]
        values["so dkkd"] = values["input_dkkd"]
        for key in values:
            if key in list_ngay:
                values[key] = convert_ngay(values[key])
            if key in phone and values[key] != "":
                values[key] = phone_format(values[key])
            if key in upper:
                values[key] = values[key].upper()

        # TODO 6. ghi vào 3 file excel

        def add_update(df, label):
            df_temp = pd.DataFrame(values, index=[values[label]])
            df.update(df_temp)
            return df

        def add_loc(df2, label):
            df2.loc[values[label]] = values
            return df2

        if dkth["ten nhan vien"] == "":
            dfs = add_loc(df, "cmnd")
        else:
            dfs = add_update(df, "cmnd")
        dfs.to_csv(
            path,
            index_label="cmnd",
        )

        for column in fields:
            if column not in exclude_list:  # TODO CÁ THỂ HOÁ
                window[column].update(value="")
        window["input_cmnd"].update(value="")
        window["input_cmnd"].set_focus()
