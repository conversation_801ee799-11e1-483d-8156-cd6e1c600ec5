from abc import ABC, abstractmethod
import sys
import requests
from config import TelegramConfig  # Tạo file config riêng


# abstract class
class MessageCreat(ABC):
    @abstractmethod
    def get_message(self, file_path: str) -> str:
        pass


class FileSender(ABC):
    @abstractmethod
    def send_file(self, file_path: str) -> None:
        pass


class MessageSender(ABC):
    @abstractmethod
    def send_message(self, text: str) -> None:
        pass


# level1 class
class TelegramFileSender(FileSender):
    def __init__(self, token: str, chat_id: str):
        self.token = token
        self.chat_id = chat_id

    def send_file(self, file_path: str) -> None:
        url = f"https://cool-butterfly-9ded.tungson92dkh.workers.dev/bot{self.token}/sendDocument"
        files = {"document": open(file_path, "rb")}
        data = {"chat_id": self.chat_id}
        requests.post(url, files=files, data=data)


class TelegramMessageSender(MessageSender):  # phuong thuc chung
    def __init__(self, token: str, chat_id: str):
        self.token = token
        self.chat_id = chat_id

    def send_message(self, text: str) -> None:
        url = f"https://cool-butterfly-9ded.tungson92dkh.workers.dev/bot{self.token}/sendMessage"
        data = {"chat_id": self.chat_id, "text": text}
        requests.post(url, data=data)


# specific class
class QlhnCreatMessage(MessageCreat):
    def get_message(self, file_path: str) -> str:
        if "thu_hoi_chung_chi" in file_path:
            return "#thuhoichungchi"
        elif "huy_bo_giay" in file_path:
            return "#thuhoigcnddkkdd"
        elif "ban_le_thuoc_den_han" in file_path:
            return "#danhsachdenhangpp"
        return ""


# factory class
class TelegramSenderFactory:
    @staticmethod
    def create_sender(channel):
        config = TelegramConfig()
        if channel:
            return QlhnSender(
                config.get_token(channel),
                config.get_chat_id(channel),
                QlhnCreatMessage(),
                file_path,
            )
        else:
            return NormalSender(
                config.get_token("hnd"), config.get_chat_id("hnd"), file_path
            )


# final class
class NormalSender:
    def __init__(
        self, token: str, chat_id: str, file_path: str, file_sender: FileSender = None
    ):
        self.file_path = file_path
        # Dependency injection
        self.file_sender = file_sender or TelegramFileSender(token, chat_id)
        self.send()

    def send(self) -> None:
        self.file_sender.send_file(self.file_path)


class QlhnSender:
    def __init__(
        self,
        token: str,
        chat_id: str,
        message_handler: MessageCreat,
        file_path: str,
        file_sender: FileSender = None,
        message_sender: MessageSender = None,
    ):
        self.file_path = file_path
        # Dependency injection
        self.file_sender = file_sender or TelegramFileSender(token, chat_id)
        self.message_sender = message_sender or TelegramMessageSender(token, chat_id)
        self.message_handler = message_handler
        self.send()

    def send(self) -> None:
        message = self.message_handler.get_message(self.file_path)
        self.file_sender.send_file(self.file_path)
        if message:
            self.message_sender.send_message(message)


if __name__ == "__main__":
    file_path = sys.argv[1]
    print(sys.argv)
    if len(sys.argv) > 2:
        channel = sys.argv[2]
        print(channel)
    else:
        channel = None
    sender = TelegramSenderFactory.create_sender(channel)
